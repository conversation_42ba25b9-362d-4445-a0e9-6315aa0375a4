-- 设备管理系统数据库设计
-- 基于 frSimpleBoot Pro 脚手架

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 基础数据表
-- ----------------------------

-- 科室管理表
DROP TABLE IF EXISTS `equipment_department`;
CREATE TABLE `equipment_department` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `dept_code` varchar(50) NOT NULL COMMENT '科室编码',
  `dept_name` varchar(100) NOT NULL COMMENT '科室名称',
  `parent_id` varchar(32) DEFAULT '0' COMMENT '上级科室ID',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态 0-正常 1-禁用',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dept_code_tenant` (`dept_code`, `tenant_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='科室管理表';

-- 位置管理表
DROP TABLE IF EXISTS `equipment_location`;
CREATE TABLE `equipment_location` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `location_code` varchar(50) NOT NULL COMMENT '位置编码',
  `location_name` varchar(100) NOT NULL COMMENT '位置名称',
  `parent_id` varchar(32) DEFAULT '0' COMMENT '上级位置ID',
  `building` varchar(50) COMMENT '建筑物',
  `floor` varchar(20) COMMENT '楼层',
  `room` varchar(50) COMMENT '房间号',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态 0-正常 1-禁用',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_location_code_tenant` (`location_code`, `tenant_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='位置管理表';

-- 设备分类表
DROP TABLE IF EXISTS `equipment_category`;
CREATE TABLE `equipment_category` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `category_code` varchar(50) NOT NULL COMMENT '分类编码',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` varchar(32) DEFAULT '0' COMMENT '上级分类ID',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态 0-正常 1-禁用',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code_tenant` (`category_code`, `tenant_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备分类表';

-- 设备类型表
DROP TABLE IF EXISTS `equipment_type`;
CREATE TABLE `equipment_type` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `type_code` varchar(50) NOT NULL COMMENT '类型编码',
  `type_name` varchar(100) NOT NULL COMMENT '类型名称',
  `category_id` varchar(32) NOT NULL COMMENT '所属分类ID',
  `sort` int DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态 0-正常 1-禁用',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_code_tenant` (`type_code`, `tenant_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备类型表';

-- ----------------------------
-- 设备档案相关表
-- ----------------------------

-- 设备档案主表
DROP TABLE IF EXISTS `equipment_asset`;
CREATE TABLE `equipment_asset` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `equipment_code` varchar(100) NOT NULL COMMENT '设备编码',
  `equipment_name` varchar(200) NOT NULL COMMENT '设备名称',
  `category_id` varchar(32) NOT NULL COMMENT '设备分类ID',
  `type_id` varchar(32) NOT NULL COMMENT '设备类型ID',
  `model` varchar(100) COMMENT '型号',
  `manufacturer` varchar(200) COMMENT '生产商',
  `supplier` varchar(200) COMMENT '供应商',
  `purchase_price` decimal(15,2) COMMENT '采购价格',
  `department_id` varchar(32) NOT NULL COMMENT '所属科室ID',
  `location_id` varchar(32) NOT NULL COMMENT '所在位置ID',
  `equipment_images` text COMMENT '设备图片(JSON格式存储图片ID数组)',
  `scrap_years` int COMMENT '报废年限(年)',
  `equipment_status` varchar(20) DEFAULT 'NORMAL' COMMENT '设备状态 NORMAL-正常 MAINTENANCE-维护中 REPAIR-维修中 SCRAP-报废 IDLE-闲置',
  `qr_code` varchar(500) COMMENT '二维码内容',
  `specification` text COMMENT '设备规格参数',
  `warranty_period` int COMMENT '保修期(月)',
  `install_date` date COMMENT '安装日期',
  `use_date` date COMMENT '启用日期',
  `remark` text COMMENT '备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  `is_deleted` char(1) DEFAULT '0' COMMENT '删除标记 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_equipment_code_tenant` (`equipment_code`, `tenant_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_type_id` (`type_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_location_id` (`location_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_equipment_status` (`equipment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备档案主表';

-- 设备BOM清单表
DROP TABLE IF EXISTS `equipment_bom`;
CREATE TABLE `equipment_bom` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `parent_id` varchar(32) DEFAULT '0' COMMENT '上级组件ID',
  `component_name` varchar(200) NOT NULL COMMENT '组件名称',
  `component_code` varchar(100) COMMENT '组件编码',
  `specification` varchar(500) COMMENT '规格型号',
  `quantity` int DEFAULT 1 COMMENT '数量',
  `unit` varchar(20) COMMENT '单位',
  `sort` int DEFAULT 0 COMMENT '排序',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备BOM清单表';

-- ----------------------------
-- 任务调度相关表
-- ----------------------------

-- 任务类型表
DROP TABLE IF EXISTS `equipment_task_type`;
CREATE TABLE `equipment_task_type` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `type_code` varchar(50) NOT NULL COMMENT '任务类型编码',
  `type_name` varchar(100) NOT NULL COMMENT '任务类型名称',
  `description` text COMMENT '任务描述',
  `is_system` char(1) DEFAULT '0' COMMENT '是否系统内置 0-否 1-是',
  `status` char(1) DEFAULT '0' COMMENT '状态 0-启用 1-禁用',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_code_tenant` (`type_code`, `tenant_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务类型表';

-- 任务元数据表
DROP TABLE IF EXISTS `equipment_task`;
CREATE TABLE `equipment_task` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `task_code` varchar(100) NOT NULL COMMENT '任务编码',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `task_type_id` varchar(32) NOT NULL COMMENT '任务类型ID',
  `equipment_id` varchar(32) NOT NULL COMMENT '关联设备ID',
  `assigned_user_id` varchar(32) NOT NULL COMMENT '分配人员ID',
  `assigned_user_name` varchar(100) COMMENT '分配人员姓名',
  `priority` varchar(20) DEFAULT 'NORMAL' COMMENT '优先级 LOW-低 NORMAL-普通 HIGH-高 URGENT-紧急',
  `planned_start_time` datetime COMMENT '计划开始时间',
  `planned_end_time` datetime COMMENT '计划结束时间',
  `actual_start_time` datetime COMMENT '实际开始时间',
  `actual_end_time` datetime COMMENT '实际结束时间',
  `task_status` varchar(20) DEFAULT 'PENDING' COMMENT '任务状态 PENDING-待执行 PROCESSING-执行中 COMPLETED-已完成 CANCELLED-已取消 OVERDUE-已逾期',
  `content` text COMMENT '任务内容',
  `result` text COMMENT '执行结果',
  `remark` text COMMENT '备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_code_tenant` (`task_code`, `tenant_id`),
  KEY `idx_task_type_id` (`task_type_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_assigned_user_id` (`assigned_user_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务元数据表';

-- ----------------------------
-- 工单管理相关表
-- ----------------------------

-- 工单表
DROP TABLE IF EXISTS `equipment_work_order`;
CREATE TABLE `equipment_work_order` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `order_code` varchar(100) NOT NULL COMMENT '工单编码',
  `order_title` varchar(200) NOT NULL COMMENT '工单标题',
  `order_type` varchar(20) NOT NULL COMMENT '工单类型 REPAIR-维修 MAINTENANCE-保养 INSPECTION-点检 PATROL-巡检 OTHER-其他',
  `equipment_id` varchar(32) NOT NULL COMMENT '关联设备ID',
  `equipment_code` varchar(100) COMMENT '设备编码',
  `equipment_name` varchar(200) COMMENT '设备名称',
  `applicant_id` varchar(32) NOT NULL COMMENT '申请人ID',
  `applicant_name` varchar(100) COMMENT '申请人姓名',
  `department_id` varchar(32) NOT NULL COMMENT '申请科室ID',
  `assigned_user_id` varchar(32) COMMENT '处理人ID',
  `assigned_user_name` varchar(100) COMMENT '处理人姓名',
  `priority` varchar(20) DEFAULT 'NORMAL' COMMENT '优先级 LOW-低 NORMAL-普通 HIGH-高 URGENT-紧急',
  `order_status` varchar(20) DEFAULT 'PENDING' COMMENT '工单状态 PENDING-待处理 PROCESSING-处理中 COMPLETED-已完成 CANCELLED-已取消',
  `description` text COMMENT '问题描述',
  `solution` text COMMENT '解决方案',
  `images` text COMMENT '相关图片(JSON格式存储图片ID数组)',
  `apply_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `assign_time` datetime COMMENT '分配时间',
  `start_time` datetime COMMENT '开始处理时间',
  `complete_time` datetime COMMENT '完成时间',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_code_tenant` (`order_code`, `tenant_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_applicant_id` (`applicant_id`),
  KEY `idx_assigned_user_id` (`assigned_user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单表';

-- ----------------------------
-- 设备点检相关表
-- ----------------------------

-- 点检标准表
DROP TABLE IF EXISTS `equipment_inspection_standard`;
CREATE TABLE `equipment_inspection_standard` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `standard_code` varchar(100) NOT NULL COMMENT '标准编码',
  `standard_name` varchar(200) NOT NULL COMMENT '标准名称',
  `equipment_type_id` varchar(32) NOT NULL COMMENT '适用设备类型ID',
  `inspection_items` text NOT NULL COMMENT '点检项目(JSON格式)',
  `frequency` varchar(20) COMMENT '点检频率 DAILY-每日 WEEKLY-每周 MONTHLY-每月 QUARTERLY-每季度 YEARLY-每年',
  `status` char(1) DEFAULT '0' COMMENT '状态 0-启用 1-禁用',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_standard_code_tenant` (`standard_code`, `tenant_id`),
  KEY `idx_equipment_type_id` (`equipment_type_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点检标准表';

-- 点检记录表
DROP TABLE IF EXISTS `equipment_inspection_record`;
CREATE TABLE `equipment_inspection_record` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `record_code` varchar(100) NOT NULL COMMENT '记录编码',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `standard_id` varchar(32) NOT NULL COMMENT '点检标准ID',
  `inspector_id` varchar(32) NOT NULL COMMENT '点检员ID',
  `inspector_name` varchar(100) COMMENT '点检员姓名',
  `inspection_date` datetime NOT NULL COMMENT '点检时间',
  `inspection_result` varchar(20) DEFAULT 'NORMAL' COMMENT '点检结果 NORMAL-正常 ABNORMAL-异常 FAULT-故障',
  `inspection_details` text COMMENT '点检详情(JSON格式)',
  `images` text COMMENT '点检照片(JSON格式存储图片ID数组)',
  `remark` text COMMENT '备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_code_tenant` (`record_code`, `tenant_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_standard_id` (`standard_id`),
  KEY `idx_inspector_id` (`inspector_id`),
  KEY `idx_inspection_date` (`inspection_date`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点检记录表';

-- ----------------------------
-- 设备巡检相关表
-- ----------------------------

-- 巡检方案表
DROP TABLE IF EXISTS `equipment_patrol_plan`;
CREATE TABLE `equipment_patrol_plan` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `plan_code` varchar(100) NOT NULL COMMENT '方案编码',
  `plan_name` varchar(200) NOT NULL COMMENT '方案名称',
  `equipment_type_id` varchar(32) NOT NULL COMMENT '适用设备类型ID',
  `patrol_items` text NOT NULL COMMENT '巡检项目(JSON格式)',
  `frequency` varchar(20) COMMENT '巡检频率 DAILY-每日 WEEKLY-每周 MONTHLY-每月',
  `route_description` text COMMENT '巡检路线描述',
  `status` char(1) DEFAULT '0' COMMENT '状态 0-启用 1-禁用',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_plan_code_tenant` (`plan_code`, `tenant_id`),
  KEY `idx_equipment_type_id` (`equipment_type_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='巡检方案表';

-- 巡检任务表
DROP TABLE IF EXISTS `equipment_patrol_task`;
CREATE TABLE `equipment_patrol_task` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `task_code` varchar(100) NOT NULL COMMENT '任务编码',
  `plan_id` varchar(32) NOT NULL COMMENT '巡检方案ID',
  `equipment_ids` text NOT NULL COMMENT '巡检设备ID列表(JSON格式)',
  `patrol_user_id` varchar(32) NOT NULL COMMENT '巡检员ID',
  `patrol_user_name` varchar(100) COMMENT '巡检员姓名',
  `planned_date` date NOT NULL COMMENT '计划巡检日期',
  `actual_start_time` datetime COMMENT '实际开始时间',
  `actual_end_time` datetime COMMENT '实际结束时间',
  `task_status` varchar(20) DEFAULT 'PENDING' COMMENT '任务状态 PENDING-待执行 PROCESSING-执行中 COMPLETED-已完成 CANCELLED-已取消',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_code_tenant` (`task_code`, `tenant_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_patrol_user_id` (`patrol_user_id`),
  KEY `idx_planned_date` (`planned_date`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='巡检任务表';

-- 巡检记录表
DROP TABLE IF EXISTS `equipment_patrol_record`;
CREATE TABLE `equipment_patrol_record` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `task_id` varchar(32) NOT NULL COMMENT '巡检任务ID',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `patrol_time` datetime NOT NULL COMMENT '巡检时间',
  `patrol_result` varchar(20) DEFAULT 'NORMAL' COMMENT '巡检结果 NORMAL-正常 ABNORMAL-异常 FAULT-故障',
  `patrol_details` text COMMENT '巡检详情(JSON格式)',
  `images` text COMMENT '巡检照片(JSON格式存储图片ID数组)',
  `remark` text COMMENT '备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_patrol_time` (`patrol_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='巡检记录表';

-- ----------------------------
-- 设备保养相关表
-- ----------------------------

-- 保养计划表
DROP TABLE IF EXISTS `equipment_maintenance_plan`;
CREATE TABLE `equipment_maintenance_plan` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `plan_code` varchar(100) NOT NULL COMMENT '计划编码',
  `plan_name` varchar(200) NOT NULL COMMENT '计划名称',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `maintenance_type` varchar(20) NOT NULL COMMENT '保养类型 ROUTINE-例行保养 PERIODIC-定期保养 PREVENTIVE-预防保养',
  `frequency_type` varchar(20) NOT NULL COMMENT '频率类型 DAYS-按天 MONTHS-按月 HOURS-按工作小时',
  `frequency_value` int NOT NULL COMMENT '频率值',
  `maintenance_content` text NOT NULL COMMENT '保养内容',
  `responsible_user_id` varchar(32) NOT NULL COMMENT '负责人ID',
  `responsible_user_name` varchar(100) COMMENT '负责人姓名',
  `next_maintenance_date` date COMMENT '下次保养日期',
  `status` char(1) DEFAULT '0' COMMENT '状态 0-启用 1-禁用',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_plan_code_tenant` (`plan_code`, `tenant_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_responsible_user_id` (`responsible_user_id`),
  KEY `idx_next_maintenance_date` (`next_maintenance_date`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保养计划表';

-- 保养记录表
DROP TABLE IF EXISTS `equipment_maintenance_record`;
CREATE TABLE `equipment_maintenance_record` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `record_code` varchar(100) NOT NULL COMMENT '记录编码',
  `plan_id` varchar(32) NOT NULL COMMENT '保养计划ID',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `maintenance_date` datetime NOT NULL COMMENT '保养日期',
  `maintenance_user_id` varchar(32) NOT NULL COMMENT '保养人员ID',
  `maintenance_user_name` varchar(100) COMMENT '保养人员姓名',
  `maintenance_content` text NOT NULL COMMENT '保养内容',
  `maintenance_result` varchar(20) DEFAULT 'NORMAL' COMMENT '保养结果 NORMAL-正常 ABNORMAL-异常',
  `images` text COMMENT '保养照片(JSON格式存储图片ID数组)',
  `spare_parts_used` text COMMENT '使用备件(JSON格式)',
  `cost` decimal(10,2) COMMENT '保养成本',
  `remark` text COMMENT '备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_code_tenant` (`record_code`, `tenant_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_maintenance_user_id` (`maintenance_user_id`),
  KEY `idx_maintenance_date` (`maintenance_date`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保养记录表';

-- ----------------------------
-- 设备保修维修相关表
-- ----------------------------

-- 保修申报表
DROP TABLE IF EXISTS `equipment_repair_apply`;
CREATE TABLE `equipment_repair_apply` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `apply_code` varchar(100) NOT NULL COMMENT '申报编码',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `fault_description` text NOT NULL COMMENT '故障描述',
  `apply_user_id` varchar(32) NOT NULL COMMENT '申报人ID',
  `apply_user_name` varchar(100) COMMENT '申报人姓名',
  `contact_phone` varchar(20) COMMENT '联系方式',
  `department_id` varchar(32) NOT NULL COMMENT '申报科室ID',
  `fault_level` varchar(20) DEFAULT 'NORMAL' COMMENT '故障等级 LOW-轻微 NORMAL-一般 HIGH-严重 URGENT-紧急',
  `images` text COMMENT '故障照片(JSON格式存储图片ID数组)',
  `apply_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申报时间',
  `apply_status` varchar(20) DEFAULT 'PENDING' COMMENT '申报状态 PENDING-待审核 APPROVED-已审核 REJECTED-已拒绝',
  `audit_user_id` varchar(32) COMMENT '审核人ID',
  `audit_user_name` varchar(100) COMMENT '审核人姓名',
  `audit_time` datetime COMMENT '审核时间',
  `audit_remark` text COMMENT '审核备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_apply_code_tenant` (`apply_code`, `tenant_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_apply_user_id` (`apply_user_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_apply_status` (`apply_status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='保修申报表';

-- 维修任务表
DROP TABLE IF EXISTS `equipment_repair_task`;
CREATE TABLE `equipment_repair_task` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `task_code` varchar(100) NOT NULL COMMENT '任务编码',
  `apply_id` varchar(32) NOT NULL COMMENT '保修申报ID',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `repair_type` varchar(20) NOT NULL COMMENT '维修类型 INTERNAL-内部维修 EXTERNAL-外部维修 WARRANTY-保修',
  `repair_user_id` varchar(32) COMMENT '维修人员ID',
  `repair_user_name` varchar(100) COMMENT '维修人员姓名',
  `planned_start_time` datetime COMMENT '计划开始时间',
  `planned_end_time` datetime COMMENT '计划结束时间',
  `actual_start_time` datetime COMMENT '实际开始时间',
  `actual_end_time` datetime COMMENT '实际结束时间',
  `fault_cause` text COMMENT '故障原因',
  `repair_solution` text COMMENT '维修方案',
  `repair_process` text COMMENT '维修过程',
  `spare_parts_used` text COMMENT '使用备件(JSON格式)',
  `repair_cost` decimal(10,2) COMMENT '维修成本',
  `task_status` varchar(20) DEFAULT 'PENDING' COMMENT '任务状态 PENDING-待执行 PROCESSING-执行中 COMPLETED-已完成 CANCELLED-已取消',
  `images` text COMMENT '维修照片(JSON格式存储图片ID数组)',
  `remark` text COMMENT '备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_code_tenant` (`task_code`, `tenant_id`),
  KEY `idx_apply_id` (`apply_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_repair_user_id` (`repair_user_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维修任务表';

-- ----------------------------
-- 设备盘点相关表
-- ----------------------------

-- 盘点任务表
DROP TABLE IF EXISTS `equipment_inventory_task`;
CREATE TABLE `equipment_inventory_task` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `task_code` varchar(100) NOT NULL COMMENT '任务编码',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `inventory_type` varchar(20) NOT NULL COMMENT '盘点类型 FULL-全盘 DEPARTMENT-科室盘点 CATEGORY-分类盘点',
  `scope_condition` text COMMENT '盘点范围条件(JSON格式)',
  `planned_start_date` date NOT NULL COMMENT '计划开始日期',
  `planned_end_date` date NOT NULL COMMENT '计划结束日期',
  `actual_start_date` date COMMENT '实际开始日期',
  `actual_end_date` date COMMENT '实际结束日期',
  `responsible_user_id` varchar(32) NOT NULL COMMENT '负责人ID',
  `responsible_user_name` varchar(100) COMMENT '负责人姓名',
  `task_status` varchar(20) DEFAULT 'PENDING' COMMENT '任务状态 PENDING-待执行 PROCESSING-执行中 COMPLETED-已完成 CANCELLED-已取消',
  `total_count` int DEFAULT 0 COMMENT '应盘总数',
  `actual_count` int DEFAULT 0 COMMENT '实盘总数',
  `missing_count` int DEFAULT 0 COMMENT '缺失数量',
  `extra_count` int DEFAULT 0 COMMENT '多出数量',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_code_tenant` (`task_code`, `tenant_id`),
  KEY `idx_responsible_user_id` (`responsible_user_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点任务表';

-- 盘点详情表
DROP TABLE IF EXISTS `equipment_inventory_detail`;
CREATE TABLE `equipment_inventory_detail` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `task_id` varchar(32) NOT NULL COMMENT '盘点任务ID',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `equipment_code` varchar(100) COMMENT '设备编码',
  `equipment_name` varchar(200) COMMENT '设备名称',
  `department_id` varchar(32) COMMENT '归属科室ID',
  `location_id` varchar(32) COMMENT '所在位置ID',
  `inventory_status` varchar(20) DEFAULT 'PENDING' COMMENT '盘点状态 PENDING-待盘点 FOUND-已找到 MISSING-缺失 EXTRA-多出',
  `inventory_user_id` varchar(32) COMMENT '盘点人员ID',
  `inventory_user_name` varchar(100) COMMENT '盘点人员姓名',
  `inventory_time` datetime COMMENT '盘点时间',
  `remark` text COMMENT '备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_inventory_status` (`inventory_status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点详情表';

-- ----------------------------
-- 设备调拨相关表
-- ----------------------------

-- 设备调拨表
DROP TABLE IF EXISTS `equipment_transfer`;
CREATE TABLE `equipment_transfer` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `transfer_code` varchar(100) NOT NULL COMMENT '调拨编码',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `from_department_id` varchar(32) NOT NULL COMMENT '调出科室ID',
  `to_department_id` varchar(32) NOT NULL COMMENT '调入科室ID',
  `from_location_id` varchar(32) COMMENT '调出位置ID',
  `to_location_id` varchar(32) COMMENT '调入位置ID',
  `transfer_reason` text COMMENT '调拨原因',
  `apply_user_id` varchar(32) NOT NULL COMMENT '申请人ID',
  `apply_user_name` varchar(100) COMMENT '申请人姓名',
  `apply_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `approve_user_id` varchar(32) COMMENT '审批人ID',
  `approve_user_name` varchar(100) COMMENT '审批人姓名',
  `approve_time` datetime COMMENT '审批时间',
  `transfer_status` varchar(20) DEFAULT 'PENDING' COMMENT '调拨状态 PENDING-待审批 APPROVED-已审批 REJECTED-已拒绝 COMPLETED-已完成',
  `actual_transfer_time` datetime COMMENT '实际调拨时间',
  `remark` text COMMENT '备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transfer_code_tenant` (`transfer_code`, `tenant_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_from_department_id` (`from_department_id`),
  KEY `idx_to_department_id` (`to_department_id`),
  KEY `idx_apply_user_id` (`apply_user_id`),
  KEY `idx_transfer_status` (`transfer_status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备调拨表';

-- ----------------------------
-- 合同管理相关表
-- ----------------------------

-- 设备合同表
DROP TABLE IF EXISTS `equipment_contract`;
CREATE TABLE `equipment_contract` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `contract_code` varchar(100) NOT NULL COMMENT '合同编号',
  `contract_name` varchar(200) NOT NULL COMMENT '合同名称',
  `purchase_method` varchar(50) COMMENT '采购方式',
  `supplier` varchar(200) NOT NULL COMMENT '供货商',
  `sign_date` date COMMENT '签署时间',
  `contract_amount` decimal(15,2) COMMENT '合同金额',
  `payment_method` varchar(100) COMMENT '付款方式',
  `payment_status` varchar(20) DEFAULT 'UNPAID' COMMENT '付款状态 UNPAID-未付款 PARTIAL-部分付款 PAID-已付款',
  `contract_files` text COMMENT '合同文件(JSON格式存储文件ID数组)',
  `start_date` date COMMENT '合同开始日期',
  `end_date` date COMMENT '合同结束日期',
  `status` char(1) DEFAULT '0' COMMENT '状态 0-正常 1-终止',
  `remark` text COMMENT '备注',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_code_tenant` (`contract_code`, `tenant_id`),
  KEY `idx_supplier` (`supplier`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备合同表';

-- 合同设备关联表
DROP TABLE IF EXISTS `equipment_contract_equipment`;
CREATE TABLE `equipment_contract_equipment` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `contract_id` varchar(32) NOT NULL COMMENT '合同ID',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_equipment` (`contract_id`, `equipment_id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同设备关联表';

-- ----------------------------
-- 设备自定义字段管理表
-- ----------------------------

-- 自定义字段配置表
DROP TABLE IF EXISTS `equipment_custom_field`;
CREATE TABLE `equipment_custom_field` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `field_code` varchar(50) NOT NULL COMMENT '字段编码',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_type` varchar(20) NOT NULL COMMENT '字段类型：text-文本,number-数字,date-日期,datetime-日期时间,select-选择,checkbox-复选,file-文件',
  `category_id` varchar(32) COMMENT '设备分类ID，为空则适用于所有分类',
  `type_id` varchar(32) COMMENT '设备类型ID，为空则适用于所有类型',
  `default_value` varchar(500) COMMENT '默认值',
  `options` text COMMENT '选项值（JSON格式，用于select、checkbox类型）',
  `validation_rules` text COMMENT '验证规则（JSON格式）',
  `is_required` char(1) DEFAULT '0' COMMENT '是否必填：0-否，1-是',
  `is_enabled` char(1) DEFAULT '0' COMMENT '是否启用：0-否，1-是',
  `display_order` int DEFAULT 0 COMMENT '显示顺序',
  `placeholder` varchar(200) COMMENT '占位符文本',
  `help_text` varchar(500) COMMENT '帮助文本',
  `field_group` varchar(50) COMMENT '字段分组',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_field_code_tenant` (`field_code`, `tenant_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_type_id` (`type_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_enabled` (`is_enabled`),
  KEY `idx_display_order` (`display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备自定义字段配置表';

-- 设备自定义字段值表
DROP TABLE IF EXISTS `equipment_asset_field_value`;
CREATE TABLE `equipment_asset_field_value` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `asset_id` varchar(32) NOT NULL COMMENT '设备ID',
  `field_id` varchar(32) NOT NULL COMMENT '字段ID',
  `field_value` text COMMENT '字段值',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_asset_field` (`asset_id`, `field_id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备自定义字段值表';

-- 设备属性模板表
DROP TABLE IF EXISTS `equipment_attribute_template`;
CREATE TABLE `equipment_attribute_template` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_desc` varchar(500) COMMENT '模板描述',
  `category_id` varchar(32) COMMENT '适用设备分类ID',
  `type_id` varchar(32) COMMENT '适用设备类型ID',
  `parent_id` varchar(32) COMMENT '父模板ID（用于模板继承）',
  `template_version` varchar(20) DEFAULT '1.0' COMMENT '模板版本',
  `is_system` char(1) DEFAULT '0' COMMENT '是否系统模板：0-否，1-是',
  `is_enabled` char(1) DEFAULT '1' COMMENT '是否启用：0-否，1-是',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `template_config` text COMMENT '模板配置（JSON格式）',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code_tenant` (`template_code`, `tenant_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_type_id` (`type_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_enabled` (`is_enabled`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备属性模板表';

-- 模板字段关联表
DROP TABLE IF EXISTS `equipment_template_field`;
CREATE TABLE `equipment_template_field` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `template_id` varchar(32) NOT NULL COMMENT '模板ID',
  `field_id` varchar(32) NOT NULL COMMENT '字段ID',
  `field_config` text COMMENT '字段在模板中的配置（JSON格式）',
  `display_order` int DEFAULT 0 COMMENT '显示顺序',
  `is_required` char(1) DEFAULT '0' COMMENT '在此模板中是否必填：0-否，1-是',
  `default_value` varchar(500) COMMENT '在此模板中的默认值',
  `field_group` varchar(50) COMMENT '在此模板中的字段分组',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_field` (`template_id`, `field_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_display_order` (`display_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板字段关联表';

-- 设备应用模板记录表
DROP TABLE IF EXISTS `equipment_asset_template`;
CREATE TABLE `equipment_asset_template` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `asset_id` varchar(32) NOT NULL COMMENT '设备ID',
  `template_id` varchar(32) NOT NULL COMMENT '模板ID',
  `template_version` varchar(20) COMMENT '应用的模板版本',
  `apply_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '应用时间',
  `apply_mode` varchar(20) DEFAULT 'full' COMMENT '应用模式：full-全量，increment-增量',
  `apply_result` text COMMENT '应用结果（JSON格式）',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_apply_date` (`apply_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备应用模板记录表';

-- ----------------------------
-- 设备标签管理表
-- ----------------------------

-- 标签分类表
DROP TABLE IF EXISTS `equipment_tag_category`;
CREATE TABLE `equipment_tag_category` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `category_code` varchar(50) NOT NULL COMMENT '分类编码',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_desc` varchar(500) COMMENT '分类描述',
  `parent_id` varchar(32) COMMENT '上级分类ID',
  `category_color` varchar(20) DEFAULT '#1890ff' COMMENT '分类颜色',
  `category_icon` varchar(50) COMMENT '分类图标',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `is_enabled` char(1) DEFAULT '1' COMMENT '是否启用：0-否，1-是',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code_tenant` (`category_code`, `tenant_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_enabled` (`is_enabled`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签分类表';

-- 设备标签表
DROP TABLE IF EXISTS `equipment_tag`;
CREATE TABLE `equipment_tag` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `tag_code` varchar(50) NOT NULL COMMENT '标签编码',
  `tag_name` varchar(100) NOT NULL COMMENT '标签名称',
  `tag_desc` varchar(500) COMMENT '标签描述',
  `category_id` varchar(32) COMMENT '标签分类ID',
  `tag_color` varchar(20) DEFAULT '#1890ff' COMMENT '标签颜色',
  `tag_icon` varchar(50) COMMENT '标签图标',
  `tag_type` varchar(20) DEFAULT 'custom' COMMENT '标签类型：system-系统，custom-自定义',
  `is_multiple` char(1) DEFAULT '1' COMMENT '是否允许多选：0-否，1-是',
  `is_required` char(1) DEFAULT '0' COMMENT '是否必选：0-否，1-是',
  `is_enabled` char(1) DEFAULT '1' COMMENT '是否启用：0-否，1-是',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `tag_config` text COMMENT '标签配置（JSON格式）',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_code_tenant` (`tag_code`, `tenant_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_enabled` (`is_enabled`),
  KEY `idx_tag_type` (`tag_type`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备标签表';

-- 设备标签关联表
DROP TABLE IF EXISTS `equipment_asset_tag`;
CREATE TABLE `equipment_asset_tag` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `asset_id` varchar(32) NOT NULL COMMENT '设备ID',
  `tag_id` varchar(32) NOT NULL COMMENT '标签ID',
  `tag_value` varchar(500) COMMENT '标签值（可选）',
  `tag_order` int DEFAULT 0 COMMENT '标签排序',
  `is_primary` char(1) DEFAULT '0' COMMENT '是否主要标签：0-否，1-是',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_asset_tag` (`asset_id`, `tag_id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_is_primary` (`is_primary`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备标签关联表';

-- 标签组合表（预设标签组合）
DROP TABLE IF EXISTS `equipment_tag_combination`;
CREATE TABLE `equipment_tag_combination` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `combination_code` varchar(50) NOT NULL COMMENT '组合编码',
  `combination_name` varchar(100) NOT NULL COMMENT '组合名称',
  `combination_desc` varchar(500) COMMENT '组合描述',
  `tag_ids` text NOT NULL COMMENT '标签ID列表（JSON格式）',
  `category_id` varchar(32) COMMENT '适用设备分类ID',
  `type_id` varchar(32) COMMENT '适用设备类型ID',
  `is_enabled` char(1) DEFAULT '1' COMMENT '是否启用：0-否，1-是',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_combination_code_tenant` (`combination_code`, `tenant_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_type_id` (`type_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签组合表';

-- 设备规格参数组表
DROP TABLE IF EXISTS `equipment_spec_group`;
CREATE TABLE `equipment_spec_group` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `group_code` varchar(50) NOT NULL COMMENT '分组编码',
  `group_name` varchar(100) NOT NULL COMMENT '分组名称',
  `group_desc` varchar(500) COMMENT '分组描述',
  `parent_id` varchar(32) DEFAULT '0' COMMENT '上级分组ID',
  `category_id` varchar(32) COMMENT '适用设备分类ID',
  `type_id` varchar(32) COMMENT '适用设备类型ID',
  `is_system` char(1) DEFAULT '0' COMMENT '是否系统分组：0-否，1-是',
  `is_enabled` char(1) DEFAULT '1' COMMENT '是否启用：0-否，1-是',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_code_tenant` (`group_code`, `tenant_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_type_id` (`type_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备规格参数组表';

-- 设备规格参数定义表
DROP TABLE IF EXISTS `equipment_spec_param`;
CREATE TABLE `equipment_spec_param` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `param_code` varchar(50) NOT NULL COMMENT '参数编码',
  `param_name` varchar(100) NOT NULL COMMENT '参数名称',
  `param_desc` varchar(500) COMMENT '参数描述',
  `group_id` varchar(32) NOT NULL COMMENT '参数分组ID',
  `data_type` varchar(20) DEFAULT 'string' COMMENT '数据类型：string-字符串，number-数字，boolean-布尔，date-日期，enum-枚举',
  `unit` varchar(20) COMMENT '单位',
  `min_value` decimal(18,6) COMMENT '最小值',
  `max_value` decimal(18,6) COMMENT '最大值',
  `default_value` varchar(500) COMMENT '默认值',
  `enum_values` text COMMENT '枚举值（JSON格式）',
  `is_required` char(1) DEFAULT '0' COMMENT '是否必填：0-否，1-是',
  `is_searchable` char(1) DEFAULT '1' COMMENT '是否可搜索：0-否，1-是',
  `is_comparable` char(1) DEFAULT '1' COMMENT '是否可比较：0-否，1-是',
  `is_enabled` char(1) DEFAULT '1' COMMENT '是否启用：0-否，1-是',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `validation_rule` text COMMENT '验证规则（JSON格式）',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_param_code_tenant` (`param_code`, `tenant_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_enabled` (`is_enabled`),
  KEY `idx_searchable` (`is_searchable`),
  KEY `idx_comparable` (`is_comparable`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备规格参数定义表';

-- 设备规格参数值表
DROP TABLE IF EXISTS `equipment_asset_spec`;
CREATE TABLE `equipment_asset_spec` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `asset_id` varchar(32) NOT NULL COMMENT '设备ID',
  `param_id` varchar(32) NOT NULL COMMENT '参数ID',
  `param_value` text COMMENT '参数值',
  `numeric_value` decimal(18,6) COMMENT '数值型参数值（用于比较和搜索）',
  `text_value` varchar(500) COMMENT '文本型参数值（用于搜索）',
  `version` int DEFAULT 1 COMMENT '版本号',
  `is_verified` char(1) DEFAULT '0' COMMENT '是否已验证：0-否，1-是',
  `verified_by` varchar(32) COMMENT '验证人',
  `verified_date` datetime COMMENT '验证时间',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_asset_param` (`asset_id`, `param_id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_param_id` (`param_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_numeric_value` (`numeric_value`),
  KEY `idx_text_value` (`text_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备规格参数值表';

-- 设备规格参数历史表
DROP TABLE IF EXISTS `equipment_asset_spec_history`;
CREATE TABLE `equipment_asset_spec_history` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `asset_id` varchar(32) NOT NULL COMMENT '设备ID',
  `param_id` varchar(32) NOT NULL COMMENT '参数ID',
  `param_value` text COMMENT '参数值',
  `numeric_value` decimal(18,6) COMMENT '数值型参数值',
  `text_value` varchar(500) COMMENT '文本型参数值',
  `version` int NOT NULL COMMENT '版本号',
  `change_type` varchar(20) DEFAULT 'UPDATE' COMMENT '变更类型：CREATE-创建，UPDATE-更新，DELETE-删除',
  `change_reason` varchar(500) COMMENT '变更原因',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_param_id` (`param_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_version` (`version`),
  KEY `idx_change_type` (`change_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备规格参数历史表';

-- 设备规格参数模板表
DROP TABLE IF EXISTS `equipment_spec_template`;
CREATE TABLE `equipment_spec_template` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_desc` varchar(500) COMMENT '模板描述',
  `category_id` varchar(32) COMMENT '适用设备分类ID',
  `type_id` varchar(32) COMMENT '适用设备类型ID',
  `template_data` text COMMENT '模板数据（JSON格式）',
  `is_enabled` char(1) DEFAULT '1' COMMENT '是否启用：0-否，1-是',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) COMMENT '创建人',
  `updater` varchar(32) COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code_tenant` (`template_code`, `tenant_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_type_id` (`type_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备规格参数模板表';

-- 设备档案版本表
DROP TABLE IF EXISTS `equipment_asset_version`;
CREATE TABLE `equipment_asset_version` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `asset_id` varchar(32) NOT NULL COMMENT '设备ID',
  `version_number` varchar(20) NOT NULL COMMENT '版本号',
  `version_name` varchar(100) COMMENT '版本名称',
  `version_desc` varchar(500) COMMENT '版本描述',
  `change_type` varchar(20) DEFAULT 'UPDATE' COMMENT '变更类型：CREATE-创建，UPDATE-更新，DELETE-删除',
  `change_summary` varchar(500) COMMENT '变更摘要',
  `change_detail` text COMMENT '变更详情（JSON格式）',
  `asset_data` longtext COMMENT '设备档案数据快照（JSON格式）',
  `is_current` char(1) DEFAULT '0' COMMENT '是否当前版本：0-否，1-是',
  `is_approved` char(1) DEFAULT '0' COMMENT '是否已审批：0-否，1-是',
  `approval_status` varchar(20) DEFAULT 'PENDING' COMMENT '审批状态：PENDING-待审批，APPROVED-已通过，REJECTED-已拒绝',
  `approver` varchar(32) COMMENT '审批人',
  `approval_date` datetime COMMENT '审批时间',
  `approval_comment` varchar(500) COMMENT '审批意见',
  `is_archived` char(1) DEFAULT '0' COMMENT '是否已归档：0-否，1-是',
  `archived_date` datetime COMMENT '归档时间',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_asset_version` (`asset_id`, `version_number`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_version_number` (`version_number`),
  KEY `idx_is_current` (`is_current`),
  KEY `idx_approval_status` (`approval_status`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_create_date` (`create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备档案版本表';

-- 设备档案变更记录表
DROP TABLE IF EXISTS `equipment_asset_change_log`;
CREATE TABLE `equipment_asset_change_log` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `asset_id` varchar(32) NOT NULL COMMENT '设备ID',
  `version_id` varchar(32) COMMENT '版本ID',
  `field_name` varchar(100) NOT NULL COMMENT '字段名称',
  `field_label` varchar(100) COMMENT '字段标签',
  `old_value` text COMMENT '原值',
  `new_value` text COMMENT '新值',
  `change_type` varchar(20) DEFAULT 'UPDATE' COMMENT '变更类型：ADD-新增，UPDATE-修改，DELETE-删除',
  `data_type` varchar(20) DEFAULT 'string' COMMENT '数据类型',
  `is_sensitive` char(1) DEFAULT '0' COMMENT '是否敏感字段：0-否，1-是',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_version_id` (`version_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_field_name` (`field_name`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_create_date` (`create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备档案变更记录表';

-- 设备档案审批流程表
DROP TABLE IF EXISTS `equipment_asset_approval`;
CREATE TABLE `equipment_asset_approval` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `asset_id` varchar(32) NOT NULL COMMENT '设备ID',
  `version_id` varchar(32) NOT NULL COMMENT '版本ID',
  `approval_type` varchar(20) DEFAULT 'VERSION' COMMENT '审批类型：VERSION-版本审批，ROLLBACK-回滚审批',
  `approval_level` int DEFAULT 1 COMMENT '审批级别',
  `approver` varchar(32) NOT NULL COMMENT '审批人',
  `approver_name` varchar(100) COMMENT '审批人姓名',
  `approval_status` varchar(20) DEFAULT 'PENDING' COMMENT '审批状态：PENDING-待审批，APPROVED-已通过，REJECTED-已拒绝',
  `approval_comment` varchar(500) COMMENT '审批意见',
  `approval_date` datetime COMMENT '审批时间',
  `is_final` char(1) DEFAULT '0' COMMENT '是否最终审批：0-否，1-是',
  `next_approver` varchar(32) COMMENT '下一审批人',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_asset_id` (`asset_id`),
  KEY `idx_version_id` (`version_id`),
  KEY `idx_approver` (`approver`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_approval_status` (`approval_status`),
  KEY `idx_approval_type` (`approval_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备档案审批流程表';

-- 设备档案版本标签表
DROP TABLE IF EXISTS `equipment_asset_version_tag`;
CREATE TABLE `equipment_asset_version_tag` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `version_id` varchar(32) NOT NULL COMMENT '版本ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `tag_value` varchar(200) COMMENT '标签值',
  `tag_color` varchar(20) DEFAULT '#1890ff' COMMENT '标签颜色',
  `tag_type` varchar(20) DEFAULT 'custom' COMMENT '标签类型：system-系统，custom-自定义',
  `sort_order` int DEFAULT 0 COMMENT '排序顺序',
  `tenant_id` varchar(32) NOT NULL COMMENT '租户ID',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` varchar(32) COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_version_tag` (`version_id`, `tag_name`),
  KEY `idx_version_id` (`version_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_tag_name` (`tag_name`),
  KEY `idx_tag_type` (`tag_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备档案版本标签表';

-- ----------------------------
-- 初始化数据
-- ----------------------------

-- 初始化任务类型数据
INSERT INTO `equipment_task_type` (`id`, `type_code`, `type_name`, `description`, `is_system`, `status`, `tenant_id`, `creator`) VALUES
('1', 'INSPECTION', '设备点检', '定期对设备进行检查和维护', '1', '0', '1', 'system'),
('2', 'PATROL', '设备巡检', '按计划对设备进行巡回检查', '1', '0', '1', 'system'),
('3', 'MAINTENANCE', '设备保养', '定期对设备进行保养维护', '1', '0', '1', 'system'),
('4', 'REPAIR', '设备维修', '设备故障维修任务', '1', '0', '1', 'system'),
('5', 'INVENTORY', '设备盘点', '定期对设备进行盘点清查', '1', '0', '1', 'system');

-- 初始化设备分类数据
INSERT INTO `equipment_category` (`id`, `category_code`, `category_name`, `parent_id`, `sort`, `status`, `tenant_id`, `creator`) VALUES
('1', 'MEDICAL', '医疗设备', '0', 1, '0', '1', 'system'),
('2', 'IT', 'IT设备', '0', 2, '0', '1', 'system'),
('3', 'OFFICE', '办公设备', '0', 3, '0', '1', 'system'),
('4', 'SECURITY', '安防设备', '0', 4, '0', '1', 'system'),
('5', 'HVAC', '暖通设备', '0', 5, '0', '1', 'system');

SET FOREIGN_KEY_CHECKS = 1;