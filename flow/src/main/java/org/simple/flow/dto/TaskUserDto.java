package org.simple.flow.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class TaskUserDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String flowTitle;
    private String processName;
    private String startUser;
    private String currentNodes;
    private String currentAssignees;
    private Date startTime;
    private Date endTime;
    private Integer version;
    private String taskId;
    private Long duration;
    private String processInstanceId;
    private String status;
    private String assignees; //处理人
    private String delegate;  //委派人
    private String formUrl;
    private String formType;
    private String viewType;
    private String viewPage;
    private String business; //业务ID
}
