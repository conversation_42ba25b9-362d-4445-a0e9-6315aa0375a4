package org.simple.flow.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class InstanceDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 流程实例ID
     */
    private String processInstanceId;
    /**
     * 流程编号
     */
    private String processKey;
    /**
     * 流程版本号
     */
    private Integer version;
    /**
     * 流程名称
     */
    private String processName;
    /**
     * 当前节点
     */
    private String currentNodes;
    /**
     * 当前处理人
     */
    private String currentAssignees;
    /**
     * 流程标题
     */
    private String flowTitle;
    /**
     * 流程状态
     */
    private String status;
    /**
     * 发起时间
     */
    private Date startTime;
    /**
     * 废除原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String userId;

    /**
     * 处理人；委托/转办/认领
     */
    private String assignees;

    /**
     * 详情页面类型
     */
    private String viewType;
    /**
     * 详情页面路由/模板
     */
    private String viewPage;
    /**
     * 业务主键ID
     * */
    private String business;
}
