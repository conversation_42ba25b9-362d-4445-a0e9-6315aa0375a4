package org.simple.flow.handler;

import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

import java.util.LinkedHashSet;
import java.util.Set;

@Component("multiInstanceHandler")
public class MultiInstanceHandler {

    public Set<String> getUserIds(DelegateExecution execution){
        Set<String> candidateUserIds = new LinkedHashSet<>();
        return candidateUserIds;
    }
}
