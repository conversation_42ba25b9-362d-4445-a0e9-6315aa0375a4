package org.simple.flow.builder;

import org.simple.flow.dto.FlowPage;

public class FlowPageBuilder {

    private final FlowPage flowPage = new FlowPage();

    private FlowPageBuilder() {
    }

    public static FlowPageBuilder builder() {
        return new FlowPageBuilder();
    }

    public FlowPageBuilder size(Integer size) {
        this.flowPage.setSize(size);
        return this;
    }

    public FlowPageBuilder current(Integer current) {
        this.flowPage.setCurrent(current);
        return this;
    }

    public FlowPageBuilder total(Long total) {
        this.flowPage.setTotal(total);
        return this;
    }

    public FlowPageBuilder records(Object records) {
        this.flowPage.setRecords(records);
        return this;
    }

    public FlowPage build() {
        return this.flowPage;
    }

}
