package org.simple.flow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.simple.base.dto.mybatis.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang.StringUtils;
import org.simple.base.vo.FrResult;
import org.simple.flow.entity.Monitor;
import org.simple.flow.service.MonitorService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * @Copyright: frSimple
 * @Date: 2023-06-28 21:04:22
 * @Author: frSimple
 */

@RestController
@RequestMapping("/flow/monitor")
@Tag(name ="监听器管理")
public class MonitorController {

    @Resource
    private MonitorService monitorService;


    @GetMapping("list")
    @Operation(summary = "查询监听器配置表")
    @SaCheckPermission("wf:mon:query")
    public FrResult<?> list(Page<Monitor> page, Monitor monitor) {
        if(StringUtils.isNotEmpty(monitor.getName())){
            String name = monitor.getName();
            monitor.setName(null);
            return FrResult.success(monitorService.page(page, Wrappers.query(monitor)
                    .like("name",name).orderByDesc("create_time")));
        }else{
            return FrResult.success(monitorService.page(page, Wrappers.query(monitor)
                    .orderByDesc("create_time")));
        }
    }

    @PostMapping("add")
    @Operation(summary ="新增监听器配置表")
    @SaCheckPermission("wf:mon:add")
    public FrResult<?> add(@RequestBody Monitor monitor) {
        return FrResult.success(monitorService.save(monitor));
    }

    @PostMapping("edit")
    @Operation(summary ="修改监听器配置表")
    @SaCheckPermission("wf:mon:edit")
    public FrResult<?> edit(@RequestBody Monitor monitor) {
        return FrResult.success(monitorService.updateById(monitor));
    }

    @DeleteMapping("del/{id}")
    @Operation(summary ="删除监听器配置表")
    @SaCheckPermission("wf:mon:del")
    public FrResult<?> del(@PathVariable("id") String id) {
        return FrResult.success(monitorService.removeById(id));
    }
}