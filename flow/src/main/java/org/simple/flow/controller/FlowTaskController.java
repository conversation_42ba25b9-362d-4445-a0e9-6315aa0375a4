package org.simple.flow.controller;

import com.alibaba.fastjson2.JSONArray;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.util.AuthUtil;
import org.simple.base.vo.FrResult;
import org.simple.flow.dto.FlowPage;
import org.simple.flow.dto.TaskSubmitDto;
import org.simple.flow.param.TaskParam;
import org.simple.flow.service.FlowTaskService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/flow/task")
@Tag(name = "流程任务管理")
public class FlowTaskController {

    @Resource
    private FlowTaskService flowTaskService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @PostMapping("/commitFlow")
    @Operation(summary = "提交任务")
    public FrResult<?> commitFlow(@RequestBody TaskSubmitDto taskSubmitDto) {
        taskSubmitDto.setUserId(AuthUtil.getUserId());
        try {
            flowTaskService.submitTask(taskSubmitDto);
            return FrResult.success();
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/log/{processInstanceId}")
    @Operation(summary = "查询流转日志")
    public FrResult<FlowPage> taskLog(@PathVariable("processInstanceId") String processInstanceId) {
        try {
            return FrResult.success(flowTaskService.taskLogs(processInstanceId));
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/todoList")
    @Operation(summary = "查询我的待办任务")
    public FrResult<FlowPage> todoList(FlowPage flowPage, TaskParam taskParam) {
        taskParam.setUserId(AuthUtil.getUserId());
        taskParam.setUserRoles(AuthUtil.getRoles());
        try {
            return FrResult.success(flowTaskService.currentUserWaitTask(flowPage, taskParam));
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/todoTaskCount")
    @Operation(summary = "查询待办任务统计")
    public FrResult<?> todoTaskCount() {
        try {
            return FrResult.success(flowTaskService.taskCount());
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/haveList")
    @Operation(summary = "查询我的已办任务")
    public FrResult<FlowPage> haveList(FlowPage flowPage, TaskParam taskParam) {
        taskParam.setUserId(AuthUtil.getUserId());
        try {
            return FrResult.success(flowTaskService.currentUserHaveTask(flowPage, taskParam));
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/requestList")
    @Operation(summary = "查询我发起的流程")
    public FrResult<FlowPage> requestList(FlowPage flowPage, TaskParam taskParam) {
        taskParam.setUserId(AuthUtil.getUserId());
        try {
            return FrResult.success(flowTaskService.currentUserRequestTask(flowPage, taskParam));
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/allTask")
    @Operation(summary = "查询所有任务")
    public FrResult<FlowPage> allTask(FlowPage flowPage, TaskParam taskParam) {
        try {
            return FrResult.success(flowTaskService.
                    userTasks(flowPage, taskParam));
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @PostMapping("/transfer")
    @Operation(summary = "转办任务")
    public FrResult<FlowPage> transfer(@RequestBody TaskParam taskParam) {
        try {
            taskParam.setUserId(AuthUtil.getUserId());
            taskParam.setUserName(AuthUtil.getUser().getNickName());
            flowTaskService.transfer(taskParam);
            return FrResult.success();
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @PostMapping("/delegate")
    @Operation(summary = "委托任务")
    public FrResult<FlowPage> delegate(@RequestBody TaskParam taskParam) {
        try {
            taskParam.setUserId(AuthUtil.getUserId());
            taskParam.setUserName(AuthUtil.getUser().getNickName());
            flowTaskService.delegate(taskParam);
            return FrResult.success();
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/nextAction")
    @Operation(summary = "提交任务")
    public FrResult<JSONArray> nextAction(TaskParam param) {
        try {
            return FrResult.success(flowTaskService.nextAction(param));
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }
}
