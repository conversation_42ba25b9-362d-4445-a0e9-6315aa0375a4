package org.simple.flow.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.RandomUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.simple.base.util.AuthUtil;
import org.simple.base.vo.FrResult;
import org.simple.flow.builder.StartParamBuilder;
import org.simple.flow.dto.FlowPage;
import org.simple.flow.dto.InstanceDto;
import org.simple.flow.dto.ProcessDto;
import org.simple.flow.param.StartParam;
import org.simple.flow.service.FlowInstanceService;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/flow/instance")
@Tag(name = "流程实例管理")
public class FlowInstanceController {

    @Resource
    private FlowInstanceService flowInstanceService;


    @PostMapping("/start")
    @Operation(summary = "发起流程")
    public FrResult<?> start(@RequestBody StartParam params) {
        Map<String, Object> variable = new HashMap<>();
        StartParam startParam = StartParamBuilder.builder()
                .processKey(params.getProcessKey())
                .businessKey(params.getProcessKey() + "_" + RandomUtil.randomString(12))
                .flowTitle(params.getFlowTitle())
                .variable(variable)
                .userId(AuthUtil.getUserId())
                .formId(params.getFormId())
                .formData(params.getFormData())
                .build();
        try {
            flowInstanceService.startFlow(startParam);
            return FrResult.success();
        } catch (Exception ex) {
            ex.printStackTrace();
            if (ex instanceof FlowableObjectNotFoundException) {
                return FrResult.failed("流程未定义或未发布");
            } else {
                return FrResult.failed(ex.getMessage());
            }
        }
    }

    @PostMapping("/startWithSubmit")
    @Operation(summary = "发起并提交流程")
    public FrResult<?> startWithSubmit(@RequestBody StartParam params) {
        Map<String, Object> variable = new HashMap<>();
        StartParam startParam = StartParamBuilder.builder()
                .processKey(params.getProcessKey())
                .businessKey(RandomUtil.randomString(12))
                .flowTitle(params.getFlowTitle())
                .variable(variable)
                .userId(AuthUtil.getUserId())
                .formId(params.getFormId())
                .formData(params.getFormData())
                .build();
        try {
            flowInstanceService.startWithSubmitFlow(startParam);
            return FrResult.success();
        } catch (Exception ex) {
            ex.printStackTrace();
            if (ex instanceof FlowableObjectNotFoundException) {
                return FrResult.failed("流程未定义或未发布");
            } else {
                return FrResult.failed(ex.getMessage());
            }
        }
    }

    @GetMapping("/list")
    @Operation(summary = "查询流程所有实例")
    @SaCheckPermission("wf:instance:query")
    public FrResult<FlowPage> list(FlowPage page, InstanceDto instanceDto) {
        return FrResult.success(flowInstanceService.list(page, instanceDto));
    }

    @GetMapping("/listCount")
    @Operation(summary = "查询流程实例统计数据")
    @SaCheckPermission("wf:instance:query")
    public FrResult<FlowPage> listCount(FlowPage page, ProcessDto processDto) {
        return FrResult.success(flowInstanceService.listCount(page, processDto));
    }

    @GetMapping("/stop/{processInstanceId}")
    @Operation(summary = "暂停流程")
    public FrResult<?> stop(@PathVariable("processInstanceId") String processInstanceId) {
        InstanceDto dto = new InstanceDto();
        dto.setProcessInstanceId(processInstanceId);
        try {
            flowInstanceService.stopFlow(dto);
            return FrResult.success();
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/active/{processInstanceId}")
    @Operation(summary = "恢复流程")
    public FrResult<?> active(@PathVariable("processInstanceId") String processInstanceId) {
        InstanceDto dto = new InstanceDto();
        dto.setProcessInstanceId(processInstanceId);
        try {
            flowInstanceService.activeFlow(dto);
            return FrResult.success();
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/abrogate")
    @Operation(summary = "作废流程")
    public FrResult<?> abrogate(InstanceDto dto) {
        dto.setUserId(AuthUtil.getUserId());
        try {
            flowInstanceService.abrogateFlow(dto);
            return FrResult.success();
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/delete/{processInstanceId}")
    @Operation(summary = "删除流程")
    public FrResult<?> delete(@PathVariable("processInstanceId") String processInstanceId) {
        InstanceDto dto = new InstanceDto();
        dto.setProcessInstanceId(processInstanceId);
        try {
            flowInstanceService.deleteFlow(dto);
            return FrResult.success();
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("/instanceDesign/{processInstanceId}")
    @Operation(summary = "获取实例流程图")
    public FrResult<?> instanceDesign(@PathVariable("processInstanceId") String processInstanceId) {
        try {
            return FrResult.success(flowInstanceService.instanceDesign(processInstanceId));
        } catch (Exception ex) {
            ex.printStackTrace();
            return FrResult.failed(ex.getMessage());
        }
    }
}
