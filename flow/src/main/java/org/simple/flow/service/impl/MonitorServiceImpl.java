package org.simple.flow.service.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.simple.flow.service.MonitorService;
import org.simple.flow.mapper.MonitorMapper;
import org.simple.flow.entity.Monitor;

/**
 * @Copyright: frSimple
 * @Date: 2023-06-28 21:04:22
 * @Author: frSimple
 */


@Service
public class MonitorServiceImpl
        extends ServiceImpl<MonitorMapper, Monitor>
        implements MonitorService {

}