package org.simple.flow.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.simple.base.dto.mybatis.Page;
import org.simple.flow.dto.ModelDto;

import javax.xml.stream.XMLStreamException;
import java.io.UnsupportedEncodingException;

public interface FlowDesignService {

    /**
     * 查询流程模型列表
     * @param key:流程编号
     * @param name:流程名称模糊查询
     * @return List<ProcessDefinition>
     * */
    JSONObject list(Page<?> page, String key, String name);

    /**
     * 查询已发布的流程
     * @return List<ProcessDefinition>
     * */
    JSONArray listPublish();


    /**
     * 查询流程模型xml
     * @param id:流程编号
     * @return List<Process>
     * */
    String bpmnXml(String id);


    /**
     * 获取流程模型信息
     * @param id:流程编号
     * @return ModelDto
     * */
    ModelDto getModel(String id) throws XMLStreamException, UnsupportedEncodingException;

    /**
     * 新建流程模型
     * @param model:流程模型对象
     * */
    void addModel(ModelDto model);

    /**
     * 修改流程模型
     * @param model:流程模型对象
     * */
    void editModel(ModelDto model);

    /**
     * 删除流程模型
     * @param id:流程模型ID
     * */
    void delModel(String id);

    /**
     * 设计-保存流程模型
     * @param model:流程模型
     * */
    void saveModel(ModelDto model) throws Exception;

    /**
     * 设计-发布流程
     * @param id:流程模型ID
     * */
    void deployModel(String id) throws Exception;

    /**
     * 设计-保存并发布流程
     * @param model:流程模型
     * */
    void saveAndDeployModel(ModelDto model) throws Exception;


    /**
     * 激活流程
     * @param processId:流程定义ID
     * */
    void activeProcess(String processId);

    /**
     * 挂起流程
     * @param processId:流程定义ID
     * */
    void suspendProcess(String processId);

    /**
     * 预览bpmn的xml格式文件
     * @param modelDto:请求对象
     * */
    String viewBpmnXmlStr(ModelDto modelDto);

    /**
     * 导出工作流
     * */
    String exportFlow(String processId);

    /**
     * 导入工作流
     * */
    void importFlow(String xmlStr);

}
