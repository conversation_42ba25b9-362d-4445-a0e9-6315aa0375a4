package org.simple.flow.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ModelQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.simple.base.service.FrCodeQuickService;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.FormModelUtil;
import org.simple.base.vo.model.FormModelVo;
import org.simple.flow.builder.FlowPageBuilder;
import org.simple.flow.constant.FlowConstant;
import org.simple.flow.dto.*;
import org.simple.flow.enums.TaskAssigneType;
import org.simple.flow.enums.TaskCommonType;
import org.simple.flow.param.TaskParam;
import org.simple.flow.service.FlowTaskService;
import org.simple.flow.util.LogicFlowConvert;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class FlowTaskServiceImpl implements FlowTaskService {

    @Resource
    private TaskService taskService;

    @Resource
    private HistoryService historyService;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private FrCodeQuickService frCodeQuickService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    @DSTransactional
    public void submitTask(TaskSubmitDto taskDto) throws Exception {
        Task task = taskService.createTaskQuery().taskId(taskDto.getTaskId()).singleResult();
        if (ObjectUtil.isNull(task)) {
            throw new Exception("任务不存在");
        }
        if (StringUtils.isEmpty(taskDto.getAction())) {
            throw new Exception("处理动作不能为空");
        }
        UserTask userTask =
                (UserTask) repositoryService.getBpmnModel(task.getProcessDefinitionId())
                        .getFlowElement(task.getTaskDefinitionKey());
        List<SequenceFlow> sequenceFlows =
                userTask.getOutgoingFlows();
        JSONObject seqObj = new JSONObject();
        sequenceFlows.forEach(sequenceFlow -> {
            seqObj.put(sequenceFlow.getId(), sequenceFlow.getName());
        });
        //提交数据
        FormModelVo formModelVo = FormModelUtil.getFormModel(taskDto.getFormId());
        frCodeQuickService.dealEditForm(formModelVo.getDbName(), taskDto.getFormId(),
                taskDto.getBusinessId(), taskDto.getFormData());

        //提交流程
        Map<String, Object> variables = null == taskDto.getVariables() ?
                new HashMap<String, Object>() : taskDto.getVariables();
        variables.put(task.getTaskDefinitionKey() + "_action", taskDto.getAction());
        Authentication.setAuthenticatedUserId(taskDto.getUserId());
        taskService.addComment(task.getId(), task.getProcessInstanceId(),   //添加处理日志
                seqObj.getString(taskDto.getAction()), taskDto.getRemark());
        taskService.setAssignee(taskDto.getTaskId(), taskDto.getUserId());  //设置处理人
        taskService.complete(taskDto.getTaskId(), variables);  //处理任务
        Authentication.setAuthenticatedUserId(null);

    }

    @Override
    public FlowPage taskLogs(String processInstanceId) {
        List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricActivityInstanceStartTime().desc()
                .orderByHistoricActivityInstanceEndTime().desc()
                .list();
        List<TaskLogDto> records = new ArrayList<>();
        for (HistoricActivityInstance hsi : list) {
            if ((hsi.getActivityType().contains("startEvent") ||
                    hsi.getActivityType().contains("endEvent") ||
                    hsi.getActivityType().contains("userTask"))) {
                TaskLogDto taskLogDto = new TaskLogDto();
                taskLogDto.setDuration(hsi.getDurationInMillis());
                taskLogDto.setStartTime(hsi.getStartTime());
                taskLogDto.setEndTime(hsi.getEndTime());
                if (hsi.getActivityType().equals("startEvent")) {
                    taskLogDto.setNodeName("开始");
                    HistoricProcessInstance historicProcessInstance =
                            historyService.createHistoricProcessInstanceQuery()
                                    .processInstanceId(processInstanceId)
                                    .singleResult();
                    taskLogDto.setUserId(historicProcessInstance.getStartUserId());
                    taskLogDto.setType("发起");
                    records.add(taskLogDto);
                } else if (hsi.getActivityType().equals("endEvent")) {
                    taskLogDto.setNodeName("结束");
                    taskLogDto.setType("完成");
                    records.add(taskLogDto);
                } else {
                    taskLogDto.setUserId(hsi.getAssignee());
                    taskLogDto.setNodeName(hsi.getActivityName());
                    List<Comment> commentList =
                            taskService.getProcessInstanceComments(processInstanceId);
                    if (CollUtil.isNotEmpty(commentList)) {
                        commentList.forEach(comment -> {
                            if (comment.getTaskId().equals(hsi.getTaskId())) {
                                taskLogDto.setType(comment.getType());
                                taskLogDto.setRemark(comment.getFullMessage());
                                if (StringUtils.isNotEmpty(comment.getUserId())) {
                                    taskLogDto.setUserId(comment.getUserId());
                                }
                                taskLogDto.setEndTime(comment.getTime());
                                TaskLogDto newDto = new TaskLogDto();
                                BeanUtil.copyProperties(taskLogDto, newDto);
                                records.add(newDto);
                            }
                        });
                    } else {
                        records.add(taskLogDto);
                    }

                }
            }
        }
        //判断流程是否已作废，作废的话添加作废日志
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId)
                        .singleResult();
        if (StringUtils.isNotEmpty(historicProcessInstance.getDeleteReason())) {
            String reason = historicProcessInstance.getDeleteReason();
            TaskLogDto taskLogDto = new TaskLogDto();
            if (reason.startsWith("{")) {
                JSONObject reasonJson = JSON.parseObject(reason);
                taskLogDto.setRemark(reasonJson.getString("reason"));
                taskLogDto.setUserId(reasonJson.getString("userId"));
                taskLogDto.setEndTime(ObjectUtil.isNull(reasonJson.get("time")) ?
                        null : reasonJson.getDate("time"));
                taskLogDto.setStartTime(ObjectUtil.isNull(reasonJson.get("time")) ?
                        null : reasonJson.getDate("time"));
                taskLogDto.setType("作废");
                records.add(0, taskLogDto);
            }
        }
        return FlowPageBuilder.builder()
                .records(records).build();
    }

    @Override
    public FlowPage currentUserWaitTask(FlowPage flowPage, TaskParam taskParam) {
        TaskQuery query = taskService.createTaskQuery()
                .active()
                .includeCaseVariables()
                .orderByTaskCreateTime().desc();
        int size = Long.valueOf(flowPage.getSize()).intValue();
        int current = Long.valueOf(flowPage.getCurrent()).intValue();
        int start = (current - 1) * size;
        if (StringUtils.isNotEmpty(taskParam.getUserId())) {
            query.taskCandidateOrAssigned(taskParam.getUserId());
        }
        if (CollUtil.isNotEmpty(taskParam.getUserRoles())) {
            query.taskCandidateGroupIn(taskParam.getUserRoles());
        }
        if (StringUtils.isNotEmpty(taskParam.getProcessKey())) {
            query.processDefinitionKey(taskParam.getProcessKey());
        }
        if (StringUtils.isNotEmpty(taskParam.getProcessName())) {
            query.processDefinitionNameLike("%" + taskParam.getProcessName() + "%");
        }
        if (StringUtils.isNotEmpty(taskParam.getFlowTitle())) {
            query.processDefinitionNameLike("%" + taskParam.getFlowTitle() + "%");
        }
        List<Task> list =
                query.listPage(start, size);
        List<TaskUserDto> records = new ArrayList<>();
        list.forEach(task -> {
            ProcessInstance processInstance =
                    runtimeService.createProcessInstanceQuery()
                            .processInstanceId(task.getProcessInstanceId()).singleResult();
            TaskUserDto taskUserDto = new TaskUserDto();
            taskUserDto.setTaskId(task.getId());
            taskUserDto.setFlowTitle(processInstance.getName());
            taskUserDto.setStartTime(task.getCreateTime());
            taskUserDto.setStartUser(processInstance.getStartUserId());
            taskUserDto.setCurrentNodes(task.getName());
            taskUserDto.setVersion(processInstance.getProcessDefinitionVersion());
            taskUserDto.setProcessName(processInstance.getProcessDefinitionName());
            taskUserDto.setProcessInstanceId(processInstance.getProcessInstanceId());
            taskUserDto.setBusiness(processInstance.getBusinessKey());

            //获取当前节点页面
            taskUserDto.setFormType(repositoryService.getBpmnModel(processInstance
                            .getProcessDefinitionId())
                    .getFlowElement(task.getTaskDefinitionKey())
                    .getAttributeValue(FlowConstant.NAME_SPACE, "formType"));
            taskUserDto.setFormUrl(repositoryService.getBpmnModel(processInstance
                            .getProcessDefinitionId())
                    .getFlowElement(task.getTaskDefinitionKey())
                    .getAttributeValue(FlowConstant.NAME_SPACE, "form"));
            records.add(taskUserDto);
        });


        return FlowPageBuilder.builder()
                .records(records)
                .total(query.count())
                .build();
    }

    @Override
    public FlowPage currentUserHaveTask(FlowPage flowPage, TaskParam taskParam) {
        HistoricTaskInstanceQuery query = historyService.createHistoricTaskInstanceQuery()
                .finished()
                .orderByHistoricTaskInstanceEndTime()
                .desc();
        int size = Long.valueOf(flowPage.getSize()).intValue();
        int current = Long.valueOf(flowPage.getCurrent()).intValue();
        int start = (current - 1) * size;
        if (StringUtils.isNotEmpty(taskParam.getUserId())) {
            query.taskAssignee(taskParam.getUserId());
        }
        if (StringUtils.isNotEmpty(taskParam.getProcessName())) {
            query.processDefinitionNameLike("%" + taskParam.getProcessName() + "%");
        }
        if (StringUtils.isNotEmpty(taskParam.getFlowTitle())) {
            query.processDefinitionNameLike("%" + taskParam.getFlowTitle() + "%");
        }
        if (StringUtils.isNotEmpty(taskParam.getProcessKey())) {
            query.processDefinitionKey(taskParam.getProcessKey());
        }

        List<HistoricTaskInstance> list =
                query.listPage(start, size);
        List<TaskUserDto> records = new ArrayList<>();
        list.forEach(task -> {
            HistoricProcessInstance historicProcessInstance
                    = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .singleResult();
            TaskUserDto taskUserDto = new TaskUserDto();
            taskUserDto.setTaskId(task.getId());
            taskUserDto.setFlowTitle(historicProcessInstance.getName());
            taskUserDto.setStartTime(task.getCreateTime());
            taskUserDto.setStartUser(historicProcessInstance.getStartUserId());
            taskUserDto.setCurrentNodes(task.getName());
            taskUserDto.setVersion(historicProcessInstance.getProcessDefinitionVersion());
            taskUserDto.setProcessName(historicProcessInstance.getProcessDefinitionName());
            taskUserDto.setDuration(task.getDurationInMillis());
            taskUserDto.setEndTime(task.getEndTime());
            taskUserDto.setProcessInstanceId(task.getProcessInstanceId());
            ModelQuery modelQuery =
                    repositoryService.createModelQuery();
            Model m = modelQuery.modelKey(historicProcessInstance
                    .getProcessDefinitionKey()).latestVersion().singleResult();
            taskUserDto.setViewType(JSONObject.parseObject(m.getMetaInfo()).getString("viewType"));
            taskUserDto.setViewPage(JSONObject.parseObject(m.getMetaInfo()).getString("viewPage"));
            taskUserDto.setBusiness(historicProcessInstance.getBusinessKey());
            records.add(taskUserDto);
        });
        return FlowPageBuilder.builder()
                .records(records)
                .total(query.count())
                .build();
    }

    @Override
    public FlowPage currentUserRequestTask(FlowPage flowPage, TaskParam taskParam) {
        HistoricProcessInstanceQuery query =
                historyService.createHistoricProcessInstanceQuery()
                        .orderByProcessInstanceStartTime()
                        .desc();
        int size = Long.valueOf(flowPage.getSize()).intValue();
        int current = Long.valueOf(flowPage.getCurrent()).intValue();
        int start = (current - 1) * size;
        if (StringUtils.isNotEmpty(taskParam.getUserId())) {
            query.startedBy(taskParam.getUserId());
        }
        if (StringUtils.isNotEmpty(taskParam.getProcessName())) {
            query.processDefinitionName(taskParam.getProcessName());
        }
        if (StringUtils.isNotEmpty(taskParam.getFlowTitle())) {
            query.processInstanceNameLike("%" + taskParam.getFlowTitle() + "%");
        }
        if (StringUtils.isNotEmpty(taskParam.getProcessKey())) {
            query.processDefinitionKey(taskParam.getProcessKey());
        }
        List<HistoricProcessInstance> list =
                query.listPage(start, size);
        List<TaskUserDto> records = new ArrayList<>();
        list.forEach(task -> {
            HistoricProcessInstance historicProcessInstance
                    = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getId())
                    .singleResult();
            TaskUserDto taskUserDto = new TaskUserDto();
            taskUserDto.setTaskId(task.getId());
            taskUserDto.setFlowTitle(historicProcessInstance.getName());
            taskUserDto.setStartTime(task.getStartTime());
            taskUserDto.setStartUser(historicProcessInstance.getStartUserId());
            taskUserDto.setVersion(historicProcessInstance.getProcessDefinitionVersion());
            taskUserDto.setProcessName(historicProcessInstance.getProcessDefinitionName());
            taskUserDto.setDuration(task.getDurationInMillis());
            taskUserDto.setEndTime(task.getEndTime());
            taskUserDto.setProcessInstanceId(task.getId());

            //添加状态
            ProcessInstance runInstance = runtimeService
                    .createProcessInstanceQuery()
                    .processInstanceId(task.getId()).singleResult();
            if (ObjectUtil.isNull(task.getEndTime())) {
                if (ObjectUtil.isNotNull(runInstance)) {
                    if (runInstance.isSuspended()) {
                        taskUserDto.setStatus("0"); //已暂停
                    } else if (runInstance.isEnded()) {
                        taskUserDto.setStatus("99"); //正常结束
                    } else {
                        taskUserDto.setStatus("1"); //正在运行
                    }
                } else {
                    taskUserDto.setStatus("-1"); //已作废
                }
            } else {
                if (ObjectUtil.isNull(runInstance)) {
                    if (StringUtils.isNotEmpty(task.getEndActivityId())) {
                        taskUserDto.setStatus("99"); //
                    } else {
                        taskUserDto.setStatus("-1"); //已作废
                    }
                } else {
                    taskUserDto.setStatus("99"); //正常结束
                }
            }

            //处理流程当前节点
            List<Task> tasks = taskService.createTaskQuery().processInstanceId(task.getId()).list();
            List<String> nodeNames = new ArrayList<>();
            List<String> assignees = new ArrayList<>();
            tasks.forEach(t -> {
                //查询流程节点 ，获取候选人或群组
                FlowElement flowElement = repositoryService.getBpmnModel(t.getProcessDefinitionId()).getFlowElement(t.getTaskDefinitionKey());
                if (flowElement instanceof UserTask) {
                    UserTask user = (UserTask) flowElement;
                    String assignName = user.getAttributeValue(FlowConstant.NAME_SPACE, "assigneeName");
                    String assignType = user.getAttributeValue(FlowConstant.NAME_SPACE, "assigneeType");
                    if (StringUtils.isNotEmpty(assignType) && assignType.equals(TaskAssigneType.ASS_ROLE.getKey())) {
                        assignees.add("[" + t.getName() + "]" + TaskAssigneType.ASS_ROLE.getValue() + "：" + assignName);
                    } else if (StringUtils.isNotEmpty(assignType) && assignType.equals(TaskAssigneType.ASS_USER.getKey())) {
                        assignees.add("[" + t.getName() + "]" + TaskAssigneType.ASS_USER.getValue() + "：" + assignName);
                    }
                }
                nodeNames.add(t.getName());
            });
            taskUserDto.setCurrentNodes(StringUtils.join(nodeNames, ","));
            taskUserDto.setCurrentAssignees(StringUtils.join(assignees, "|"));
            ModelQuery modelQuery =
                    repositoryService.createModelQuery();
            Model m = modelQuery.modelKey(task
                    .getProcessDefinitionKey()).latestVersion().singleResult();
            taskUserDto.setViewType(JSONObject.parseObject(m.getMetaInfo()).getString("viewType"));
            taskUserDto.setViewPage(JSONObject.parseObject(m.getMetaInfo()).getString("viewPage"));
            taskUserDto.setBusiness(task.getBusinessKey());
            records.add(taskUserDto);
        });
        return FlowPageBuilder.builder()
                .records(records)
                .total(query.count())
                .build();
    }

    @Override
    public FlowPage userTasks(FlowPage flowPage, TaskParam taskParam) {
        TaskQuery query = taskService.createTaskQuery()
                .active()
                .includeCaseVariables()
                .orderByTaskCreateTime().desc();
        int size = Long.valueOf(flowPage.getSize()).intValue();
        int current = Long.valueOf(flowPage.getCurrent()).intValue();
        int start = (current - 1) * size;
        if (StringUtils.isNotEmpty(taskParam.getProcessKey())) {
            query.processDefinitionKey(taskParam.getProcessKey());
        }
        if (StringUtils.isNotEmpty(taskParam.getProcessName())) {
            query.processDefinitionNameLike("%" + taskParam.getProcessName() + "%");
        }
        if (StringUtils.isNotEmpty(taskParam.getFlowTitle())) {
            query.processDefinitionNameLike("%" + taskParam.getFlowTitle() + "%");
        }
        List<Task> list =
                query.listPage(start, size);
        List<TaskUserDto> records = new ArrayList<>();
        list.forEach(task -> {
            ProcessInstance processInstance =
                    runtimeService.createProcessInstanceQuery()
                            .processInstanceId(task.getProcessInstanceId()).singleResult();
            TaskUserDto taskUserDto = new TaskUserDto();
            taskUserDto.setTaskId(task.getId());
            taskUserDto.setFlowTitle(processInstance.getName());
            taskUserDto.setStartTime(task.getCreateTime());
            taskUserDto.setStartUser(processInstance.getStartUserId());
            taskUserDto.setCurrentNodes(task.getName());
            taskUserDto.setVersion(processInstance.getProcessDefinitionVersion());
            taskUserDto.setProcessName(processInstance.getProcessDefinitionName());
            FlowElement flowElement = repositoryService.
                    getBpmnModel(task.getProcessDefinitionId())
                    .getFlowElement(task.getTaskDefinitionKey());
            taskUserDto.setCurrentAssignees(LogicFlowConvert.getDealUser(flowElement, task));
            taskUserDto.setAssignees(task.getAssignee());
            taskUserDto.setBusiness(processInstance.getBusinessKey());
            ModelQuery modelQuery =
                    repositoryService.createModelQuery();
            Model m = modelQuery.modelKey(processInstance
                    .getProcessDefinitionKey()).latestVersion().singleResult();
            taskUserDto.setViewType(JSONObject.parseObject(m.getMetaInfo()).getString("viewType"));
            taskUserDto.setViewPage(JSONObject.parseObject(m.getMetaInfo()).getString("viewPage"));
            if (ObjectUtil.isNotNull(task.getDelegationState())) {
                taskUserDto.setDelegate(task.getAssignee());
            }
            records.add(taskUserDto);
        });


        return FlowPageBuilder.builder()
                .records(records)
                .total(query.count())
                .build();
    }

    @Override
    public void transfer(TaskParam taskParam) throws Exception {
        Task task = taskService.createTaskQuery().taskId(taskParam.getTaskId())
                .singleResult();
        if (ObjectUtil.isNull(task)) {
            throw new Exception("任务不存在");
        }
        if (StringUtils.isEmpty(taskParam.getToUserId())) {
            throw new Exception("转办人不能为空");
        }
        //处理意见
        String remark = taskParam.getUserName() + "将任务[转办]给：" + taskParam.getToUser();
        Authentication.setAuthenticatedUserId(taskParam.getUserId());
        taskService.addComment(task.getId(), task.getProcessInstanceId(), TaskCommonType.TRANSFER.getValue()
                , remark);
        Authentication.setAuthenticatedUserId(null);
        taskService.setOwner(task.getId(), taskParam.getUserId());
        taskService.setAssignee(task.getId(), taskParam.getToUserId());
    }

    @Override
    public void delegate(TaskParam taskParam) throws Exception {
        Task task = taskService.createTaskQuery().taskId(taskParam.getTaskId())
                .singleResult();
        if (ObjectUtil.isNull(task)) {
            throw new Exception("任务不存在");
        }
        if (StringUtils.isEmpty(taskParam.getToUserId())) {
            throw new Exception("委托人不能为空");
        }
        //处理意见
        String remark = taskParam.getUserName() +
                "将任务[委派]给：" + taskParam.getToUser();
        Authentication.setAuthenticatedUserId(taskParam.getUserId());
        taskService.addComment(task.getId(), task.getProcessInstanceId(), TaskCommonType.DELEGATE.getValue()
                , remark);
        taskService.setOwner(task.getId(), taskParam.getUserId());
        taskService.delegateTask(task.getId(), taskParam.getToUserId());
        Authentication.setAuthenticatedUserId(null);
    }

    @Override
    public JSONArray nextAction(TaskParam taskParam) throws Exception {
        Task task =
                taskService.createTaskQuery().taskId(taskParam.getTaskId()).singleResult();
        if (ObjectUtil.isNull(task)) {
            throw new Exception("未查询到任务");
        }
        UserTask userTask =
                (UserTask) repositoryService.getBpmnModel(task.getProcessDefinitionId())
                        .getFlowElement(task.getTaskDefinitionKey());
        List<SequenceFlow> sequenceFlows =
                userTask.getOutgoingFlows();
        JSONArray result = new JSONArray();
        sequenceFlows.forEach(sequenceFlow -> {
            JSONObject obj = new JSONObject();
            obj.put("value", sequenceFlow.getId());
            obj.put("label", sequenceFlow.getName());
            result.add(obj);
        });
        return result;
    }

    @Override
    public List<ProcessDto> taskCount() throws Exception {
        ModelQuery query =
                repositoryService.createModelQuery().latestVersion().
                        orderByCreateTime().desc();
        ProcessDefinitionQuery processDefinitionQuery =
                repositoryService.createProcessDefinitionQuery().latestVersion()
                        .active();
        List<Model> list = query.list();
        List<ProcessDto> result = new ArrayList<>();
        list.forEach(obj -> {
            ProcessDefinition p =
                    processDefinitionQuery.processDefinitionKey(obj.getKey()).singleResult();
            if (null != p) {
                ProcessDto processDto = new ProcessDto();
                processDto.setProcessKey(obj.getKey());
                processDto.setProcessName(obj.getName());
                processDto.setRunInsCount(getTaskCount(obj.getKey()));
                result.add(processDto);
            }
        });
        return result;
    }

    private Long getTaskCount(String processKey) {
        TaskQuery query = taskService.createTaskQuery()
                .active()
                .includeCaseVariables()
                .orderByTaskCreateTime().desc();
        TaskParam taskParam = new TaskParam();
        taskParam.setUserId(AuthUtil.getUserId());
        taskParam.setUserRoles(AuthUtil.getRoles());
        query.taskCandidateOrAssigned(taskParam.getUserId());
        query.taskCandidateGroupIn(taskParam.getUserRoles());
        query.processDefinitionKey(processKey);
        return query.count();
    }
}
