# 设备管理系统架构设计

## 1. 系统概述

基于 frSimpleBoot Pro 脚手架开发的企业级设备管理系统，采用前后端分离架构，支持多租户、权限管理、工作流等功能。

## 2. 技术架构

### 2.1 后端技术栈
- **框架**: Spring Boot 3.x + JDK 17
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis-Plus 3.5+
- **认证**: Sa-Token + OAuth2 + JWT
- **缓存**: Redis + Redisson
- **任务调度**: XXL-Job
- **文档**: Knife4j (Swagger)
- **工具**: Hutool、Lombok

### 2.2 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI库**: TDesign
- **构建工具**: Vite 5
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios

## 3. 模块架构设计

### 3.1 模块划分

```
equipment/
├── equipment-base/        # 基础模块
│   ├── constants/         # 常量定义
│   ├── enums/            # 枚举类
│   ├── utils/            # 工具类
│   └── dto/              # 数据传输对象
├── equipment-core/        # 核心业务模块
│   ├── asset/            # 设备档案管理
│   ├── task/             # 任务调度中心
│   ├── workorder/        # 工单管理
│   ├── inspection/       # 设备点检
│   ├── patrol/           # 设备巡检
│   ├── maintenance/      # 设备保养
│   ├── repair/           # 设备保修维修
│   ├── inventory/        # 设备盘点
│   ├── transfer/         # 设备调拨
│   └── contract/         # 合同管理
├── equipment-basic/       # 基础数据管理
│   ├── department/       # 科室管理
│   ├── location/         # 位置管理
│   ├── category/         # 设备分类
│   └── type/             # 设备类型
└── equipment-report/      # 报表统计
    ├── dashboard/        # 管理员大屏
    └── statistics/       # 统计报表
```

### 3.2 数据模型设计

#### 基础数据实体
- **Department**: 科室管理
- **Location**: 位置管理  
- **Category**: 设备分类
- **Type**: 设备类型

#### 核心业务实体
- **Asset**: 设备档案
- **BOM**: 设备BOM清单
- **Task**: 任务管理
- **WorkOrder**: 工单管理
- **InspectionStandard/Record**: 点检标准和记录
- **PatrolPlan/Task/Record**: 巡检方案、任务和记录
- **MaintenancePlan/Record**: 保养计划和记录
- **RepairApply/Task**: 保修申报和维修任务
- **InventoryTask/Detail**: 盘点任务和详情
- **Transfer**: 设备调拨
- **Contract**: 合同管理

## 4. 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   移动端(小程序)  │    │    Web管理端     │    │   移动端(APP)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
           │                       │                       │
           └───────────────────────┼───────────────────────┘
                                   │
┌──────────────────────────────────┼──────────────────────────────────┐
│                          API Gateway                                │
└──────────────────────────────────┼──────────────────────────────────┘
                                   │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   设备管理服务    │    │   基础数据服务    │    │   报表统计服务    │
│  Equipment-Core │    │ Equipment-Basic │    │Equipment-Report │
└─────────────────┘    └─────────────────┘    └─────────────────┘
           │                       │                       │
           └───────────────────────┼───────────────────────┘
                                   │
┌──────────────────────────────────┼──────────────────────────────────┐
│                       数据访问层 (MyBatis-Plus)                      │
└──────────────────────────────────┼──────────────────────────────────┘
                                   │
┌──────────────────────────────────┼──────────────────────────────────┐
│                          MySQL数据库                                │
└──────────────────────────────────┼──────────────────────────────────┘
```

## 5. 核心功能设计

### 5.1 设备档案管理

#### 功能特性
- 设备基本信息维护
- 设备编码自动生成
- 设备图片管理
- BOM树形结构
- 档案卡片打印
- Excel批量导入

#### 技术实现
```java
@RestController
@RequestMapping("/equipment/asset")
public class AssetController extends BaseController {
    
    @PostMapping("/import")
    @SaCheckPermission("asset:import")
    public FrResult<?> importAssets(@RequestParam("file") MultipartFile file) {
        // Excel导入逻辑
    }
    
    @GetMapping("/print/{id}")
    @SaCheckPermission("asset:print")
    public void printAssetCard(@PathVariable String id, HttpServletResponse response) {
        // 档案卡片打印
    }
}
```

### 5.2 任务调度中心

#### 功能特性
- 任务类型管理
- 任务分配与跟踪
- 任务自动催办
- 任务统计分析

#### 技术实现
- 集成XXL-Job分布式任务调度
- 基于Redis的任务状态缓存
- 消息推送机制

### 5.3 设备点检

#### 功能特性
- 点检标准设置
- 点检单录入
- 照片上传
- 点检记录查询

#### 业务流程
```
设置点检标准 → 生成点检任务 → 执行点检 → 录入结果 → 生成报表
```

### 5.4 设备巡检

#### 功能特性
- 巡检方案设置
- 巡检任务创建
- 巡检单生成
- 巡检记录管理

#### 技术亮点
- 支持自定义巡检方案
- 不同设备类型使用不同方案
- 巡检路线规划

### 5.5 设备保养

#### 功能特性
- 保养计划管理
- 自动调度提醒
- 保养记录录入
- 保养历史查询

#### 自动化特性
- 按保养频率自动生成任务
- 保养提醒推送
- 保养成本统计

### 5.6 设备保修维修

#### 功能特性
- 保修申报
- 保修审核
- 维修任务管理
- 维修成本统计

#### 工作流程
```
故障申报 → 审核 → 分配维修任务 → 执行维修 → 验收 → 归档
```

### 5.7 设备盘点

#### 功能特性
- 盘点任务创建
- 扫码盘点
- 盘点差异分析
- 盘点报表生成

#### 技术实现
- 二维码扫描识别
- 实时盘点状态更新
- 差异报告自动生成

## 6. 权限设计

### 6.1 角色定义
- **超级管理员**: 系统所有权限
- **设备管理员**: 设备档案、任务管理等权限
- **科室管理员**: 本科室设备相关权限
- **维修人员**: 维修任务相关权限
- **普通用户**: 查看权限

### 6.2 权限控制
```java
// 权限注解示例
@SaCheckPermission("asset:add")     // 设备新增
@SaCheckPermission("asset:edit")    // 设备修改
@SaCheckPermission("asset:del")     // 设备删除
@SaCheckPermission("asset:query")   // 设备查询
@SaCheckPermission("asset:export")  // 设备导出
@SaCheckPermission("asset:import")  // 设备导入
```

## 7. 数据安全

### 7.1 多租户隔离
- 基于tenant_id的数据隔离
- 行级数据安全控制
- 租户间数据完全隔离

### 7.2 数据加密
- 敏感信息字段加密存储
- 传输过程HTTPS加密
- 数据库连接加密

### 7.3 审计日志
- 操作日志记录
- 数据变更追踪
- 系统访问日志

## 8. 性能优化

### 8.1 缓存策略
- Redis缓存热点数据
- 设备状态实时缓存
- 统计数据定时缓存

### 8.2 数据库优化
- 索引优化
- 分页查询优化
- 读写分离

### 8.3 前端优化
- 组件懒加载
- 图片压缩上传
- 分页数据加载

## 9. 扩展性设计

### 9.1 插件化架构
- 功能模块插件化
- 第三方系统集成接口
- 自定义字段扩展

### 9.2 微服务架构
- 服务拆分设计
- 服务间通信
- 分布式事务处理

### 9.3 移动端支持
- 响应式设计
- 小程序接口
- APP原生开发

## 10. 部署架构

### 10.1 开发环境
```
本地开发 → Git提交 → Jenkins构建 → 开发环境部署
```

### 10.2 生产环境
```
nginx (负载均衡) 
    ↓
Spring Boot应用集群
    ↓
MySQL主从集群 + Redis集群
```

### 10.3 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  equipment-app:
    image: equipment-management:latest
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: equipment_db
  
  redis:
    image: redis:6.2
```

## 11. 监控运维

### 11.1 应用监控
- Spring Boot Actuator健康检查
- 接口响应时间监控
- 系统资源使用监控

### 11.2 日志管理
- ELK Stack日志收集
- 日志分级管理
- 异常告警机制

### 11.3 备份策略
- 数据库定时备份
- 文件存储备份
- 配置文件备份

这个架构设计为设备管理系统的开发提供了完整的技术方案和实施路径，确保系统的可扩展性、稳定性和安全性。