package org.simple.license.controller;

import cn.hutool.core.util.HexUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.simple.base.dto.license.LicenseExtParam;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.license.dto.LicenseSimpleParam;
import org.simple.license.entity.License;
import org.simple.license.service.CreateLicense;
import org.simple.license.service.LicenseService;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/license/info")
@Tag(name = "license接口")
@Slf4j
public class LicenseController {

    @Resource
    private LicenseService licenseService;


    @PostMapping("list")
    public FrResult<?> list(@RequestBody Page<License> page, @RequestBody License license) {
        return FrResult.success(licenseService.page(page, Wrappers.query(license)));
    }

    @PostMapping("create")
    public FrResult<?> createLicense(@RequestBody License license) {
        try {
            if (StringUtils.isEmpty(license.getSubject())) {
                license.setSubject(RandomUtil.getLicenseId());
            }
            LicenseExtParam extParam = new LicenseExtParam();
            extParam.setAuthObj(license.getAuthObj());
            extParam.setIpAddress(license.getIpAddress());
            extParam.setMacAddress(license.getMacAddress());
            extParam.setAuthEmail(license.getAuthEmail());
            CreateLicense createLicense = new CreateLicense(
                    LicenseSimpleParam.builder()
                            .subject(license.getSubject())
                            .startDateTime(license.getStartDateTime())
                            .endDateTime(license.getEndDateTime())
                            .licenseExtParam(extParam)
                            .build()
            );
            byte[] licenseByte = createLicense.create();
            license.setInfo(createLicense.getInfo());
            license.setFile(licenseByte);
            licenseService.save(license);
            return FrResult.success();
        } catch (Exception ex) {
            log.error("生成license文件失败", ex);
            return FrResult.failed(ex.getMessage());
        }
    }


    @GetMapping("downLoad/{id}")
    public FrResult<?> downLoad(@PathVariable("id") String id,
                                HttpServletResponse response){
        try {
            response.reset();
            License license = licenseService.getById(id);
            byte[] licenseByte = license.getFile();
            return FrResult.success(HexUtil.encodeHexStr(licenseByte));
        } catch (Exception ex) {
            log.error("生成license文件失败", ex);
            return FrResult.failed(ex.getMessage());
        }
    }
}
