package org.simple.license.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import org.simple.base.dto.license.LicenseExtParam;

@Data
@Builder
public class LicenseSimpleParam {

    @Schema(description = "私钥别名")
    private String priKeyAlias;
    @Schema(description = "私钥密码")
    private String priKeyPwd;
    @Schema(description = "密钥库密码")
    private String keyStorePwd;
    @Schema(description = "私钥文件路径")
    private String priPath;
    @Schema(description = "项目编号")
    private String subject;
    @Schema(description = "有效开始时间")
    private String startDateTime;
    @Schema(description = "失效时间")
    private String endDateTime;
    @Schema(description = "授权发布组织")
    private String info;
    @Schema(description = "其它个性化内容")
    private LicenseExtParam licenseExtParam;

}
