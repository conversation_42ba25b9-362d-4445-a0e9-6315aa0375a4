package org.simple.license.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.simple.base.dto.BaseEntity;

import java.io.Serial;
import java.io.Serializable;


/**
 * @Copyright: frSimple
 * @Desc: 授权license信息实体
 * @Date: 2024-07-28 14:16:46
 * @Author: frSimple
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "center_license")
@Schema(description = "授权license信息")
public class License extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
    @Schema(description = "项目编号")
    private String subject;
    @Schema(description = "有效开始时间")
    private String startDateTime;
    @Schema(description = "失效时间")
    private String endDateTime;
    @Schema(description = "mac地址")
    private String macAddress;
    @Schema(description = "ip地址")
    private String ipAddress;
    @Schema(description = "授权发布组织")
    private String info;
    @Schema(description = "授权对象联系方式")
    private String authEmail;
    @Schema(description = "授权对象")
    @TableField(condition = SqlCondition.LIKE)
    private String authObj;
    @Schema(description = "授权文件")
    private byte[] file;
}
