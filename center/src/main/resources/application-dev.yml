server:
  tomcat:
    uri-encoding: utf-8
    max-http-form-post-size: 10MB
    max-swallow-size: 10MB
    max-part-header-size: 5MB
    max-part-count: 100
  port: 3344

spring:
  #多数据库配置
  datasource:
    dynamic:
      p6spy: true
      lazy: false
      primary: master
      strict: false
      hikari:
        min-idle: 10
        max-pool-size: 30
        is-auto-commit: true
        idle-timeout: 30000
        max-lifetime: 900000
        connection-timeout: 10000
        connection-test-query: SELECT 1
        validation-timeout: 1000
      datasource:
        #基础数据库-mysql
        master:
          poolName: master
          username: root
          password: 123456
          url: *********************************************************************************************************
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
  data:
    #redis配置
    redis:
      # redis数据库索引(默认为0)，我们使用索引为3的数据库，避免和其他数据库冲突
      database: 10
      # redis服务器地址（默认为127.0.01）
      host: localhost
      # redis端口（默认为6379）
      port: 6379
      # redis访问密码（默认为空）
      password: 123456
      # redis连接超时时间（单位毫秒）
      timeout: 3000
      # redis连接池配置
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 200
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 0
  #rabbitmq消息队列
  rabbitmq:
    enable: false
    host: **************
    port: 5672
    username: frsimple
    password: frsimple
config:
  snowflake:
    workerId: 1

#弃用
app:
  auth:
    lockTime: 10
    errorNumber: 3
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCi7arytH+MJmau5lJsFt/98tJE4MTnf0aWUlrQhh5L1dfkJCai7XNGzDx1UnecVBSglDMgdmWUwOoXwQhHdb3uR9BJqH6Rj6D035lVqKyKirAYaD+PcAIZwus1HxVz/junibVT/yr6TlkpWCgS9saXGJrnX5eJjhvEjNbQjcV1vwIDAQAB
    privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKLtqvK0f4wmZq7mUmwW3/3y0kTgxOd/RpZSWtCGHkvV1+QkJqLtc0bMPHVSd5xUFKCUMyB2ZZTA6hfBCEd1ve5H0EmofpGPoPTfmVWorIqKsBhoP49wAhnC6zUfFXP+O6eJtVP/KvpOWSlYKBL2xpcYmudfl4mOG8SM1tCNxXW/AgMBAAECgYAFQj0p/HtAOrF5kRooiywR2WEOpqyXhj5w6Qz6VwhD5vQpbnGuASw4pRK8fK8H2JgaPyS2HJ9jVKWACWB37swOOnN2pavcGT/AaLX1CEItdA6mwTDJjoDLW7QCRtXm5mQx5+OwA2dCkBCZV/Fwoi9fMVq7rrR6Fhy0cMMxmoe6UQJBAOZVY8/u/JmkMW27rRleOQ829tjl26TTI7sgwE4YXVgTmC3NqFnutiJPRpIPWUnFgC4arvURH4Bsh+WSRkaaIa8CQQC1FXHH2MSklMkZ4t6L1+6gYnZIeWeWLmi2T13Bj47vRGE1cdR95/pVNxxywxq1gvMsu87CJpCpP6KehpYlwEDxAkAqfbIuF85toYak0ax7M5CfJ+qd1LmSTIkY6k/PmFsP9n1qZbga7xiWd71zEHXOUCr3VmDUQNZo4JypUzS3rZNtAkEAsLF4EJUHa8ByafvhQ3szsPPijt1HolcufZX72f8GbZm/cLLdsO1GaxgXfjO6QBrCxYeMPA39Yehh+WVB5RwvQQJARHL5Dow3b/19coHoNZun/AL5ry0xWBUzyCfGi8zN0knvesVLiRVNpXmyaxma6Yx5MwjaxDt3x/dgZ8P6/WbSUA==

# sa-token配置
sa-token:
  # token前缀
  token-prefix: Bearer
  #token名称 (同时也是cookie名称)
  token-name: Authorization
  #token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  #是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 是否尝试从cookie里读取token
  is-read-cookie: false
  #在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  #token风格
  token-style: tik
  #是否输出操作日志
  is-log: true
  jwt-secret-key: frsimple@2023

logging:
  level:
    org.simple: info # debug 可以查看打印的sql
    com.baomidou.dynamic: info
    org.flowable: info
mybatis-plus:
  global-config:
    banner: false
    db-config:
      id-type: auto
      where-strategy: NOT_EMPTY

#springdoc相关配置
springdoc:
  default-flat-param-object: true
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    enabled: true
  api-docs:
    path: /v3/api-docs
    enabled: ${springdoc.swagger-ui.enabled}
  group-configs:
    - group: 'frSimple'
      paths-to-match: '/**'
      packages-to-scan: org.simple.center  #改成你自己的包名，一般到启动类的包名
#knife4j相关配置 可以不用改
knife4j:
  enable: true
  setting:
    language: zh_cn

#flowable配置
flowable:
  async-executor-activate: false #关闭定时任务JOB
  #  将databaseSchemaUpdate设置为true。当Flowable发现库与数据库表结构不一致时，会自动将数据库表结构升级至新版本。
  # database-schema-update: true
#xxl-job配置
xxl:
  job:
    enable: false
    admin:
      addresses: http://localhost:8081/xxl-job-admin
    accessToken: 8WVT2qfjh3q3bNCX
    executor:
      appname: xxl-job-center
      address:
      ip:
      port: 0
      logpath: /home/<USER>
      logretentiondays: 30

#向量库配置
pgvector:
  host: **************
  port: 5432
  database: postgres
  username: postgres
  password: frsimple@2025
  table-name: frsimple

auth:
  sm2:
    public-key: 044e31ca21164a650a5fc4a32625cd7a3638cd32f87e62a6d0dc6fe37c3f205284f8fe73ec17e152d57e9de34c1e1e26768e6f1385d3cbb1008b78445ccd773744
    private-key: 00c7e91091e7e37863516a1182df468300859d4c1f9cadca564e8b58dcfab31076
  #xss,sql注入-白名單
  ignore:
    - /center/tenant/**
    - /center/code/**
  #接口白名单
  whiteUrl:
    - /api/base/basics/auth/login
    - /api/base/basics/auth/getPublicKey
    - /swagger-ui/index.html
    - /swagger-resources/**
    - /doc.html
    - /favicon.ico
    - /v3/api-docs/**
    - /swagger-ui/**
    - /error/**
    - /webjars/**

#授权信息
license:
  enable: false
  alias: publicCert
  storePwd: frsimple@2024
  storePath: /license/publicCerts.keystore
  subject: L20240728184246729Hrgsr
  path: /license/L20240728184246729Hrgsr.lic

# 图片/附件，注意生产请使用域名地址，地址为java后端服务地址
file:
  domain: http://localhost:3344
  path: /center/file/downLoadExpFile/

# Mybatis Mate 配置
mybatis-mate:
  cert:
    grant: thisIsTestLicense
    license: Qw46ncaiXykSqBByQDTGLFmior/pd8kzu3B4aR1BW3SQkXer4DtwQ0o5f6LNqjnUg3qRSo/3w+UmqYoSXA8ZBrTf16OcXFbLl7V3WRCBLeoRDi1c9BTQjumEzt/8HTzM0n+/hg+GU57OJbDvBZQI40U3abKT84qCW0EqjNA2Kg4fOlaHhXn6w3mEHXc3aMw8H+rBtxWSXyZOYUp83QVcTfh+n9sGDeys3NF7zjOrFxb24wB5DTpg7yirXnjeZznqPFwnyu45mACLPP4T8rFjHlW18+Pv2xoqlUCXx4QRsdBQEUDi26u4z0dba+O2S/vl7QGNfXZvGhbmv59IJK/FLA==
  encryptor:
    password: qmh9MK4KsZY8FnqkJYk8tzgc0H
    private-key: MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQg5cRkVd4x1B2CX4tD98a9nsAK2bWZB54MMU/F7D77Sq6gCgYIKoEcz1UBgi2hRANCAASrG8WsM9VAGQHphsaR/sY/dXpRmA9qQglUVLFB3o15zPwDSCUEz7xis6fBi2pT4Csy2Mt4ERSsuoGFdO52MZHz
    public-key: MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEqxvFrDPVQBkB6YbGkf7GP3V6UZgPakIJVFSxQd6Necz8A0glBM+8YrOnwYtqU+ArMtjLeBEUrLqBhXTudjGR8w==
    algorithm: sm2
