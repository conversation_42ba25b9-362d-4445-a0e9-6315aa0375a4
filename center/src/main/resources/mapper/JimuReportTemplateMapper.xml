<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.center.mapper.JimuReportTemplateMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="org.simple.center.entity.JimuReportTemplate">
        <id column="template_id" property="templateId" />
        <result column="template_name" property="templateName" />
        <result column="template_code" property="templateCode" />
        <result column="template_category" property="templateCategory" />
        <result column="template_desc" property="templateDesc" />
        <result column="template_config" property="templateConfig" />
        <result column="preview_image" property="previewImage" />
        <result column="is_builtin" property="isBuiltin" />
        <result column="use_count" property="useCount" />
        <result column="template_tags" property="templateTags" />
        <result column="applicable_scene" property="applicableScene" />
        <result column="template_version" property="templateVersion" />
        <result column="status" property="status" />
        <result column="sort" property="sort" />
        <result column="tenant_id" property="tenantId" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 按分类查询模板列表 -->
    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT *
        FROM jimu_report_template
        WHERE del_flag = 0
          AND status = 1
          <if test="category != null and category != ''">
              AND template_category = #{category}
          </if>
        ORDER BY use_count DESC, create_time DESC
    </select>

    <!-- 查询热门模板 -->
    <select id="selectPopularTemplates" resultMap="BaseResultMap">
        SELECT *
        FROM jimu_report_template
        WHERE del_flag = 0
          AND status = 1
        ORDER BY use_count DESC, create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询模板统计信息 -->
    <select id="selectTemplateStatistics" resultType="map">
        SELECT 
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_count,
            SUM(CASE WHEN is_builtin = 1 THEN 1 ELSE 0 END) as builtin_count,
            SUM(CASE WHEN is_builtin = 0 THEN 1 ELSE 0 END) as custom_count,
            SUM(use_count) as total_use_count
        FROM jimu_report_template
        WHERE del_flag = 0
    </select>

    <!-- 更新模板使用次数 -->
    <update id="increaseUseCount">
        UPDATE jimu_report_template
        SET use_count = use_count + 1,
            update_time = NOW()
        WHERE template_id = #{templateId}
          AND del_flag = 0
    </update>

    <!-- 根据标签查询模板 -->
    <select id="selectByTags" resultMap="BaseResultMap">
        SELECT *
        FROM jimu_report_template
        WHERE del_flag = 0
          AND status = 1
          AND (
              <foreach collection="tags" item="tag" separator=" OR ">
                  template_tags LIKE CONCAT('%', #{tag}, '%')
              </foreach>
          )
        ORDER BY use_count DESC, create_time DESC
    </select>

    <!-- 根据关键词搜索模板 -->
    <select id="searchTemplates" resultMap="BaseResultMap">
        SELECT *
        FROM jimu_report_template
        WHERE del_flag = 0
          AND status = 1
          AND (
              template_name LIKE CONCAT('%', #{keyword}, '%')
              OR template_desc LIKE CONCAT('%', #{keyword}, '%')
              OR template_tags LIKE CONCAT('%', #{keyword}, '%')
          )
        ORDER BY use_count DESC, create_time DESC
    </select>

</mapper>
