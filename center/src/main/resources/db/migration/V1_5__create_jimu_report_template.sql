-- 积木报表模板管理表

-- 1. 积木报表模板表
CREATE TABLE IF NOT EXISTS `jimu_report_template` (
  `template_id` varchar(32) NOT NULL COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(100) NOT NULL COMMENT '模板编码',
  `template_category` varchar(50) DEFAULT 'CUSTOM' COMMENT '模板分类(ASSET-设备管理,INSPECTION-点检管理,MAINTENANCE-维修管理,STATISTICS-统计分析,CUSTOM-自定义)',
  `template_desc` text COMMENT '模板描述',
  `template_config` longtext COMMENT '模板配置JSON',
  `preview_image` varchar(255) DEFAULT NULL COMMENT '预览图片',
  `is_builtin` tinyint(1) DEFAULT '0' COMMENT '是否内置模板(0-否，1-是)',
  `use_count` int(11) DEFAULT '0' COMMENT '使用次数',
  `template_tags` varchar(255) DEFAULT NULL COMMENT '模板标签(多个标签用逗号分隔)',
  `applicable_scene` varchar(255) DEFAULT NULL COMMENT '适用场景',
  `template_version` varchar(20) DEFAULT '1.0' COMMENT '模板版本',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0-禁用，1-启用)',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-未删除，1-已删除)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`template_id`),
  UNIQUE KEY `uniq_jimu_report_template_code` (`template_code`),
  KEY `idx_jimu_report_template_category` (`template_category`),
  KEY `idx_jimu_report_template_status` (`status`),
  KEY `idx_jimu_report_template_builtin` (`is_builtin`),
  KEY `idx_jimu_report_template_use_count` (`use_count`),
  KEY `idx_jimu_report_template_tenant` (`tenant_id`),
  KEY `idx_jimu_report_template_create_time` (`create_time`),
  KEY `idx_jimu_report_template_tags` (`template_tags`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积木报表模板表';

-- 2. 模板分类表
CREATE TABLE IF NOT EXISTS `jimu_report_template_category` (
  `category_id` varchar(32) NOT NULL COMMENT '分类ID',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_code` varchar(100) NOT NULL COMMENT '分类编码',
  `category_desc` text COMMENT '分类描述',
  `parent_id` varchar(32) DEFAULT NULL COMMENT '父级分类ID',
  `category_level` int(11) DEFAULT '1' COMMENT '分类层级',
  `category_icon` varchar(100) DEFAULT NULL COMMENT '分类图标',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0-禁用，1-启用)',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-未删除，1-已删除)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `uniq_jimu_report_template_category_code` (`category_code`),
  KEY `idx_jimu_report_template_category_parent` (`parent_id`),
  KEY `idx_jimu_report_template_category_level` (`category_level`),
  KEY `idx_jimu_report_template_category_status` (`status`),
  KEY `idx_jimu_report_template_category_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积木报表模板分类表';

-- 3. 模板使用记录表
CREATE TABLE IF NOT EXISTS `jimu_report_template_usage` (
  `usage_id` varchar(32) NOT NULL COMMENT '使用记录ID',
  `template_id` varchar(32) NOT NULL COMMENT '模板ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户名',
  `report_id` varchar(32) DEFAULT NULL COMMENT '生成的报表ID',
  `usage_type` varchar(20) DEFAULT 'USE' COMMENT '使用类型(USE-使用,COPY-复制,EXPORT-导出)',
  `usage_time` datetime DEFAULT NULL COMMENT '使用时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户代理',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`usage_id`),
  KEY `idx_jimu_report_template_usage_template` (`template_id`),
  KEY `idx_jimu_report_template_usage_user` (`user_id`),
  KEY `idx_jimu_report_template_usage_time` (`usage_time`),
  KEY `idx_jimu_report_template_usage_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积木报表模板使用记录表';

-- 初始化模板分类数据
INSERT INTO `jimu_report_template_category` (`category_id`, `category_name`, `category_code`, `category_desc`, `parent_id`, `category_level`, `category_icon`, `sort`, `status`, `tenant_id`, `del_flag`, `create_by`, `create_time`) VALUES
('ASSET', '设备管理', 'ASSET', '设备管理相关报表模板', NULL, 1, 'asset-icon', 1, 1, 'default', 0, 'system', NOW()),
('INSPECTION', '点检管理', 'INSPECTION', '点检管理相关报表模板', NULL, 1, 'inspection-icon', 2, 1, 'default', 0, 'system', NOW()),
('MAINTENANCE', '维修管理', 'MAINTENANCE', '维修管理相关报表模板', NULL, 1, 'maintenance-icon', 3, 1, 'default', 0, 'system', NOW()),
('STATISTICS', '统计分析', 'STATISTICS', '统计分析相关报表模板', NULL, 1, 'chart-icon', 4, 1, 'default', 0, 'system', NOW()),
('CUSTOM', '自定义', 'CUSTOM', '自定义报表模板', NULL, 1, 'custom-icon', 5, 1, 'default', 0, 'system', NOW());

-- 初始化内置模板数据
INSERT INTO `jimu_report_template` (`template_id`, `template_name`, `template_code`, `template_category`, `template_desc`, `template_config`, `is_builtin`, `use_count`, `template_tags`, `applicable_scene`, `template_version`, `status`, `sort`, `tenant_id`, `del_flag`, `create_by`, `create_time`) VALUES
('asset_statistics_template', '设备统计报表模板', 'asset_statistics', 'ASSET', '设备统计分析报表模板，包含设备数量、状态分布、分类统计等', '{"type":"dashboard","title":"设备统计报表","dataSource":"asset_statistics","layout":"grid","components":[{"type":"card","title":"设备总数","field":"totalAssets"},{"type":"pie","title":"设备状态分布","field":"statusDistribution"},{"type":"bar","title":"设备分类统计","field":"categoryDistribution"},{"type":"line","title":"设备使用年限","field":"ageDistribution"}]}', 1, 0, '设备,统计,图表', '设备管理,统计分析', '1.0', 1, 1, 'default', 0, 'system', NOW()),
('inspection_report_template', '点检报告模板', 'inspection_report', 'INSPECTION', '设备点检报告模板，包含点检任务统计、质量分析等', '{"type":"report","title":"点检报告","dataSource":"inspection_report","layout":"table","components":[{"type":"table","title":"点检任务统计","field":"taskStatistics"},{"type":"chart","title":"点检质量分析","field":"qualityStatistics"}]}', 1, 0, '点检,报告,质量', '点检管理,质量分析', '1.0', 1, 2, 'default', 0, 'system', NOW()),
('maintenance_report_template', '维修报告模板', 'maintenance_report', 'MAINTENANCE', '设备维修报告模板，包含维修工单统计、效率分析等', '{"type":"report","title":"维修报告","dataSource":"maintenance_report","layout":"mixed","components":[{"type":"table","title":"维修工单统计","field":"orderStatistics"},{"type":"chart","title":"维修效率分析","field":"efficiencyStatistics"}]}', 1, 0, '维修,报告,效率', '维修管理,效率分析', '1.0', 1, 3, 'default', 0, 'system', NOW()),
('approval_statistics_template', '审核统计报表模板', 'approval_statistics', 'STATISTICS', '审核统计分析报表模板，包含审核统计、质量分布等', '{"type":"dashboard","title":"审核统计报表","dataSource":"approval_statistics","layout":"grid","components":[{"type":"card","title":"审核统计","field":"approvalStatistics"},{"type":"pie","title":"质量等级分布","field":"qualityDistribution"},{"type":"bar","title":"审核效率统计","field":"efficiencyStatistics"}]}', 1, 0, '审核,统计,质量', '审核管理,统计分析', '1.0', 1, 4, 'default', 0, 'system', NOW());

-- 创建额外索引
CREATE INDEX idx_jimu_report_template_name ON jimu_report_template(template_name);
CREATE INDEX idx_jimu_report_template_version ON jimu_report_template(template_version);
CREATE INDEX idx_jimu_report_template_update_time ON jimu_report_template(update_time);
CREATE INDEX idx_jimu_report_template_category_name ON jimu_report_template_category(category_name);
CREATE INDEX idx_jimu_report_template_usage_type ON jimu_report_template_usage(usage_type);
