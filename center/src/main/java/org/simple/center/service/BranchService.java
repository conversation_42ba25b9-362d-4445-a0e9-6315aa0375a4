package org.simple.center.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.center.entity.Branch;

import java.util.List;


/**
 * @Copyright: simple
 * @Desc:
 * @Date: 2022-08-03 21:47:58
 * @Author: frSimple
 */
public interface BranchService extends IService<Branch> {

    public List<Tree<String>> queryOrganTree(String tenantName);

}