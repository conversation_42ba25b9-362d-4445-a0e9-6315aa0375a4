package org.simple.center.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.center.entity.JimuReportTemplate;

import java.util.List;
import java.util.Map;

/**
 * 积木报表模板服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface JimuReportTemplateService extends IService<JimuReportTemplate> {

    /**
     * 分页查询模板列表
     *
     * @param page 分页参数
     * @param template 查询条件
     * @return 模板列表
     */
    IPage<JimuReportTemplate> selectTemplatePage(Page<JimuReportTemplate> page, JimuReportTemplate template);

    /**
     * 按分类查询模板
     *
     * @param category 分类
     * @return 模板列表
     */
    List<JimuReportTemplate> getTemplatesByCategory(String category);

    /**
     * 获取热门模板
     *
     * @param limit 限制数量
     * @return 模板列表
     */
    List<JimuReportTemplate> getPopularTemplates(Integer limit);

    /**
     * 获取模板统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getTemplateStatistics();

    /**
     * 使用模板（增加使用次数）
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean useTemplate(String templateId);

    /**
     * 复制模板
     *
     * @param templateId 模板ID
     * @param newName 新模板名称
     * @return 新模板ID
     */
    String copyTemplate(String templateId, String newName);

    /**
     * 根据标签查询模板
     *
     * @param tags 标签列表
     * @return 模板列表
     */
    List<JimuReportTemplate> getTemplatesByTags(List<String> tags);

    /**
     * 搜索模板
     *
     * @param keyword 关键词
     * @return 模板列表
     */
    List<JimuReportTemplate> searchTemplates(String keyword);

    /**
     * 导入模板
     *
     * @param templateConfig 模板配置
     * @return 模板ID
     */
    String importTemplate(Map<String, Object> templateConfig);

    /**
     * 导出模板
     *
     * @param templateId 模板ID
     * @return 模板配置
     */
    Map<String, Object> exportTemplate(String templateId);

    /**
     * 初始化默认模板
     */
    void initDefaultTemplates();

    /**
     * 模板验证
     *
     * @param templateConfig 模板配置
     * @return 验证结果
     */
    Map<String, Object> validateTemplate(Map<String, Object> templateConfig);
}
