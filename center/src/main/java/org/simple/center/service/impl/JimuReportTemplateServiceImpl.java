package org.simple.center.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.simple.base.util.AuthUtil;
import org.simple.center.entity.JimuReportTemplate;
import org.simple.center.mapper.JimuReportTemplateMapper;
import org.simple.center.service.JimuReportTemplateService;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 积木报表模板服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Slf4j
@Service
public class JimuReportTemplateServiceImpl extends ServiceImpl<JimuReportTemplateMapper, JimuReportTemplate> implements JimuReportTemplateService {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public IPage<JimuReportTemplate> selectTemplatePage(Page<JimuReportTemplate> page, JimuReportTemplate template) {
        QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        
        if (StringUtils.hasText(template.getTemplateName())) {
            queryWrapper.like("template_name", template.getTemplateName());
        }
        if (StringUtils.hasText(template.getTemplateCategory())) {
            queryWrapper.eq("template_category", template.getTemplateCategory());
        }
        if (template.getStatus() != null) {
            queryWrapper.eq("status", template.getStatus());
        }
        if (template.getIsBuiltin() != null) {
            queryWrapper.eq("is_builtin", template.getIsBuiltin());
        }
        
        queryWrapper.orderByDesc("use_count").orderByDesc("create_time");
        
        return this.page(page, queryWrapper);
    }

    @Override
    public List<JimuReportTemplate> getTemplatesByCategory(String category) {
        QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0)
                   .eq("status", 1);
        
        if (StringUtils.hasText(category)) {
            queryWrapper.eq("template_category", category);
        }
        
        queryWrapper.orderByDesc("use_count").orderByDesc("create_time");
        
        return this.list(queryWrapper);
    }

    @Override
    public List<JimuReportTemplate> getPopularTemplates(Integer limit) {
        QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0)
                   .eq("status", 1)
                   .orderByDesc("use_count")
                   .orderByDesc("create_time");
        
        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }
        
        return this.list(queryWrapper);
    }

    @Override
    public Map<String, Object> getTemplateStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 模板总数
        QueryWrapper<JimuReportTemplate> totalQuery = new QueryWrapper<>();
        totalQuery.eq("del_flag", 0);
        long totalCount = this.count(totalQuery);
        statistics.put("totalCount", totalCount);
        
        // 启用模板数
        QueryWrapper<JimuReportTemplate> activeQuery = new QueryWrapper<>();
        activeQuery.eq("del_flag", 0).eq("status", 1);
        long activeCount = this.count(activeQuery);
        statistics.put("activeCount", activeCount);
        
        // 内置模板数
        QueryWrapper<JimuReportTemplate> builtinQuery = new QueryWrapper<>();
        builtinQuery.eq("del_flag", 0).eq("is_builtin", 1);
        long builtinCount = this.count(builtinQuery);
        statistics.put("builtinCount", builtinCount);
        
        // 自定义模板数
        QueryWrapper<JimuReportTemplate> customQuery = new QueryWrapper<>();
        customQuery.eq("del_flag", 0).eq("is_builtin", 0);
        long customCount = this.count(customQuery);
        statistics.put("customCount", customCount);
        
        // 按分类统计
        List<Map<String, Object>> categoryStats = baseMapper.selectMaps(
            new QueryWrapper<JimuReportTemplate>()
                .select("template_category, COUNT(*) as count")
                .eq("del_flag", 0)
                .eq("status", 1)
                .groupBy("template_category")
        );
        statistics.put("categoryStats", categoryStats);
        
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean useTemplate(String templateId) {
        try {
            return baseMapper.increaseUseCount(templateId) > 0;
        } catch (Exception e) {
            log.error("更新模板使用次数失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyTemplate(String templateId, String newName) {
        JimuReportTemplate originalTemplate = this.getById(templateId);
        if (originalTemplate == null) {
            throw new RuntimeException("模板不存在");
        }
        
        JimuReportTemplate newTemplate = new JimuReportTemplate();
        newTemplate.setTemplateId(UUID.randomUUID().toString().replace("-", ""));
        newTemplate.setTemplateName(newName);
        newTemplate.setTemplateCode(originalTemplate.getTemplateCode() + "_copy");
        newTemplate.setTemplateCategory(originalTemplate.getTemplateCategory());
        newTemplate.setTemplateDesc(originalTemplate.getTemplateDesc());
        newTemplate.setTemplateConfig(originalTemplate.getTemplateConfig());
        newTemplate.setPreviewImage(originalTemplate.getPreviewImage());
        newTemplate.setIsBuiltin(0); // 复制的模板不是内置模板
        newTemplate.setUseCount(0);
        newTemplate.setTemplateTags(originalTemplate.getTemplateTags());
        newTemplate.setApplicableScene(originalTemplate.getApplicableScene());
        newTemplate.setTemplateVersion("1.0");
        newTemplate.setStatus(1);
        newTemplate.setSort(originalTemplate.getSort());
        newTemplate.setTenantId(AuthUtil.getUser().getTenantId());
        newTemplate.setDelFlag(0);
        newTemplate.setCreateBy(AuthUtil.getUserId());
        newTemplate.setCreateTime(LocalDateTime.now());
        
        this.save(newTemplate);
        return newTemplate.getTemplateId();
    }

    @Override
    public List<JimuReportTemplate> getTemplatesByTags(List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            return new ArrayList<>();
        }
        
        QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0)
                   .eq("status", 1);
        
        // 根据标签查询（包含任意一个标签）
        StringBuilder tagCondition = new StringBuilder();
        for (int i = 0; i < tags.size(); i++) {
            if (i > 0) {
                tagCondition.append(" OR ");
            }
            tagCondition.append("template_tags LIKE '%").append(tags.get(i)).append("%'");
        }
        queryWrapper.and(wrapper -> wrapper.apply(tagCondition.toString()));
        
        queryWrapper.orderByDesc("use_count").orderByDesc("create_time");
        
        return this.list(queryWrapper);
    }

    @Override
    public List<JimuReportTemplate> searchTemplates(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return new ArrayList<>();
        }
        
        QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0)
                   .eq("status", 1)
                   .and(wrapper -> wrapper
                       .like("template_name", keyword)
                       .or()
                       .like("template_desc", keyword)
                       .or()
                       .like("template_tags", keyword)
                   )
                   .orderByDesc("use_count")
                   .orderByDesc("create_time");
        
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importTemplate(Map<String, Object> templateConfig) {
        try {
            String templateId = UUID.randomUUID().toString().replace("-", "");
            
            JimuReportTemplate template = new JimuReportTemplate();
            template.setTemplateId(templateId);
            template.setTemplateName((String) templateConfig.get("name"));
            template.setTemplateCode((String) templateConfig.get("code"));
            template.setTemplateCategory((String) templateConfig.getOrDefault("category", "CUSTOM"));
            template.setTemplateDesc((String) templateConfig.get("description"));
            template.setTemplateConfig(objectMapper.writeValueAsString(templateConfig));
            template.setPreviewImage((String) templateConfig.get("previewImage"));
            template.setIsBuiltin(0); // 导入的模板不是内置模板
            template.setUseCount(0);
            template.setTemplateTags((String) templateConfig.get("tags"));
            template.setApplicableScene((String) templateConfig.get("applicableScene"));
            template.setTemplateVersion((String) templateConfig.getOrDefault("version", "1.0"));
            template.setStatus(1);
            template.setSort(0);
            template.setTenantId(AuthUtil.getUser().getTenantId());
            template.setDelFlag(0);
            template.setCreateBy(AuthUtil.getUserId());
            template.setCreateTime(LocalDateTime.now());
            
            this.save(template);
            return templateId;
            
        } catch (Exception e) {
            log.error("导入模板失败", e);
            throw new RuntimeException("导入模板失败");
        }
    }

    @Override
    public Map<String, Object> exportTemplate(String templateId) {
        JimuReportTemplate template = this.getById(templateId);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }
        
        Map<String, Object> exportData = new HashMap<>();
        exportData.put("id", template.getTemplateId());
        exportData.put("name", template.getTemplateName());
        exportData.put("code", template.getTemplateCode());
        exportData.put("category", template.getTemplateCategory());
        exportData.put("description", template.getTemplateDesc());
        exportData.put("previewImage", template.getPreviewImage());
        exportData.put("tags", template.getTemplateTags());
        exportData.put("applicableScene", template.getApplicableScene());
        exportData.put("version", template.getTemplateVersion());
        exportData.put("createTime", template.getCreateTime());
        
        // 模板配置
        try {
            if (StringUtils.hasText(template.getTemplateConfig())) {
                Map<String, Object> config = objectMapper.readValue(template.getTemplateConfig(), Map.class);
                exportData.put("config", config);
            }
        } catch (Exception e) {
            log.error("解析模板配置失败", e);
        }
        
        return exportData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initDefaultTemplates() {
        log.info("开始初始化积木报表默认模板...");
        
        try {
            // 初始化设备统计报表模板
            initAssetStatisticsTemplate();
            
            // 初始化点检报告模板
            initInspectionReportTemplate();
            
            // 初始化维修报告模板
            initMaintenanceReportTemplate();
            
            // 初始化审核统计报表模板
            initApprovalStatisticsTemplate();
            
            log.info("积木报表默认模板初始化完成");
            
        } catch (Exception e) {
            log.error("初始化默认模板失败", e);
        }
    }

    @Override
    public Map<String, Object> validateTemplate(Map<String, Object> templateConfig) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 验证必需字段
        if (!templateConfig.containsKey("name") || !StringUtils.hasText((String) templateConfig.get("name"))) {
            errors.add("模板名称不能为空");
        }
        
        if (!templateConfig.containsKey("code") || !StringUtils.hasText((String) templateConfig.get("code"))) {
            errors.add("模板编码不能为空");
        }
        
        // 验证模板编码唯一性
        if (templateConfig.containsKey("code")) {
            String code = (String) templateConfig.get("code");
            QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("template_code", code)
                       .eq("del_flag", 0);
            
            if (this.count(queryWrapper) > 0) {
                errors.add("模板编码已存在");
            }
        }
        
        // 验证模板配置
        if (templateConfig.containsKey("config")) {
            try {
                objectMapper.writeValueAsString(templateConfig.get("config"));
            } catch (Exception e) {
                errors.add("模板配置格式错误");
            }
        }
        
        // 验证模板分类
        if (!templateConfig.containsKey("category")) {
            warnings.add("建议指定模板分类");
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        
        return result;
    }

    // ==================== 私有方法 ====================

    private void initAssetStatisticsTemplate() {
        String templateId = "asset_statistics_template";
        
        // 检查是否已存在
        QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        if (this.count(queryWrapper) > 0) {
            return;
        }
        
        JimuReportTemplate template = new JimuReportTemplate();
        template.setTemplateId(templateId);
        template.setTemplateName("设备统计报表模板");
        template.setTemplateCode("asset_statistics");
        template.setTemplateCategory("ASSET");
        template.setTemplateDesc("设备统计分析报表模板，包含设备数量、状态分布、分类统计等");
        template.setTemplateConfig(createAssetStatisticsConfig());
        template.setIsBuiltin(1);
        template.setUseCount(0);
        template.setTemplateTags("设备,统计,图表");
        template.setApplicableScene("设备管理,统计分析");
        template.setTemplateVersion("1.0");
        template.setStatus(1);
        template.setSort(1);
        template.setTenantId("default");
        template.setDelFlag(0);
        template.setCreateBy("system");
        template.setCreateTime(LocalDateTime.now());
        
        this.save(template);
    }

    private void initInspectionReportTemplate() {
        String templateId = "inspection_report_template";
        
        QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        if (this.count(queryWrapper) > 0) {
            return;
        }
        
        JimuReportTemplate template = new JimuReportTemplate();
        template.setTemplateId(templateId);
        template.setTemplateName("点检报告模板");
        template.setTemplateCode("inspection_report");
        template.setTemplateCategory("INSPECTION");
        template.setTemplateDesc("设备点检报告模板，包含点检任务统计、质量分析等");
        template.setTemplateConfig(createInspectionReportConfig());
        template.setIsBuiltin(1);
        template.setUseCount(0);
        template.setTemplateTags("点检,报告,质量");
        template.setApplicableScene("点检管理,质量分析");
        template.setTemplateVersion("1.0");
        template.setStatus(1);
        template.setSort(2);
        template.setTenantId("default");
        template.setDelFlag(0);
        template.setCreateBy("system");
        template.setCreateTime(LocalDateTime.now());
        
        this.save(template);
    }

    private void initMaintenanceReportTemplate() {
        String templateId = "maintenance_report_template";
        
        QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        if (this.count(queryWrapper) > 0) {
            return;
        }
        
        JimuReportTemplate template = new JimuReportTemplate();
        template.setTemplateId(templateId);
        template.setTemplateName("维修报告模板");
        template.setTemplateCode("maintenance_report");
        template.setTemplateCategory("MAINTENANCE");
        template.setTemplateDesc("设备维修报告模板，包含维修工单统计、效率分析等");
        template.setTemplateConfig(createMaintenanceReportConfig());
        template.setIsBuiltin(1);
        template.setUseCount(0);
        template.setTemplateTags("维修,报告,效率");
        template.setApplicableScene("维修管理,效率分析");
        template.setTemplateVersion("1.0");
        template.setStatus(1);
        template.setSort(3);
        template.setTenantId("default");
        template.setDelFlag(0);
        template.setCreateBy("system");
        template.setCreateTime(LocalDateTime.now());
        
        this.save(template);
    }

    private void initApprovalStatisticsTemplate() {
        String templateId = "approval_statistics_template";
        
        QueryWrapper<JimuReportTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        if (this.count(queryWrapper) > 0) {
            return;
        }
        
        JimuReportTemplate template = new JimuReportTemplate();
        template.setTemplateId(templateId);
        template.setTemplateName("审核统计报表模板");
        template.setTemplateCode("approval_statistics");
        template.setTemplateCategory("STATISTICS");
        template.setTemplateDesc("审核统计分析报表模板，包含审核统计、质量分布等");
        template.setTemplateConfig(createApprovalStatisticsConfig());
        template.setIsBuiltin(1);
        template.setUseCount(0);
        template.setTemplateTags("审核,统计,质量");
        template.setApplicableScene("审核管理,统计分析");
        template.setTemplateVersion("1.0");
        template.setStatus(1);
        template.setSort(4);
        template.setTenantId("default");
        template.setDelFlag(0);
        template.setCreateBy("system");
        template.setCreateTime(LocalDateTime.now());
        
        this.save(template);
    }

    private String createAssetStatisticsConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("type", "dashboard");
        config.put("title", "设备统计报表");
        config.put("dataSource", "asset_statistics");
        config.put("layout", "grid");
        config.put("components", Arrays.asList(
            Map.of("type", "card", "title", "设备总数", "field", "totalAssets"),
            Map.of("type", "pie", "title", "设备状态分布", "field", "statusDistribution"),
            Map.of("type", "bar", "title", "设备分类统计", "field", "categoryDistribution"),
            Map.of("type", "line", "title", "设备使用年限", "field", "ageDistribution")
        ));
        
        try {
            return objectMapper.writeValueAsString(config);
        } catch (Exception e) {
            log.error("创建设备统计模板配置失败", e);
            return "{}";
        }
    }

    private String createInspectionReportConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("type", "report");
        config.put("title", "点检报告");
        config.put("dataSource", "inspection_report");
        config.put("layout", "table");
        config.put("components", Arrays.asList(
            Map.of("type", "table", "title", "点检任务统计", "field", "taskStatistics"),
            Map.of("type", "chart", "title", "点检质量分析", "field", "qualityStatistics")
        ));
        
        try {
            return objectMapper.writeValueAsString(config);
        } catch (Exception e) {
            log.error("创建点检报告模板配置失败", e);
            return "{}";
        }
    }

    private String createMaintenanceReportConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("type", "report");
        config.put("title", "维修报告");
        config.put("dataSource", "maintenance_report");
        config.put("layout", "mixed");
        config.put("components", Arrays.asList(
            Map.of("type", "table", "title", "维修工单统计", "field", "orderStatistics"),
            Map.of("type", "chart", "title", "维修效率分析", "field", "efficiencyStatistics")
        ));
        
        try {
            return objectMapper.writeValueAsString(config);
        } catch (Exception e) {
            log.error("创建维修报告模板配置失败", e);
            return "{}";
        }
    }

    private String createApprovalStatisticsConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("type", "dashboard");
        config.put("title", "审核统计报表");
        config.put("dataSource", "approval_statistics");
        config.put("layout", "grid");
        config.put("components", Arrays.asList(
            Map.of("type", "card", "title", "审核统计", "field", "approvalStatistics"),
            Map.of("type", "pie", "title", "质量等级分布", "field", "qualityDistribution"),
            Map.of("type", "bar", "title", "审核效率统计", "field", "efficiencyStatistics")
        ));
        
        try {
            return objectMapper.writeValueAsString(config);
        } catch (Exception e) {
            log.error("创建审核统计模板配置失败", e);
            return "{}";
        }
    }
}
