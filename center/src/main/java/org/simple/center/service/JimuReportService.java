package org.simple.center.service;

import java.util.List;
import java.util.Map;

/**
 * 积木报表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface JimuReportService {

    /**
     * 获取报表分类
     */
    List<Map<String, Object>> getReportCategories();

    /**
     * 获取报表模板列表
     */
    List<Map<String, Object>> getReportTemplates(String categoryId);

    /**
     * 创建报表模板
     */
    String createReportTemplate(Map<String, Object> templateData);

    /**
     * 获取报表数据
     */
    Map<String, Object> getReportData(String reportId, Map<String, Object> params);

    /**
     * 导出报表
     */
    String exportReport(String reportId, String exportType, Map<String, Object> params);

    /**
     * 获取报表统计信息
     */
    Map<String, Object> getReportStatistics();

    /**
     * 创建报表定时任务
     */
    void createReportSchedule(Map<String, Object> scheduleData);

    /**
     * 预览报表
     */
    String previewReport(String reportId, Map<String, Object> params);

    /**
     * 分享报表
     */
    String shareReport(String reportId, Map<String, Object> shareConfig);

    /**
     * 删除报表模板
     */
    void deleteReportTemplate(String templateId);

    /**
     * 复制报表
     */
    String copyReport(String reportId, Map<String, Object> copyConfig);

    /**
     * 初始化设备管理系统默认报表
     */
    void initDefaultReports();
}