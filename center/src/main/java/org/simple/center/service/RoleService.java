package org.simple.center.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.center.entity.Role;
import org.simple.center.entity.RoleMenu;

public interface RoleService extends IService<Role> {

    //删除角色
    FrResult<?> delRole(String id);

    FrResult<?> queryRoleMenu(String roleId);

    FrResult<?> insertRoleMenus(RoleMenu roleMenu);
}
