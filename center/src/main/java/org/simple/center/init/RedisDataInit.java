package org.simple.center.init;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.simple.base.constant.CommonConst;
import org.simple.base.constant.RedisConstant;
import org.simple.base.deserializer.DictDeserializer;
import org.simple.base.dto.EmailDto;
import org.simple.base.dto.OssDto;
import org.simple.base.dto.SmsDto;
import org.simple.base.util.DictUtil;
import org.simple.base.util.ObjectMapperUtil;
import org.simple.base.util.SysConfigUtil;
import org.simple.base.util.SysParamUtil;
import org.simple.base.vo.DictVo;
import org.simple.base.vo.SysParamVo;
import org.simple.center.entity.*;
import org.simple.center.service.*;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * redis数据初始化
 */
@Component
@Slf4j
public class RedisDataInit implements CommandLineRunner {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private OssService ossService;

    @Resource
    private EmailService emailService;

    @Resource
    private SmsService smsService;

    @Resource
    private DictionaryService dictionaryService;

    @Resource
    private SysParamService sysParamService;

    @Resource
    private SystemConfigService systemConfigService;

    private final static ObjectMapper objectMapper =
            ObjectMapperUtil.objectMapper();


    @Override
    public void run(String... args) throws Exception {
        log.info("初始化缓存数据...start");
        //初始化加载oss数据
        loadOssConfig();
        //初始化加载短信配置，短信模版数据
        loadSmsConfig();
        //初始化邮件配置数据
        loadEmailConfig();
        //初始化字典缓存数据
        loadDictConfig();
        //初始化系统参数数据
        loadSysParameter();
        //初始化系统参数
        loadSysConfig();
        log.info("初始化缓存数据...end");
    }

    private void loadSysConfig() {
        //开始初始化系统参数
        List<SysConfig> list = systemConfigService.list();
        List<String> newKeys = new ArrayList<>();
        list.forEach(obj -> {
            stringRedisTemplate.opsForValue().set(CommonConst.SIMPLE_SYSTEM_CONFIG +
                    obj.getConfigKey(), obj.getConfigValue());
            newKeys.add(CommonConst.SIMPLE_SYSTEM_CONFIG +
                    obj.getConfigKey());
        });
        //删除缓存中已经不存在的字典
        SysConfigUtil.delNotUsedKey(newKeys);
    }

    private void loadSysParameter() {
        //开始初始化系统参数
        List<SysParam> list = sysParamService.list();
        List<String> newKeys = new ArrayList<>();
        list.forEach(obj -> {
            try {
                SysParamVo sysParamVo = new SysParamVo();
                BeanUtil.copyProperties(obj, sysParamVo);
                redisTemplate.opsForValue().set(CommonConst.SIMPLE_PARAMS +
                        obj.getName(), objectMapper.writeValueAsString(sysParamVo));
                newKeys.add(CommonConst.SIMPLE_PARAMS +
                        obj.getName());
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });

        //删除缓存中已经不存在的字典
        SysParamUtil.delNotUsedKey(newKeys);
    }

    private void loadDictConfig() throws JsonProcessingException {
        //开始初始化字典缓存
        Dictionary dictionary = new Dictionary();
        dictionary.setValue("#");
        List<Dictionary> dictionaryList = dictionaryService.list(Wrappers.query(dictionary));
        List<String> newKeys = new ArrayList<>();
        if (!dictionaryList.isEmpty()) {
            SimpleModule simpleModule = new SimpleModule();
            simpleModule.addDeserializer(List.class, new DictDeserializer());
            objectMapper.registerModules(simpleModule);
            for (Dictionary item : dictionaryList) {
                Dictionary d = new Dictionary();
                d.setCode(item.getCode());
                List<Dictionary> dicts =
                        dictionaryService.list(Wrappers.query(d)
                                .notIn("value", "#")
                                .orderByAsc("sort,id"));
                List<DictVo> list = new ArrayList<>();
                if (!dicts.isEmpty()) {
                    for (Dictionary item1 : dicts) {
                        DictVo dictVo = new DictVo();
                        BeanUtil.copyProperties(item1, dictVo);
                        list.add(dictVo);
                    }
                    newKeys.add(CommonConst.SIMPLE_DICT + item.getCode());
                    redisTemplate.opsForValue().set(
                            CommonConst.SIMPLE_DICT + item.getCode(),
                            objectMapper.writeValueAsString(list));
                }
            }
        }

        //删除缓存中已经不存在的字典
        DictUtil.delNotUsedKey(newKeys);
    }

    private void loadSmsConfig() {
        List<Sms> smsList = smsService.list();
        if (CollectionUtil.isNotEmpty(smsList)) {
            smsList.forEach(sms -> {
                SmsDto smsDto = new SmsDto();
                smsDto.setAppid(sms.getAppid());
                smsDto.setEndpoint(sms.getEndpoint());
                smsDto.setSecretId(sms.getSecretId());
                smsDto.setSecretKey(sms.getSecretKey());
                smsDto.setSign(sms.getSign());
                if (sms.getType().equals("ALI")) {
                    redisTemplate.opsForHash().putAll(RedisConstant.SMS_ALI,
                            BeanUtil.beanToMap(smsDto));
                } else if (sms.getType().equals("TENCENT")) {
                    redisTemplate.opsForHash().putAll(RedisConstant.SMS_TENCENT,
                            BeanUtil.beanToMap(smsDto));
                }
            });
        }
    }

    private void loadEmailConfig() {
        List<Email> emails = emailService.list();
        if (CollectionUtil.isNotEmpty(emails)) {
            Email email = emails.get(0);
            EmailDto emailDto = new EmailDto();
            emailDto.setHost(email.getHost());
            emailDto.setPort(email.getPort());
            emailDto.setSiteName(email.getSiteName());
            emailDto.setUserName(email.getUserName());
            emailDto.setPassWord(email.getPassWord());
            emailDto.setIsSsl(email.getIsSsl());
            redisTemplate.opsForHash().putAll(RedisConstant.EMAIL_PIX, BeanUtil.beanToMap(emailDto));
        }
    }

    private void loadOssConfig() {
        List<Oss> ossList = ossService.list();
        if (CollectionUtil.isNotEmpty(ossList)) {
            ossList.forEach(oss -> {
                OssDto o = new OssDto();
                o.setAccessKeySecret(oss.getAccessorySecret());
                o.setAccessKeyId(oss.getAccessoryId());
                o.setAppid(oss.getAppid());
                o.setRegion(oss.getRegion());
                o.setEndpoint(oss.getEndpoint());
                o.setWorkspace(oss.getWorkspace());
                switch (oss.getType()) {
                    case "ALIOSS":
                        if (StringUtils.isNotEmpty(oss.getIsUse()) && oss.getIsUse().equals("1")) {
                            redisTemplate.opsForValue().set(RedisConstant.USEOSS_PIX, RedisConstant.ALIOSS_PIX);
                        }
                        redisTemplate.opsForHash().putAll(RedisConstant.ALIOSS_PIX, BeanUtil.beanToMap(o));
                        break;
                    case "TENCENTCOS":
                        if (StringUtils.isNotEmpty(oss.getIsUse()) && oss.getIsUse().equals("1")) {
                            redisTemplate.opsForValue().set(RedisConstant.USEOSS_PIX, RedisConstant.TENCENT_PIX);
                        }
                        redisTemplate.opsForHash().putAll(RedisConstant.TENCENT_PIX, BeanUtil.beanToMap(o));
                        break;
                    case "MINIO":
                        if (StringUtils.isNotEmpty(oss.getIsUse()) && oss.getIsUse().equals("1")) {
                            redisTemplate.opsForValue().set(RedisConstant.USEOSS_PIX, RedisConstant.MINIO_PIX);
                        }
                        redisTemplate.opsForHash().putAll(RedisConstant.MINIO_PIX, BeanUtil.beanToMap(o));
                        break;
                    case "FTP":
                        if (StringUtils.isNotEmpty(oss.getIsUse()) && oss.getIsUse().equals("1")) {
                            redisTemplate.opsForValue().set(RedisConstant.USEOSS_PIX, RedisConstant.FTPOSS_PIX);
                        }
                        redisTemplate.opsForHash().putAll(RedisConstant.FTPOSS_PIX, BeanUtil.beanToMap(o));
                        break;
                    case "BASE":
                        if (StringUtils.isNotEmpty(oss.getIsUse()) && oss.getIsUse().equals("1")) {
                            redisTemplate.opsForValue().set(RedisConstant.USEOSS_PIX, RedisConstant.BASEOSS_PIX);
                        }
                        redisTemplate.opsForHash().putAll(RedisConstant.BASEOSS_PIX, BeanUtil.beanToMap(o));
                        break;
                }
            });
        }
    }
}
