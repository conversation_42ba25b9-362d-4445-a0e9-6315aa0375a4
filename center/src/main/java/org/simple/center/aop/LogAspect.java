package org.simple.center.aop;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.Order;
import org.simple.base.util.AuthUtil;
import org.simple.base.vo.FrResult;
import org.simple.center.entity.Logs;
import org.simple.center.event.LogEvent;
import org.simple.center.event.LogEventArgs;
import org.simple.center.service.LogsService;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 日志切面
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022-12-28
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
@Order(10)
public class LogAspect {
    private final ApplicationEventPublisher applicationEventPublisher;
    private final LogsService logsService;

    List<String> excludeRequestList = new ArrayList<>(Arrays.asList(
            "org.simple.openapi.controller.OpenController.openApi",
            "org.simple.center.controller.LogsController.logsList"));


    @Pointcut("execution(* org.simple.*.controller..*(..))")
    private void log() {
    }

    /**
     * 环绕通知，在目标函数执行中执行，可控制目标函数是否执行
     *
     * @param proceedingJoinPoint 执行的切点
     * @return 无
     * @throws Throwable 错误信息
     */
    @Around("log()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        String method = proceedingJoinPoint.getSignature().getDeclaringTypeName() + "." + proceedingJoinPoint.getSignature().getName();
        Object result = null;
        if (!excludeRequestList.contains(method)) {
            long startTime = System.currentTimeMillis();
            Logs logsData = new Logs();
            try {
                HttpServletRequest request = ((ServletRequestAttributes)
                        (RequestContextHolder.currentRequestAttributes()))
                        .getRequest();
                MethodSignature ms = (MethodSignature) proceedingJoinPoint.getSignature();
                Class<?> targetCls = proceedingJoinPoint.getTarget().getClass();
                //获取目标方法上的注解指定的操作名称
                Method targetMethod = targetCls.getDeclaredMethod(ms.getName(), ms.getParameterTypes());
                Operation apiOperation = targetMethod.getAnnotation(Operation.class);
                String summary = ObjectUtil.isNotNull(apiOperation) ? apiOperation.summary() : "";
                logsData = logsService.getLogData(summary, StpUtil.isLogin() ? AuthUtil.getUserId() : "匿名游客", null, request);
                if(StpUtil.isLogin()){
                    logsData.setUsername(AuthUtil.getUserId());
                }
                result = proceedingJoinPoint.proceed();
                logsData.setTime(String.valueOf((System.currentTimeMillis() - startTime)));
                if (result instanceof FrResult) {
                    FrResult frResult = (FrResult) result;
                    if (frResult.getCode() != 0) {
                        logsData.setStatus("1");
                        logsData.setError(frResult.getMsg());
                    }
                }
                log.info("{}", JSONUtil.toJsonStr(logsData));
                log.info("返回结果：{}", JSONObject.toJSONString(result));
                applicationEventPublisher.publishEvent(new LogEvent<>(
                        new LogEventArgs<>(JSONUtil.toJsonStr(logsData)),requestAttributes));
                return result;
            } catch (Exception ex) {
                log.error("异常错误信息：", ex);
                logsData.setTime(String.valueOf((System.currentTimeMillis() - startTime)));
                logsData.setStatus("1");
                logsData.setError(ex.getMessage());
                // 错误日志
                applicationEventPublisher.publishEvent(new LogEvent<>(
                        new LogEventArgs<>(JSONUtil.toJsonStr(logsData)),requestAttributes));
               throw ex;
            }
        } else {
            result = proceedingJoinPoint.proceed();
        }
        return result;
    }
}
