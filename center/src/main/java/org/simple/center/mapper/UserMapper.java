package org.simple.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import org.simple.base.dto.mybatis.Page;
import org.apache.ibatis.annotations.*;
import org.simple.center.dto.UserDto;
import org.simple.center.entity.Menu;
import org.simple.center.entity.User;

import java.util.List;
@Mapper
public interface UserMapper extends BaseMapper<User> {

    @Select("select t1.* from center_rolemenu t join center_menu t1 on t1.id = t.menu_id" +
            " where t.role_id in ( select  role_id from center_roleuser where  " +
            "user_id  = #{userId}) and t1.type ='c' and t1.status = '0' order by t1.sort asc")
    @Results({
            @Result(column = "meta", property = "meta", typeHandler = JacksonTypeHandler.class)
    })
    List<Menu> getUserMenu(@Param("userId") String userId);


    @Delete("delete from center_roleuser where user_id = #{userId}")
    void delRoleUser(@Param("userId") String userId);

    @Delete("delete from center_usertenant where user_id = #{userId}")
    void delUserTenant(@Param("userId") String userId);

    @Insert("insert into center_usertenant(id,tenant_id,user_id) values(#{id},#{tenant},#{user})")
    void insertUserTenant(@Param("id") String id, @Param("tenant") String tenant, @Param("user") String user);

    @Insert("insert into center_roleuser(id,role_id,user_id) values(#{id},#{role},#{user})")
    void insertRoleUser(@Param("id") String id, @Param("role") String role, @Param("user") String user);

    IPage<List<UserDto>> listAll(Page<UserDto> page, @Param("user") User user);

    @Select("select t1.auth_code from center_rolemenu t join center_menu t1 on t1.id = t.menu_id" +
            " where t.role_id in ( select  role_id from center_roleuser where user_id  = #{userId}) " +
            " and t1.type = 'b'")
    List<String> selectPermissionsByUserId(@Param("userId") String uerId);

    @Select("select role_id from center_roleuser where user_id = #{id}")
    List<String> selectRoles(@Param("id") String id);

}
