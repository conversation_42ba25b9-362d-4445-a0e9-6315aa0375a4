package org.simple.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import org.apache.ibatis.annotations.*;
import org.simple.center.entity.Menu;

import java.util.List;

@Mapper
public interface MenuMapper extends BaseMapper<Menu> {

    @Select("select t1.* from center_menu t1 where t1.type = 'c' order by t1.sort asc")
    @Results({
            @Result(column = "meta", property = "meta", typeHandler = JacksonTypeHandler.class)
    })
    List<Menu> getTreeMenuAll();


    @Select("select t1.* from center_menu t1 order by t1.sort asc")
    @Results({
            @Result(column = "meta", property = "meta", typeHandler = JacksonTypeHandler.class)
    })
    List<Menu> getRoleMenuAll();

    @Select("select t.id,t.auth_code,t.parent_id, t.name,t.type,t.meta,t.sort,t.component from (\n" +
            " with recursive cte as ( \n" +
            "                                      select * from center_menu  \n" +
            "                                     where id in (select id from center_menu where type = 'b') \n" +
            "                                     union all  \n" +
            "                                      select t.* from center_menu t   \n" +
            "                                     join cte t1 on t1.parent_id = t.id \n" +
            "                                    ) \n" +
            "                                    select * from cte)t order by t.sort asc")
    @Results({
            @Result(column = "meta", property = "meta", typeHandler = JacksonTypeHandler.class)
    })
    List<Menu> getRoleMenuAll1();

    void delRoleMenuByMenu(@Param("menuList") List<String> menuList);
}
