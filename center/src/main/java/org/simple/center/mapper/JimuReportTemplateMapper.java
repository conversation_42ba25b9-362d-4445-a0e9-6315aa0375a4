package org.simple.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.center.entity.JimuReportTemplate;

import java.util.List;
import java.util.Map;

/**
 * 积木报表模板 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Mapper
public interface JimuReportTemplateMapper extends BaseMapper<JimuReportTemplate> {

    /**
     * 按分类查询模板列表
     *
     * @param category 分类
     * @return 模板列表
     */
    List<JimuReportTemplate> selectByCategory(@Param("category") String category);

    /**
     * 查询热门模板
     *
     * @param limit 限制数量
     * @return 模板列表
     */
    List<JimuReportTemplate> selectPopularTemplates(@Param("limit") Integer limit);

    /**
     * 查询模板统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> selectTemplateStatistics();

    /**
     * 更新模板使用次数
     *
     * @param templateId 模板ID
     * @return 影响行数
     */
    int increaseUseCount(@Param("templateId") String templateId);

    /**
     * 根据标签查询模板
     *
     * @param tags 标签列表
     * @return 模板列表
     */
    List<JimuReportTemplate> selectByTags(@Param("tags") List<String> tags);

    /**
     * 根据关键词搜索模板
     *
     * @param keyword 关键词
     * @return 模板列表
     */
    List<JimuReportTemplate> searchTemplates(@Param("keyword") String keyword);
}
