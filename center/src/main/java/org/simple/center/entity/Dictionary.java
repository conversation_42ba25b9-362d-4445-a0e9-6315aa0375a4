package org.simple.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.simple.base.dto.BaseEntity;

import java.io.Serial;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "center_dictionary")
@Schema(description = "系统字典信息表")
public class Dictionary extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(name = "id", description = "主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    @Schema(name = "code", description = "字典编号")
    private String code;
    @Schema(name = "value", description = "字段值")
    private String value;
    @Schema(name = "label", description = "显示值")
    private String label;
    @Schema(name = "sort", description = "排序")
    private String sort;
}
