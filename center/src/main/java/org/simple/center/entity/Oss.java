package org.simple.center.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.simple.base.dto.BaseEntity;

import java.io.Serial;
import java.io.Serializable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "oss存储信息")
@TableName(value = "center_oss", autoResultMap = true)
public class Oss extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(name = "id", description = "主键ID")
    private String id;
    @Schema(name = "endpoint", description = "地址")
    private String endpoint;
    @Schema(name = "workspace", description = "工作空间")
    private String workspace;
    @Schema(name = "accessoryId", description = "accessoryId")
    private String accessoryId;
    @Schema(name = "accessorySecret", description = "accessorySecret")
    private String accessorySecret;
    @Schema(name = "appid", description = "appid")
    private String appid;
    @Schema(name = "region", description = "地区")
    private String region;
    @Schema(name = "type", description = "oss类型；阿里，腾讯，minio")
    private String type;
    @Schema(name = "isUse", description = "是否启用")
    private String isUse;
}
