package org.simple.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import mybatis.mate.annotation.FieldEncrypt;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Accessors(chain = true)
@TableName(value = "center_user", autoResultMap = true)
@Schema(description = "用户信息")
public class User implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(name = "id", description = "用户ID")
    private String id;
    @Schema(name = "user_name", description = "登录名")
    private String userName;
    @Schema(name = "password", description = "密码")
    private String password;
    @Schema(name = "nick_name", description = "昵称")
    private String nickName;
    @Schema(name = "phone", description = "手机号")
    @FieldEncrypt
    private String phone;
    @Schema(name = "email", description = "电子邮箱")
    @FieldEncrypt
    private String email;
    @Schema(name = "status", description = "状态")
    private String status;
    @Schema(name = "avatar", description = "头像")
    private String avatar;
    @Schema(name = "create_date", description = "创建日期")
    private LocalDateTime createDate;
    @Schema(name = "update_date", description = "修改日期")
    private LocalDateTime updateDate;
    @Schema(name = "login_date", description = "最后登录日期")
    private LocalDateTime loginDate;
    @Schema(name = "tenant_id", description = "关联主机构")
    private String tenantId;
    @Schema(name = "error", description = "密码错误次数")
    private Integer error;
    @Schema(name = "organ", description = "关联组织机构ID")
    private String organ;
    @Schema(name = "type", description = "用户类型")
    private String type;

}