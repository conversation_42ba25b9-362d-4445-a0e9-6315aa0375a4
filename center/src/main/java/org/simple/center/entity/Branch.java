package org.simple.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.simple.base.dto.BaseEntity;

import java.io.Serial;
import java.io.Serializable;


/**
 * @Copyright: simple
 * @Desc: 组织机构信息表实体
 * @Date: 2022-08-03 21:47:58
 * @Author: frSimple
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "center_branch")
@Schema(description = "组织机构信息")
public class Branch extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(name = "id", description = "主键ID")
    private String id;
    @Schema(name = "name", description = "组织名称")
    private String name;
    @Schema(name = "parentId", description = "父节点ID")
    private String parentId;
    @Schema(name = "sort", description = "排序")
    private Integer sort;
    @Schema(name = "tenantId", description = "机构ID")
    private String tenantId;

}
