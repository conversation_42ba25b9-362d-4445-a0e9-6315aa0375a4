package org.simple.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;



/**
 * @Copyright: frSimple
 * @Desc: 实体
 * @Date: 2025-06-17 10:22:28
 * @Author: frSimple
 */

@Data
@NoArgsConstructor
@TableName(value = "center_system_config")
@Schema(description ="")
public class SysConfig implements Serializable{
    @Serial
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description ="id")
    private String id;
    @Schema(description ="config_key")
    private String configKey;
    @Schema(description ="config_value")
    private String configValue;
}
