package org.simple.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;



/**
 * @Copyright: frSimple
 * @Desc: 密码修改日志实体
 * @Date: 2025-06-17 13:22:42
 * @Author: frSimple
 */

@Data
@NoArgsConstructor
@TableName(value = "center_password_log")
@Schema(description ="密码修改日志")
public class PasswordLog implements Serializable{
    @Serial
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description ="id")
    private String id;
    @Schema(description ="old_password")
    private String oldPassword;
    @Schema(description ="new_password")
    private String newPassword;
    @Schema(description ="user_id")
    private String userId;
    @Schema(description ="create_time")
    private LocalDateTime createTime;
    @Schema(description ="creator")
    private String creator;
    @Schema(description ="expire_time")
    private LocalDateTime expireTime;
}
