package org.simple.center.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Accessors(chain = true)
@Schema(description = "系统日志信息")
@TableName(value = "center_logs")
public class Logs implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(name = "id", description = "主键ID")
    private String id;
    @Schema(name = "serviceName", description = "请求服务")
    private String serviceName;
    @Schema(name = "resource", description = "资源")
    private String resource;
    @Schema(name = "ip", description = "ip地址")
    private String ip;
    @Schema(name = "path", description = "请求路径")
    private String path;
    @Schema(name = "method", description = "请求方法")
    private String method;
    @Schema(name = "useragent", description = "useragent")
    private String useragent;
    @Schema(name = "params", description = "请求参数")
    private String params;
    @Schema(name = "username", description = "请求人")
    private String username;
    @Schema(name = "time", description = "消耗时长")
    private String time;
    @Schema(name = "status", description = "请求状态")
    private String status;
    @Schema(name = "error", description = "错误信息")
    private String error;
    @Schema(name = "createTime", description = "请求时间")
    private LocalDateTime createTime;
}
