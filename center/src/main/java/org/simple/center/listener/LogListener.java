package org.simple.center.listener;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simple.center.entity.Logs;
import org.simple.center.event.LogEvent;
import org.simple.center.event.LogEventArgs;
import org.simple.center.service.LogsService;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * 监听日志事件处理
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022-12-28
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogListener {
    private final LogsService logsService;

    /**
     * 异步执行事件
     *
     * @param event 事件
     */
    @EventListener(LogEvent.class)
    @Async
    public void logListener(LogEvent<LogEventArgs<String>> event) {
        try {
            RequestContextHolder.setRequestAttributes(event.getRequestAttributes());
            LogEventArgs<String> args = (LogEventArgs<String>) event.getSource();
            String logEntity = JSONUtil.toJsonStr(args.getData());
            if (logEntity != null) {
                logsService.save(JSONUtil.toBean(logEntity, Logs.class));
            }
        } catch (Exception e) {
            log.error("执行错误: ", e);
        }
    }
}
