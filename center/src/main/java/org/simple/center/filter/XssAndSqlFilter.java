package org.simple.center.filter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.AntPathMatcher;
import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.simple.base.vo.FrResult;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/1 10:51
 * @description
 */
@WebFilter(filterName = "XssAndSqlFilter", urlPatterns = "/*", asyncSupported = true)
@ConfigurationProperties(prefix = "auth")
@EnableConfigurationProperties(XssAndSqlFilter.class)
public class XssAndSqlFilter implements Filter {

    /**
     * 忽略权限检查的url地址
     */
    @Getter
    @Setter
    private List<String> ignore;

    @SneakyThrows
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        //获取请求你ip后的全部路径
        HttpServletRequest req = (HttpServletRequest) request;
        String uri = req.getRequestURI();
        //跳过不需要的Xss校验的地址
        if (CollUtil.isNotEmpty(ignore)) {
            AntPathMatcher pathMatcher = new AntPathMatcher();
            for (int i = 0; i < ignore.size(); i++) {
                if (pathMatcher.match(ignore.get(i), uri)) {
                    chain.doFilter(request, response);
                    return;
                }
            }
        }

        String method = "GET";
        String param = "";
        XssAndSqlHttpServletRequestWrapper xssRequest = null;
        if (request instanceof HttpServletRequest) {
            method = ((HttpServletRequest) request).getMethod();
            xssRequest = new XssAndSqlHttpServletRequestWrapper((HttpServletRequest) request);
        }
        if ("POST".equalsIgnoreCase(method)) {
            param = this.getBodyString(xssRequest.getReader());
            if (StringUtils.isNotBlank(param)) {
                if (xssRequest.checkXSSAndSql(param)) {
                    response.setCharacterEncoding("UTF-8");
                    response.setContentType("application/json;charset=UTF-8");
                    PrintWriter out = response.getWriter();
                    out.write(JSONObject.toJSONString(
                            FrResult.xssAndSql()));
                    return;
                }
            }
        }
        if (xssRequest.checkParameter()) {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json;charset=UTF-8");
            PrintWriter out = response.getWriter();
            out.write(JSONObject.toJSONString(
                    FrResult.xssAndSql()));
            return;
        }
        chain.doFilter(xssRequest, response);
    }

    // 获取request请求body中参数
    public static String getBodyString(BufferedReader br) {
        String inputLine;
        String str = "";
        try {
            while ((inputLine = br.readLine()) != null) {
                str += inputLine;
            }
            br.close();
        } catch (IOException e) {
            System.out.println("IOException: " + e);
        }
        return str;

    }

    @Override
    public void destroy() {
    }

    @Override
    public void init(FilterConfig filterConfig1) throws ServletException {
    }
}