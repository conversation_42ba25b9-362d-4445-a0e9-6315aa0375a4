package org.simple.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.center.entity.Branch;
import org.simple.center.service.BranchService;
import org.springframework.web.bind.annotation.*;


/**
 * @Copyright: simple
 * @Desc:
 * @Date: 2022-08-03 21:47:58
 * @Author: frSimple
 */

@RestController
@RequestMapping("/center/branch")
@Tag(name ="组织机构管理" , description = "组织机构管理")
public class BranchController {

    @Resource
    private BranchService branchService;


    @GetMapping("queryOrganTree")
    @SaCheckPermission("system:organ:query")
    @Operation(summary = "根据机构ID查询组织机构树形")
    public FrResult<?> queryOrganTree(@RequestParam(required = false, name = "tenantId") String tenantId) {
        return FrResult.success(branchService.queryOrganTree(tenantId));
    }


    @PostMapping("editOrgan")
    @SaCheckPermission("system:organ:edit")
    @Operation(summary = "修改组织机构信息")
    public FrResult<?> editOrgan(@RequestBody Branch branch) {
        return FrResult.success(branchService.updateById(branch));
    }

    @PostMapping("addOrgan")
    @SaCheckPermission("system:organ:add")
    @Operation(summary = "添加组织机构信息")
    public FrResult<?> addOrgan(@RequestBody Branch branch) {
        branch.setId(RandomUtil.getOrganId());
        return FrResult.success(branchService.save(branch));
    }


    @DeleteMapping("delOrgan/{id}")
    @SaCheckPermission("system:organ:del")
    @Operation(summary = "删除组织机构")
    public FrResult<?> delOrgan(@PathVariable("id") String id) {
        Branch branch = new Branch();
        branch.setParentId(id);
        if (!branchService.list(Wrappers.query(branch)).isEmpty()) {
            return FrResult.failed("存在关联的下级组织，请先删除所有下级组织");
        }
        return FrResult.success(branchService.removeById(id));
    }

    @DeleteMapping("getOrgan")
    @SaCheckPermission("system:organ:query")
    @Operation(summary = "查询组织机构信息")
    public FrResult<?> getOrgan(@RequestParam("id") String id) {
        return FrResult.success(branchService.getById(id));
    }


}