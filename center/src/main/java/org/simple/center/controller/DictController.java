package org.simple.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.simple.base.constant.CommonConst;
import org.simple.base.deserializer.DictDeserializer;
import org.simple.base.dto.mybatis.Page;
import org.simple.base.util.DictUtil;
import org.simple.base.util.ObjectMapperUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.DictVo;
import org.simple.base.vo.FrResult;
import org.simple.center.entity.Dictionary;
import org.simple.center.service.DictionaryService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/center/dict")
@Tag(name ="组织机构管理" , description = "组织机构管理")
public class DictController {

    @Resource
    private DictionaryService dictionaryService;

    @Resource
    private RedisTemplate<String,Object> redisTemplate;

    private final static ObjectMapper objectMapper =
            ObjectMapperUtil.objectMapper();

    @GetMapping("list")
    @SaCheckPermission("system:dict:query")
    @Operation(summary = "查询字典信息列表")
    public FrResult<?> list(Page<Dictionary> page, Dictionary dictionary) {
        //只查询字典
        dictionary.setValue("#");
        String label = "";
        if (StringUtils.isNotEmpty(dictionary.getLabel())) {
            label = dictionary.getLabel();
            dictionary.setLabel("");
        }
        return FrResult.success(dictionaryService.page(page,
                Wrappers.query(dictionary).like("label", label)
                        .or().like("code", label)
                        .orderByDesc("create_time")));
    }

    @GetMapping("listModule")
    @SaCheckPermission("system:dict:query")
    @Operation(summary = "查询字典信息列表")
    public FrResult<?> listModule(Page<Dictionary> page, Dictionary dictionary) {
        //只查询字典
        dictionary.setValue("#");
        String code = "";
        if (StringUtils.isNotEmpty(dictionary.getCode())) {
            code = dictionary.getCode();
            dictionary.setCode("");
        }
        return FrResult.success(dictionaryService.page(page,
                Wrappers.query(dictionary).likeRight("code", code)
                        .orderByAsc("sort,id")));
    }

    @GetMapping("list1")
    @SaCheckPermission("system:dict:query")
    @Operation(summary = "查询字典项列表")
    public FrResult<?> list1(Dictionary dictionary) {
        String id = "";
        if (StringUtils.isNotEmpty(dictionary.getId())) {
            id = dictionary.getId();
        }
        dictionary.setId(null);
        dictionary.setValue("#");
        return FrResult.success(dictionaryService.list(Wrappers.query(dictionary).
                notIn("id", id).orderByAsc("sort,id")));
    }

    @GetMapping("values")
    @SaCheckPermission("system:dict:query")
    @Operation(summary = "查询字典key值列表")
    public FrResult<?> listValues(@RequestParam("code") String code) {
        Dictionary dictionary = new Dictionary();
        dictionary.setCode(code);
        List<Dictionary> dists = dictionaryService.list(
                Wrappers.query(dictionary).notIn("value", "#").orderByAsc("sort,id"));
        return FrResult.success(dists);
    }

    @GetMapping("cache/{code}")
    @Operation(summary = "查询字典key值列表")
    public FrResult<?> listValues1(@PathVariable("code") String code) {
        //先从缓存中拿
        List<DictVo> array = DictUtil.getDictByCode(code);
        if (!array.isEmpty()) {
            return FrResult.success(array);
        }
        Dictionary dictionary = new Dictionary();
        dictionary.setCode(code);
        List<Dictionary> dists = dictionaryService.list(
                Wrappers.query(dictionary).notIn("value", "#").orderByAsc("sort,id"));
        return FrResult.success(dists);
    }

    @PostMapping("addDict")
    @SaCheckPermission("system:dict:add")
    @Operation(summary = "添加字典")
    public FrResult<?> addDict(@RequestBody Dictionary dictionary) {
        dictionary.setId(RandomUtil.getDictId());
        dictionaryService.save(dictionary);
        return FrResult.successNodata("新增成功");
    }

    @PostMapping("editDict")
    @SaCheckPermission("system:dict:edit")
    @Operation(summary = "修改字典")
    public FrResult<?> editDict(@RequestBody Dictionary dictionary) {
        //清洗对象
        Dictionary d = dictionaryService.getById(dictionary.getId());
        if (d.getValue().equals("#")) {
            Dictionary d1 = new Dictionary();
            d1.setCode(d.getCode());
            List<Dictionary> list = dictionaryService.
                    list(Wrappers.query(d1));
            if (CollectionUtil.isNotEmpty(list)) {
                list.forEach(dict -> {
                    dict.setCode(dictionary.getCode());
                    dictionaryService.updateById(dict);
                });
            }
            d.setLabel(dictionary.getLabel());
            d.setCode(dictionary.getCode());
            dictionaryService.updateById(d);
        } else {
            d.setSort(dictionary.getSort());
            d.setLabel(dictionary.getLabel());
            d.setValue(dictionary.getValue());
            dictionaryService.updateById(d);
        }
        return FrResult.successNodata("修改成功");
    }

    @DeleteMapping("delDict")
    @SaCheckPermission("system:dict:del")
    @Operation(summary = "删除字典")
    public FrResult<?> delDict(@RequestParam("id") String id) {
        Dictionary d = dictionaryService.getById(id);
        if (d.getValue().equals("#")) {
            Dictionary dic = new Dictionary();
            dic.setCode(d.getCode());
            dictionaryService.remove(Wrappers.query(dic));
        } else {
            dictionaryService.removeById(id);
        }
        return FrResult.successNodata("删除成功");
    }

    @GetMapping("refDictCache")
    @SaCheckPermission("system:dict:query")
    @Operation(summary = "刷新字典缓存")
    public FrResult<?> refDictCache() throws JsonProcessingException {
        Dictionary dictionary = new Dictionary();
        dictionary.setValue("#");
        List<Dictionary> dictionaryList = dictionaryService.list(Wrappers.query(dictionary).orderByAsc("sort,id"));
        List<String> newKeys = new ArrayList<>();
        if (!dictionaryList.isEmpty()) {
            SimpleModule simpleModule = new SimpleModule();
            simpleModule.addDeserializer(List.class, new DictDeserializer());
            objectMapper.registerModules(simpleModule);
            for (Dictionary item : dictionaryList) {
                Dictionary d = new Dictionary();
                d.setCode(item.getCode());
                List<Dictionary> dicts =
                        dictionaryService.list(Wrappers.query(d).notIn("value", "#").orderByAsc("sort,id"));
                List<DictVo> list = new ArrayList<>();
                if (!dicts.isEmpty()) {
                    for (Dictionary item1 : dicts) {
                        DictVo dictVo = new DictVo();
                        BeanUtil.copyProperties(item1, dictVo);
                        list.add(dictVo);
                    }
                    redisTemplate.opsForValue().set(
                            CommonConst.SIMPLE_DICT + item.getCode(),
                            objectMapper.writeValueAsString(list));
                    newKeys.add(CommonConst.SIMPLE_DICT + item.getCode());
                }
            }
        }
        DictUtil.delNotUsedKey(newKeys);
        return FrResult.success("刷新完成!");
    }


}
