package org.simple.center.controller;

import org.simple.base.dto.mybatis.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.center.dto.LogsDto;
import org.simple.center.service.LogsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/center/logs")
@Tag(name ="操作日志管理" , description = "操作日志管理")
public class LogsController {

    @Resource
    private LogsService logsService;

    @GetMapping("list")
    public FrResult<?> logsList(Page<LogsDto> page, LogsDto logs) {
        return FrResult.success(logsService.logsList(page, logs));
    }

}
