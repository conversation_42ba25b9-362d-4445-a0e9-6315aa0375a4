package org.simple.center.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.simple.base.constant.RedisConstant;
import org.simple.base.dto.EmailDto;
import org.simple.base.sms.EmailUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.center.dto.EmailParams;
import org.simple.center.entity.Email;
import org.simple.center.service.EmailService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/center/email")
@Tag(name = "邮箱发送配置", description = "邮箱发送配置")
public class EmailController {

    @Resource
    private EmailService emailService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @GetMapping("emailCfg")
    public FrResult<?> getEmailCfg() {
        return FrResult.success(emailService.list());
    }

    @PostMapping("saveOrUpdate")
    public FrResult<?> saveOrUpdate(@RequestBody Email email) {
        if (StringUtils.isEmpty(email.getId())) {
            email.setId(RandomUtil.getEmailCfgId());
        }
        emailService.saveOrUpdate(email);
        //初始化缓存
        EmailDto emailDto = new EmailDto();
        emailDto.setHost(email.getHost());
        emailDto.setPort(email.getPort());
        emailDto.setSiteName(email.getSiteName());
        emailDto.setUserName(email.getUserName());
        emailDto.setPassWord(email.getPassWord());
        emailDto.setIsSsl(email.getIsSsl());
        redisTemplate.opsForHash().putAll(RedisConstant.EMAIL_PIX, BeanUtil.beanToMap(emailDto));
        redisTemplate.expire(RedisConstant.EMAIL_PIX, 300000000, TimeUnit.DAYS);
        return FrResult.successNodata("修改成功");
    }

    @PostMapping("sendEmail")
    public FrResult<?> sendEmail(EmailParams emailParams,
                                 @RequestParam(value = "files", required = false) MultipartFile[] files) {
        if (ObjectUtil.isEmpty(files)) {
            return EmailUtil.sendEmail(
                    emailParams.getTitle(), emailParams.getContent(),
                    emailParams.getTos().split(","), true, null);
        } else {
            File[] fileList = new File[files.length];
            if (files.length != 0) {
                for (int i = 0; i < files.length; i++) {
                    MultipartFile file = files[i];
                    try {
                        File f = null;
                        InputStream inputStream = file.getInputStream();
                        String originalFilename = file.getOriginalFilename();
                        f = new File(originalFilename);
                        FileUtil.writeFromStream(inputStream, f);
                        fileList[i] = f;
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            return EmailUtil.sendEmail(
                    emailParams.getTitle(), emailParams.getContent(),
                    emailParams.getTos().split(","), true, fileList);
        }

    }
}
