package org.simple.center.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.simple.base.constant.RedisConstant;
import org.simple.base.dto.mybatis.Page;
import org.simple.base.enums.UserType;
import org.simple.base.sms.EmailUtil;
import org.simple.base.sms.SmsUtil;
import org.simple.base.storage.OssUtil;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.base.util.Sm2Util;
import org.simple.center.dto.UserDto;
import org.simple.center.entity.File;
import org.simple.center.entity.User;
import org.simple.center.service.FileService;
import org.simple.center.service.UserService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/center/user")
@Tag(name ="用户管理" , description = "用户管理")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private RedisTemplate<String,Object> redisTemplate;

    @Resource
    private FileService fileService;

    @GetMapping("info")
    public FrResult<?> getUserInfo() {
        User user = userService.getById(AuthUtil.getUserId());
        Map<String, Object> userInfo =
                BeanUtil.beanToMap(AuthUtil.getUser());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("nickName", user.getNickName());
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("roles", StpUtil.getPermissionList(AuthUtil.getUserId()));
        result.put("user", userInfo);
        return FrResult.success(result);
    }


    @GetMapping("menu")
    public FrResult<?> getUserMenu() {
        return FrResult.success(userService.getUserMenu(AuthUtil.getUserId()));
    }

    @GetMapping("list")
    @SaCheckPermission("system:user:query")
    public FrResult<?> list(Page<UserDto> page, User user) {
        return FrResult.success(userService.listAll(page, user));
    }

    @GetMapping("list1")
    @SaCheckPermission("system:user:query")
    public FrResult<?> list1(User user) {
        String id = user.getId();
        user.setId(null);
        return FrResult.success(userService.list(Wrappers.query(user).notIn("id", id)));
    }

    @PostMapping("addUser")
    @SaCheckPermission("system:user:add")
    public FrResult<?> addUser(@RequestBody UserDto userDto) {
        String userid = RandomUtil.getUserId();
        User user = new User();
        user.setId(userid);
        user.setStatus("0");
        user.setError(0);
        user.setCreateDate(LocalDateTime.now());
        user.setUpdateDate(LocalDateTime.now());
        user.setPassword(Sm2Util.encrypt(userDto.getPassword()));
        user.setPhone(userDto.getPhone());
        user.setEmail(userDto.getEmail());
        user.setNickName(userDto.getNickName());
        user.setUserName(userDto.getUserName());
        user.setTenantId(userDto.getTenantId());
        user.setOrgan(userDto.getOrgan());
        user.setType(UserType.USER_SYSTEM.getValue());
        userService.save(user);
        userService.insertUserTenant(RandomUtil.getUserTenantId(),
                user.getTenantId(), userid);
        //for循环插入用户角色关联关系数据
        String[] roles = userDto.getRoles().split(",");
        for (String role : roles) {
            userService.insertRoleUser(RandomUtil.getRoleUserId(), role, userid);
        }
        return FrResult.successNodata("新增成功");
    }

    @PostMapping("editUser")
    @SaCheckPermission("system:user:edit")
    public FrResult<?> editUser(@RequestBody UserDto userDto) {
        return userService.updateUser(userDto);
    }

    @DeleteMapping("delUser/{id}")
    @SaCheckPermission("system:user:del")
    public FrResult<?> delUser(@PathVariable("id") String id) {
        if (id.equals("1")) {
            return FrResult.failed("不允许删除超级管理员");
        }
        return userService.delUser(id);
    }

    @GetMapping("lock/{id}")
    @SaCheckPermission("system:user:edit")
    public FrResult<?> lock(@PathVariable("id") String id) {
        User user = new User();
        user.setId(id);
        user.setStatus("1");
        userService.updateById(user);
        return FrResult.successNodata("账户已被锁定");
    }

    @GetMapping("unlock/{id}")
    @SaCheckPermission("system:user:edit")
    public FrResult<?> unlock(@PathVariable("id") String id) {
        User user = new User();
        user.setId(id);
        user.setStatus("0");
        userService.updateById(user);
        return FrResult.successNodata("账户已解锁");
    }

    @GetMapping("resetPwd/{id}")
    @SaCheckPermission("system:user:edit")
    public FrResult<?> resetPwd(@PathVariable("id") String id) {
        try {
            return FrResult.success(userService.resetPwd(id));
        } catch (Exception e) {
            return FrResult.failed(e.getMessage());
        }
    }

    @PostMapping("updatePwd")
    public FrResult<?> updatePwd(@RequestBody UserDto userDto) {
        try {
            userService.updatePwd(userDto);
            return FrResult.successNodata("账户密码修改成功");
        } catch (Exception e) {
            return FrResult.failed(e.getMessage());
        }
    }

    @PostMapping("checkPwd")
    public FrResult<?> checkPwd(@RequestBody UserDto userDto) {
        if (StringUtils.isEmpty(userDto.getId())) {
            userDto.setId(AuthUtil.getUserId());
        }
        User user = userService.getById(userDto.getId());
        if (!Sm2Util.decrypt(user.getPassword()).equals(userDto.getPassword())) {
            return FrResult.failed("密码错误");
        }
        return FrResult.successNodata("密码正确");
    }

    @PostMapping("updateAvatar")
    public FrResult<?> updateAvatar(@RequestParam("file") MultipartFile file) {
        //上传图片
        try{
            String id = RandomUtil.getFileId();
            FrResult<String> result = OssUtil.upLoad(
                    file,id,false);
            if(result.getCode() != 0){
                throw new Exception(result.getMsg());
            }
            String filePath = result.getData();
            org.simple.center.entity.File f = new File();
            f.setId(id);
            f.setFileName(file.getOriginalFilename());
            f.setFilePath(filePath);
            f.setFileSize(String.valueOf(file.getSize()));
            f.setStatus("0");
            f.setIsPub("1");
            fileService.save(f);
            //上传成功，头像用户头像
            User user = new User();
            user.setAvatar(id);
            user.setId(AuthUtil.getUserId());
            userService.updateById(user);
            return FrResult.success(id);
        }catch (Exception ex){
            return FrResult.failed(ex.getMessage());
        }
    }

    @GetMapping("queryUser")
    public FrResult<?> queryUser() {
        User user = userService.getById(AuthUtil.getUserId());
        User r = new User();
        r.setId(user.getId());
        r.setEmail(StrUtil.hide(user.getEmail(), 3, 5));
        r.setPhone(DesensitizedUtil.mobilePhone(user.getPhone()));
        r.setNickName(user.getNickName());
        return FrResult.success(r);
    }

    @PostMapping("updateUser")
    public FrResult<?> updateUser(@RequestBody User user) {
        if (!user.getId().equals(AuthUtil.getUserId())) {
            return FrResult.failed("非法操作");
        }
        return FrResult.success(userService.updateById(user));
    }


    @GetMapping("sendMsg")
    public FrResult<?> sendMsg(@RequestParam("phone") String phone) {
        //判断是否和当前手机号一直
        User phoneUser = new User();
        phoneUser.setPhone(phone);
        if (!userService.list(Wrappers.query(phoneUser)).isEmpty()) {
            return FrResult.failed("新手机号已注册，不能重复使用");
        }

        String key = RedisConstant.PHONE_UPDATE_CODE_STR + AuthUtil.getUserId();
        //校验是否发送过短信以免重复发送60秒只能发送一次
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            Long seconds = redisTemplate.opsForValue().getOperations().getExpire(key);
            if (seconds > 0) {
                return FrResult.failed("已获取过短信，请等待" + seconds + "秒之后在获取");
            }
        }
        //获取四位随机数字
        String rom = cn.hutool.core.util.RandomUtil.randomNumbers(4);
        try {
            FrResult<?> result = SmsUtil.getTencentSms().sendSms(null, "865723",
                    new String[]{rom}, new String[]{phone});
            if (result.getCode() == 1) {
                return FrResult.failed("发送失败：" + result.getMsg());
            }
        } catch (Exception e) {
            return FrResult.failed("发送失败：" + e.getMessage());
        }
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.opsForValue().set(RedisConstant.PHONE_UPDATE_CODE_STR + AuthUtil.getUserId(),
                rom + "_" + phone, RedisConstant.CODE_TIMEOUT, TimeUnit.SECONDS);
        return FrResult.successNodata("发送成功");
    }


    @GetMapping("updatePhone")
    public FrResult<?> updatePhone(@RequestParam("password") String password,
                                   @RequestParam("code") String code) {
        String key = RedisConstant.PHONE_UPDATE_CODE_STR + AuthUtil.getUserId();
        Object smsStr = redisTemplate.opsForValue().get(key);
        if (null == smsStr) {
            return FrResult.failed("验证码错误");
        }
        String smsCode = String.valueOf(smsStr).split("_")[0];
        String phone = String.valueOf(smsStr).split("_")[1];
        if (!code.equals(smsCode)) {
            return FrResult.failed("验证码错误");
        }
        User userEntityInfo = userService.getById(AuthUtil.getUserId());
        if (!Sm2Util.decrypt(userEntityInfo.getPassword()).equals(password)) {
            return FrResult.failed("用户密码错误");
        }
        //验证成功开始更新数据
        User user = new User();
        user.setId(AuthUtil.getUserId());
        user.setPhone(phone);
        userService.updateById(user);

        return FrResult.successNodata("关联手机绑定成功");
    }


    @GetMapping("sendEmail")
    public FrResult<?> sendEmail(@RequestParam("email") String email) {
        //判断是否和当前手机号一直
        User emailUser = new User();
        emailUser.setEmail(email);
        if (!userService.list(Wrappers.query(emailUser)).isEmpty()) {
            return FrResult.failed("新邮箱已注册，不能重复使用");
        }

        String key = RedisConstant.EMAIL_UPDATE_CODE_STR + AuthUtil.getUserId();
        //校验是否发送过短信以免重复发送60秒只能发送一次
        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            Long seconds = redisTemplate.opsForValue().getOperations().getExpire(key);
            if (seconds > 0) {
                return FrResult.failed("已获取过验证码，请等待" + seconds + "秒之后在获取");
            }
        }
        //获取四位随机数字
        String rom = cn.hutool.core.util.RandomUtil.randomNumbers(4);
        try {
            FrResult<?> result = EmailUtil.sendEmail("更换绑定邮箱地址", "验证码：" + rom,
                            new String[]{email}, true, null);
            if (result.getCode() == 1) {
                return FrResult.failed("发送失败：" + result.getMsg());
            }
        } catch (Exception e) {
            return FrResult.failed("发送失败：" + e.getMessage());
        }
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.opsForValue().set(RedisConstant.EMAIL_UPDATE_CODE_STR + AuthUtil.getUserId(),
                rom + "_" + email, RedisConstant.CODE_TIMEOUT, TimeUnit.SECONDS);
        return FrResult.successNodata("发送成功");
    }


    @GetMapping("updateEmail")
    public FrResult<?> updateEmail(@RequestParam("password") String password,
                                   @RequestParam("code") String code) {
        String key = RedisConstant.EMAIL_UPDATE_CODE_STR + AuthUtil.getUserId();
        Object smsStr = redisTemplate.opsForValue().get(key);
        if (null == smsStr) {
            return FrResult.failed("验证码错误");
        }
        String smsCode = String.valueOf(smsStr).split("_")[0];
        String email = String.valueOf(smsStr).split("_")[1];
        if (!code.equals(smsCode)) {
            return FrResult.failed("验证码错误");
        }
        User userEntityInfo = userService.getById(AuthUtil.getUserId());
        if (!Sm2Util.decrypt(userEntityInfo.getPassword()).equals(password)) {
            return FrResult.failed("用户密码错误");
        }
        //验证成功开始更新数据
        User user = new User();
        user.setId(AuthUtil.getUserId());
        user.setEmail(email);
        userService.updateById(user);

        return FrResult.successNodata("更新绑定邮箱成功");
    }


    @GetMapping("userNameByIds")
    public FrResult<?> userNameByIds(@RequestParam("ids")String ids) {
        List<User> list =
                userService.listByIds(Arrays.asList(ids.split(",")));
        JSONObject result = new JSONObject();
        list.forEach(o -> {
             result.put(o.getId(),o.getNickName());
        });
        return FrResult.success(result);
    }
}
