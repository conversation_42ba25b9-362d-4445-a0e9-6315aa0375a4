<template>
  <div>
    <a-card :bordered="false">
      <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
              url: '/${modeName}/${classname}/list',
              method: 'get',
              }"
      >
        <!-- 查询条件 -->
        <template #frSimpleQuery>
            #foreach ($row in $reqJson)
                #if($row.isUse.equals('1') && $row.type.equals('input'))
                    #if($row.type.equals('input'))
                      <a-input v-model:value="params.${row.attr}" :style="{ width: '300px' }"
                               placeholder="请输入${row.columnComment}"></a-input>
                    #end
                    #if($row.type.equals('select'))
                      <a-select v-model:value="params.${row.attr}" :options="dict['${row.typeVal}']?.list"
                                :style="{ width: '300px' }"
                                placeholder="请选择${row.columnComment}"></a-select>
                    #end
                    #if($row.type.equals('date'))
                      <a-date-picker v-model:value="params.${row.attr}" :style="{ width: '300px' }"
                                     placeholder="请选择${row.columnComment}"/>

                    #end
                #end
            #end
        </template>
        <!-- 操作按钮 -->
        <template #frSimpleBtn>
          <a-button v-if="authAdd" type="primary" @click="addRow">新增租户</a-button>
        </template>
        <!-- 操作列 -->
        <template #bodyCell="{ record:row, column }">
          <template v-if="column.key === 'operation'">
            <a-space :size="4">
              <a-button v-if="authEdit" size="small" type="link"
                        @click="editRow(row)">
                修改
              </a-button
              >
              <a-popconfirm
                  v-if="authDel"
                  title="是否确认删除?"
                  @confirm="delRow(row)"
              >
                <a-button size="small" type="text" danger
                > 删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </FrQuery>
    </a-card>
    <!-- 新增/修改 -->
    <a-modal
        v-model:open="visibleModal"
        :width="500"
        :mask-closable="false"
        :title="opt === 'add' ? '新增' : '修改'"
        :confirm-loading="saveBtn.loading"
        :ok-text="saveBtn.content"
        @ok="onSubmit"
    >
        <a-form ref="form" :label-col="{ span: 6 }" :label-align="'right'" :model="formData" :rules="rules">
            #foreach ($row in $formJson)
                #if($row.isUse.equals('1') && !$row.attr.equals($pri))
                  <a-form-item label="${row.columnComment}" name="${row.attr}">
                      #if($row.type.equals('input'))
                        <a-input v-model:value="formData.${row.attr}"
                                 placeholder="请输入${row.columnComment}"></a-input>
                      #end
                      #if($row.type.equals('select'))
                        <a-select v-model:value="formData.${row.attr}" :options="dict['${row.typeVal}']?.list"
                                  placeholder="请选择${row.columnComment}"></a-select>
                      #end
                      #if($row.type.equals('date'))
                        <a-date-picker v-model:value="formData.${row.attr}"
                                       placeholder="请选择${row.columnComment}"/>
                      #end
                  </a-form-item>
                #end
            #end
        </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'List${classname}',
  };
</script>

<script setup lang="ts">
  import {ref, onMounted, computed, reactive,nextTick} from 'vue';
  import {message} from 'ant-design-vue';
  import {publicInterface} from '@/api/common'
  import {useUserStore} from '@/store';
  import FrQuery from '@/components/fr-query/index.vue';

  //权限控制
  const userStore = useUserStore();
  const authAdd = computed(() => userStore.roles.includes('${opt}add'));
  const authEdit = computed(() => userStore.roles.includes('${opt}edit'));
  const authDel = computed(() => userStore.roles.includes('${opt}del'));

      #if(!$dictList.equals(''))
      //初始化字典
      const dict = ref<DictType>({});
      const initDict = async () => {
        dict.value = await getDict('${dictList}');
      };
      #end

  const queryRef = ref(null);
  const firstFetch = async () => {
    queryRef.value.loadData(true);
  };

  //新增/修改弹窗start
  const visibleModal = ref(false);
  const formData = ref({
          ${pri}: null,
      #foreach ($row in $formJson)
          #if($row.isUse.equals('1') && !$row.attr.equals($pri))
                  ${row.attr}: null,
          #end
      #end
  });
  const form = ref(null);
  const saveBtn = reactive({
    content: '保存',
    loading: false,
  });

  const rules = {
      #foreach ($row in $formJson)
          #if($row.isUse.equals('1') && !$row.attr.equals($pri))
                  ${row.attr}: [{required: true, message: '${row.columnComment}比填'}],
          #end
      #end
  } as Rules;

  //提交方法
  const onSubmit = async () => {
    saveBtn.content = '保存中...';
    saveBtn.loading = true;
    form.value.validate().then(async () => {
      let submitForm = {
          #foreach ($row in $formJson)
              #if($row.isUse.equals('1') && !$row.attr.equals($pri))
                      ${row.attr}: formData.value.${row.attr},
              #end
          #end
              ${pri}: null
      };
      if (opt.value === 'add') {
        try {
          let result1 = await publicInterface({
            url: '/${modeName}/${classname}/add',
            method: 'post',
            data: submitForm
          });
          if (result1.code === 0) {
            visibleModal.value = false;
            queryRef.value.loadData();
            message.success('保存成功');
          } else {
            message.error('保存失败：' + result1.msg);
          }
        } catch (error) {
          message.error('保存失败:' + error.message);
        } finally {
          saveBtn.content = '保存';
          saveBtn.loading = false;
        }
      } else {
        submitForm.${pri} = formData.value.${pri};
        try {
          let result1 = await publicInterface({
            url: '/${modeName}/${classname}/edit',
            method: 'post',
            data: submitForm
          });
          if (result1.code === 0) {
            visibleModal.value = false;
            queryRef.value.loadData();
            message.success('保存成功');
          } else {
            message.error('保存失败：' + result1.msg);
          }
        } catch (error) {
          message.error('保存失败:' + error.message);
        } finally {
          saveBtn.content = '保存';
          saveBtn.loading = false;
        }
      }
    }).finally(() => {
      saveBtn.content = '保存';
      saveBtn.loading = false;
    });
  };
  //新增/修改弹窗end

  const columns = [
      #foreach ($row in $outJson)
          #if($row.isUse.equals('1'))
            {
              width: 100,
              key: '${row.attr}',
              dataIndex: '${row.attr}',
              title: '${row.columnComment}',
              align: '${row.align}'
            },
          #end
      #end
    {
      key: 'operation',
      title: '操作',
      width: 200,
      align: 'center',
      fixed: 'right',
    },
  ] as Columns;
  const params = ref({
      #foreach ($row in $reqJson)
          #if($row.isUse.equals('1') && $row.type.equals('input'))
                  ${row.attr}: null,
          #end
      #end
  });
  const opt = ref('add');
  const addRow = async () => {
    opt.value = 'add';
    visibleModal.value = true;
    nextTick(() => {
      form.value.resetFields();
      formData.value.${pri} = '';
    })
  };
  const editRow = async (row) => {
    opt.value = 'edit';
    visibleModal.value = true;
    nextTick(() => {
      form.value.resetFields();
      formData.value.${pri} = row.${pri};
        #foreach ($row in $formJson)
            #if($row.isUse.equals('1') && !$row.attr.equals($pri))
              formData.value.${row.attr} = row.${row.attr};
            #end
        #end
    })
  };
  const delRow = async (row) => {
    publicInterface({
      url: '/${modeName}/${classname}/del',
      urlParam: row.${pri},
      method: 'delete'
    }).then((res) => {
      if (res.code === 0) {
        queryRef.value.loadData();
        message.success('删除成功');
      } else {
        message.error('删除失败：' + res.msg);
      }
    })
        .catch((error) => {
          message.error('删除失败:' + error.message);
        });
  };

  //vue的api
  onMounted(async () => {
      #if(!$dictList.equals(''))
        initDict()
      #end
  });
</script>

<style lang="less" scoped>
</style>
