package ${package}.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;



/**
 * @Copyright: ${company}
 * @Desc: ${comments}实体
 * @Date: ${datetime}
 * @Author: ${author}
 */

@Data
@NoArgsConstructor
@TableName(value = "${tableName}")
@Schema(description ="${comments}")
public class ${className} implements Serializable{
    @Serial
    private static final long serialVersionUID=1L;

#foreach ($column in $columns)
#if($column.columnKey.equals('PRI'))
    @TableId(type = IdType.ASSIGN_ID)
#end
#if($column.dataType.equals('json'))
    @TableField(typeHandler = JacksonTypeHandler.class)
#end
    @Schema(description ="${column.columnComment}")
    private $column.attrType $column.attrname;
#end
}
