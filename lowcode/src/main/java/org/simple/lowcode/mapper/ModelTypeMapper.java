package org.simple.lowcode.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.simple.lowcode.entity.ModelType;

/**
 * @Copyright: frSimple
 * @Date: 2024-11-17 08:28:44
 * @Author: frSimple
 */

@Mapper
public interface ModelTypeMapper
        extends BaseMapper<ModelType> {

    @Select("with recursive cte as ( \n" +
            "                          select id,name,parent_id from code_model_type  \n" +
            "                         where id = #{id} \n" +
            "                         union all  \n" +
            "                          select t.id,t.name,t.parent_id from code_model_type t   \n" +
            "                         join cte t1 on t1.parent_id = t.id \n" +
            "                        ) \n" +
            "                        select GROUP_CONCAT(name order by id asc separator ' > ' ) from cte")
    String allParentName(@Param("id") String id);
}
