package org.simple.lowcode.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * @Copyright: frSimple
 * @Desc: 数据模型输出配置表实体
 * @Date: 2024-11-17 08:28:42
 * @Author: frSimple
 */

@Data
@NoArgsConstructor
@TableName(value = "code_model_out", autoResultMap = true)
@Schema(description = "数据模型输出配置表")
public class ModelOut implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
    @Schema(description = "关联模型ID")
    private String modelId;
    @Schema(description = "属性名称")
    private String fieldName;
    @Schema(description = "属性说明")
    private String fieldNo;
    @Schema(description = "其它属性")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject extAttr;
    @Schema(description = "参数名")
    private String attrNo;
    @Schema(description = "排序")
    private Long sort;
}
