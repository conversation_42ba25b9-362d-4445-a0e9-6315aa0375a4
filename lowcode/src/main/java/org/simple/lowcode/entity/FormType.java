package org.simple.lowcode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.simple.base.dto.BaseEntity;

import java.io.Serial;
import java.io.Serializable;


/**
 * @Copyright: frSimple
 * @Desc: 表单模板分类实体
 * @Date: 2024-10-31 17:47:15
 * @Author: frSimple
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "code_form_type")
@Schema(description ="表单模板分类")
public class FormType extends BaseEntity implements Serializable{
@Serial
private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description ="主键ID")
    private String id;
    @Schema(description ="分类名称")
    private String name;
    @Schema(description ="父分类ID")
    private String parentId;
    @Schema(description ="类型")
    private String genre;
}
