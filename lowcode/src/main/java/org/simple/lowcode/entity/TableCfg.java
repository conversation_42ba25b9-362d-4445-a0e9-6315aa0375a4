package org.simple.lowcode.entity;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName(value = "code_table_cfg",autoResultMap = true)
public class TableCfg implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    private String dataSource;
    private String tableName;
    private String fix;
    private String pkg;
    private String auth;
    private LocalDateTime createTime;
    private String modeDesc;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray reqJson;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray outJson;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray formJson;

}
