package org.simple.lowcode.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.simple.base.dto.mybatis.Page;
import org.simple.base.storage.OssUtil;
import org.simple.base.vo.FrResult;
import org.simple.lowcode.entity.CodeTemplate;
import org.simple.lowcode.service.CodeTemplateService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Copyright: simple
 * @Desc:
 * @Date: 2023-02-16 20:27:05
 * @Author: frSimple
 */

@RestController
@RequestMapping("/lowcode/codeTemplate")
@Tag(name ="合同生成管理" , description = "合同生成管理")
public class CodeTemplateController {

    @Resource
    private RedisTemplate<String,Object> redisTemplate;
    @Resource
    private CodeTemplateService codeTemplateService;


    @GetMapping("list")
    @Operation(summary = "查询合同模板信息表")
    public FrResult<?> list(Page<CodeTemplate> page, CodeTemplate codeTemplate) {
        if (StringUtils.isNotEmpty(codeTemplate.getName())) {
            String name = codeTemplate.getName();
            codeTemplate.setName(null);
            return FrResult.success(codeTemplateService.
                    page(page, Wrappers.query(codeTemplate).like("name", name)));
        } else {
            return FrResult.success(codeTemplateService.page(page, Wrappers.query(codeTemplate)));
        }
    }

    @PostMapping("add")
    @Operation(summary = "新增合同模板信息")
    @SaCheckPermission("lowcode:wordcreate:add")
    public FrResult<?> add(CodeTemplate codeTemplate,
                           @RequestParam(value = "file") MultipartFile file) throws Exception {
        //上传图片
        FrResult<?> result =
                OssUtil.upLoad(file,file.getOriginalFilename(),false);
        if (result.getCode() != 0) {
            throw new Exception("图片上传失败：" + result.getMsg());
        }
        codeTemplate.setUrl(result.getData().toString());
        codeTemplate.setCreateTime(LocalDateTime.now());
        codeTemplate.setUpdateTime(LocalDateTime.now());
        codeTemplate.setFileName(file.getOriginalFilename());
        return FrResult.success(codeTemplateService.save(codeTemplate));
    }

    @PostMapping("edit")
    @Operation(summary = "修改合同模板信息")
    @SaCheckPermission("lowcode:wordcreate:edit")
    public FrResult<?> edit(CodeTemplate codeTemplate,
                            @RequestParam(value = "file", required = false) MultipartFile file) throws Exception {
        if (null != file) {
            //上传图片
            FrResult<?> result =
                    OssUtil.upLoad(file,file.getOriginalFilename(),false);
            if (result.getCode() != 0) {
                throw new Exception("图片上传失败：" + result.getMsg());
            }
            codeTemplate.setUrl(result.getData().toString());
            codeTemplate.setFileName(file.getOriginalFilename());
        }
        codeTemplate.setUpdateTime(LocalDateTime.now());
        return FrResult.success(codeTemplateService.updateById(codeTemplate));
    }

    @DeleteMapping("del/{id}")
    @Operation(summary = "删除合同模板信息")
    @SaCheckPermission("lowcode:wordcreate:del")
    public FrResult<?> del(@PathVariable("id") String id) {
        return FrResult.success(codeTemplateService.removeById(id));
    }

    @GetMapping("get/{code}")
    @Operation(summary = "查询合同模板信息")
    @SaCheckPermission("lowcode:wordcreate:query")
    public FrResult<?> get(@PathVariable("code") String code) {
        CodeTemplate codeTemplate = new CodeTemplate();
        codeTemplate.setCode(code);
        List<CodeTemplate> list =
                codeTemplateService.list(Wrappers.query(codeTemplate));
        return FrResult.success(list.size());
    }

    @GetMapping("downLoad/{id}")
    @Operation(summary = "下载合同模板")
    @SaCheckPermission("lowcode:wordcreate:query")
    public FrResult<?> downLoad(@PathVariable("id") String id) {
        CodeTemplate codeTemplate = codeTemplateService.getById(id);
        return FrResult.success();
    }

    @GetMapping("createContract")
    @Operation(summary = "根据合同模板生成合同")
    @SaIgnore
    public void downLoad(HttpServletResponse response) throws IOException {
        JSONObject object = new JSONObject();
        object.put("title","frSimple");

        JSONArray array = new JSONArray();
        JSONObject one = new JSONObject();
        one.put("id","1");
        one.put("name","张三");
        one.put("age","99");
        array.add(one);
        JSONObject one1 = new JSONObject();
        one1.put("id","2");
        one1.put("name","李四");
        one1.put("age","100");
        array.add(one1);
        object.put("student",array);
        String code = "CESHI_001";
        CodeTemplate codeTemplate = new CodeTemplate();
        codeTemplate.setCode(code);
        String fileName = codeTemplateService.getOne(Wrappers.query(codeTemplate)).getFileName();
        try{
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
            response.setContentType("application/docx; charset=UTF-8");
            codeTemplateService.createContract(code, object, response);
            response.getOutputStream().flush();
        }catch (Exception ex){
            ex.printStackTrace();
        }finally {
            response.getOutputStream().close();
        }

    }

}