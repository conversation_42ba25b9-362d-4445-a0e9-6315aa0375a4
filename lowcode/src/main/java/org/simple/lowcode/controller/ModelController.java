package org.simple.lowcode.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.constant.CommonConst;
import org.simple.base.util.DataModelUtil;
import org.simple.base.util.ObjectMapperUtil;
import org.simple.base.vo.FrResult;
import org.simple.base.vo.model.DataModelVo;
import org.simple.lowcode.dto.ModelDto;
import org.simple.lowcode.entity.*;
import org.simple.lowcode.service.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @Copyright: frSimple
 * @Date: 2024-11-17 08:28:41
 * @Author: frSimple
 */

@RestController
@RequestMapping("/lowcode/model")
@Tag(name = "模型信息")
@Slf4j
public class ModelController {

    @Resource
    private ModelService modelService;

    private final static ObjectMapper objectMapper =
            ObjectMapperUtil.objectMapper();

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ModelReqService modelReqService;

    @Resource
    private ModelOutService modelOutService;

    @Resource
    private DataSourceService dataSourceService;

    @Resource
    private ModelButtonService modelButtonService;


    @PostMapping("list")
    @Operation(summary = "查询数据模型信息表")
    public FrResult<?> list(@RequestBody Page<Model> page, @RequestBody Model model) {
        return FrResult.success(modelService.pageList(page, model));
    }

    @PostMapping("add")
    @Operation(summary = "新增数据模型信息表")
    @SaCheckPermission("lowcode:model:add")
    public FrResult<?> add(@RequestBody ModelDto model) {
        try {
            modelService.saveModel(model);
            return FrResult.success();
        } catch (Exception e) {
            return FrResult.failed(e.getMessage());
        }
    }

    @PostMapping("edit")
    @Operation(summary = "修改数据模型信息表")
    @SaCheckPermission("lowcode:model:edit")
    public FrResult<?> edit(@RequestBody ModelDto model) {
        try {
            modelService.editModel(model);
            return FrResult.success();
        } catch (Exception e) {
            return FrResult.failed(e.getMessage());
        }
    }

    @DeleteMapping("del/{id}")
    @Operation(summary = "删除数据模型信息表")
    @SaCheckPermission("lowcode:model:del")
    public FrResult<?> del(@PathVariable("id") String id) {
        try {
            modelService.delModel(id);
            return FrResult.success();
        } catch (Exception e) {
            return FrResult.failed(e.getMessage());
        }
    }

    @PostMapping("refCache")
    @Operation(summary = "刷新缓存")
    public FrResult<?> refCache(@RequestBody(required = false) Model m) throws JsonProcessingException {
        log.info("开始初始化数据模型...");
        List<Model> list = modelService.list();
        List<String> newKeys = new ArrayList<>();
        for (Model model : list) {
            DataModelVo d = new DataModelVo();
            BeanUtil.copyProperties(model, d);
            d.setReqData(JSONArray.from(getReqList(d.getId())));
            d.setOutData(JSONArray.from(getOutList(d.getId())));
            d.setBtnData(JSONArray.from(getBtnList(d.getId())));
            DataSource dataSource = dataSourceService.getById(d.getDataSource());
            d.setServer(dataSource.getAppName());
            newKeys.add(CommonConst.SIMPLE_MODEL + model.getId());
            redisTemplate.opsForValue().set(CommonConst.SIMPLE_MODEL +
                    model.getId(), objectMapper.writeValueAsString(d));
        }
        //删除不存在的模型
        DataModelUtil.delNotUsedKey(newKeys);
        log.info("结束初始化数据模型...");
        return FrResult.success();
    }

    private List<ModelReq> getReqList(String modelId) {
        ModelReq where = new ModelReq();
        where.setModelId(modelId);
        return modelReqService.list(Wrappers.query(where).orderByAsc("sort"));
    }

    private List<ModelOut> getOutList(String modelId) {
        ModelOut where = new ModelOut();
        where.setModelId(modelId);
        return modelOutService.list(Wrappers.query(where).orderByAsc("sort"));
    }

    private List<ModelButton> getBtnList(String modelId) {
        ModelButton where = new ModelButton();
        where.setModelId(modelId);
        return modelButtonService.list(Wrappers.query(where).orderByAsc("sort"));
    }

}