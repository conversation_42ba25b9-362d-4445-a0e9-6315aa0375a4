package org.simple.lowcode.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.constant.CommonConst;
import org.simple.base.util.FormModelUtil;
import org.simple.base.util.ObjectMapperUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.base.vo.model.FieldModelVo;
import org.simple.base.vo.model.FormModelVo;
import org.simple.lowcode.dto.FormDesignDto;
import org.simple.lowcode.entity.DataSource;
import org.simple.lowcode.entity.FormField;
import org.simple.lowcode.entity.FormTemplate;
import org.simple.lowcode.entity.Model;
import org.simple.lowcode.service.DataSourceService;
import org.simple.lowcode.service.FormFieldService;
import org.simple.lowcode.service.FormTemplateService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Copyright: frSimple
 * @Date: 2024-10-31 16:48:44
 * @Author: frSimple
 */

@RestController
@RequestMapping("/lowcode/formTemplate")
@Tag(name = "表单模板主表")
@Slf4j
public class FormTemplateController {

    private final static ObjectMapper objectMapper =
            ObjectMapperUtil.objectMapper();

    @Resource
    private FormTemplateService formTemplateService;

    @Resource
    private FormFieldService formFieldService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private DataSourceService dataSourceService;


    @PostMapping("list")
    @Operation(summary = "查询表单模板")
    @SaCheckPermission("flow:formTemplate:query")
    public FrResult<?> list(@RequestBody Page<FormTemplate> page,
                            @RequestBody FormTemplate formTemplate) {
        return FrResult.success(formTemplateService.pageList(page, formTemplate));
    }

    @PostMapping("add")
    @Operation(summary = "新增表单模板")
    @SaCheckPermission("flow:formTemplate:add")
    public FrResult<?> add(@RequestBody FormTemplate formTemplate) {
        formTemplate.setId(RandomUtil.getFormId());
        return FrResult.success(formTemplateService.save(formTemplate));
    }

    @PostMapping("edit")
    @Operation(summary = "修改表单模板")
    @SaCheckPermission("flow:formTemplate:edit")
    public FrResult<?> edit(@RequestBody FormTemplate formTemplate) {
        return FrResult.success(formTemplateService.updateById(formTemplate));
    }

    @DeleteMapping("del/{id}")
    @Operation(summary = "删除表单模板")
    @SaCheckPermission("flow:formTemplate:del")
    public FrResult<?> del(@PathVariable("id") String id) {
        return FrResult.success(formTemplateService.removeById(id));
    }

    @GetMapping("listField/{id}")
    @Operation(summary = "查询表单模板元素")
    public FrResult<?> list(@PathVariable("id") String id) {
        FormField formField = new FormField();
        formField.setTemplateId(id);
        return FrResult.success(formFieldService.list(Wrappers.query(formField).
                orderByAsc("sort")));
    }

    @PostMapping("design")
    @Operation(summary = "设计表单模板")
    public FrResult<?> edit(@RequestBody FormDesignDto formTemplate) {
        String templateId = formTemplate.getFormTemplate().getId();
        formTemplateService.updateById(formTemplate.getFormTemplate());
        //先删除然后进入全量插入
        FormField formField = new FormField();
        formField.setTemplateId(templateId);
        formFieldService.remove(Wrappers.query(formField));
        for (int i = 0; i < formTemplate.getFieldList().size(); i++) {
            FormField o = formTemplate.getFieldList().get(i);
            o.setTemplateId(templateId);
            o.setSort(i);
            formFieldService.save(o);
        }
        return FrResult.success();
    }

    @PostMapping("copy")
    @Operation(summary = "复制表单模板")
    public FrResult<?> copy(@RequestBody FormTemplate formTemplate) {
        try {
            FormTemplate f = formTemplateService.getById(formTemplate.getId());
            String newTemplateId = RandomUtil.getFormId();
            f.setId(newTemplateId);
            f.setName(formTemplate.getName());
            f.setCreateTime(LocalDateTime.now());
            f.setUpdateTime(LocalDateTime.now());
            formTemplateService.save(f);

            FormField formField = new FormField();
            formField.setTemplateId(formTemplate.getId());
            List<FormField> listField = formFieldService.list(Wrappers.query(formField));
            for (FormField ff : listField) {
                ff.setTemplateId(newTemplateId);
                ff.setId(RandomUtil.getFieldId());
                formFieldService.save(ff);
            }
            return FrResult.success();
        } catch (Exception ex) {
            log.error("异常错误：", ex);
            return FrResult.failed(ex.getMessage());
        }
    }

    @PostMapping("refCache")
    @Operation(summary = "刷新缓存")
    public FrResult<?> refCache(@RequestBody(required = false) Model m) throws JsonProcessingException {
        log.info("开始初始化页面模型...");
        List<FormTemplate> list = formTemplateService.list();
        List<String> newKeys = new ArrayList<>();
        for (FormTemplate model : list) {
            FormModelVo d = new FormModelVo();
            BeanUtil.copyProperties(model, d);
            d.setFieldList(getFieldList(d.getId()));
            DataSource dataSource = dataSourceService.getById(d.getDataSource());
            d.setServer(null == dataSource ? "" : dataSource.getAppName());
            d.setDbName(null == dataSource ? "" : dataSource.getDataName());
            newKeys.add(CommonConst.SIMPLE_FORM_MODEL + model.getId());
            redisTemplate.opsForValue().set(CommonConst.SIMPLE_FORM_MODEL +
                    model.getId(), objectMapper.writeValueAsString(d));
        }
        //删除不存在的模型
        FormModelUtil.delNotUsedKey(newKeys);
        log.info("结束初始化页面模型...");
        return FrResult.success();
    }

    private List<FieldModelVo> getFieldList(String formId) {
        FormField where = new FormField();
        where.setTemplateId(formId);
        List<FormField> fieldList = formFieldService.list(Wrappers.query(where));
        List<FieldModelVo> vos = new ArrayList<>();
        if (!fieldList.isEmpty()) {
            for (FormField field : fieldList) {
                FieldModelVo v = new FieldModelVo();
                BeanUtil.copyProperties(field, v);
                vos.add(v);
            }
        }
        return vos;
    }
}