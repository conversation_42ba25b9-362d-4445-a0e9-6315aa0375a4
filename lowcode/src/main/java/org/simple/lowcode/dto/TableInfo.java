package org.simple.lowcode.dto;


import com.alibaba.fastjson2.JSONArray;
import lombok.Data;

import java.util.Date;

@Data
public class TableInfo {

    private String tableComment;
    private String tableCollation;
    private Date createTime;
    private String tableName;
    private String fix;
    private String pkg;
    private String auth;
    private String id;
    private String modeDesc;
    private J<PERSON><PERSON>rray reqJson;
    private JSO<PERSON>rray outJson;
    private JSONArray formJson;
}
