package org.simple.lowcode.dto;

import com.alibaba.fastjson2.JSONArray;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.simple.lowcode.entity.Model;
import org.simple.lowcode.entity.ModelButton;
import org.simple.lowcode.entity.ModelOut;
import org.simple.lowcode.entity.ModelReq;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ModelDto extends Model {
    private String parentName;
    private JSONArray reqData;
    private JSONArray outData;
    private List<ModelReq> reqList;
    private List<ModelOut> outList;
    private List<ModelButton> btnList;
    private String AppName;
}
