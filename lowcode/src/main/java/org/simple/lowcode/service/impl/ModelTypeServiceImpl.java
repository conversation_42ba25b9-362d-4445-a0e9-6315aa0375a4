package org.simple.lowcode.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.simple.lowcode.entity.ModelType;
import org.simple.lowcode.mapper.ModelTypeMapper;
import org.simple.lowcode.service.ModelTypeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Copyright: frSimple
 * @Date: 2024-11-17 08:28:44
 * @Author: frSimple
 */


@Service
public class ModelTypeServiceImpl
        extends ServiceImpl<ModelTypeMapper, ModelType>
        implements ModelTypeService {

    @Override
    public List<Tree<String>> listTree() {
        List<ModelType> treeDtoList = baseMapper.selectList(null);
        TreeNodeConfig config = new TreeNodeConfig();
        return TreeUtil.build(treeDtoList, "0", config,
                (object, tree) -> {
                    tree.setName(object.getName());
                    tree.setId(object.getId());
                    tree.setParentId(object.getParentId());
                    tree.putExtra("value",object.getId());
                    tree.putExtra("label",object.getName());
                });
    }
}