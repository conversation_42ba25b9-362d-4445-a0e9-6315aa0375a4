package org.simple.lowcode.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.lowcode.dto.ModelDto;
import org.simple.lowcode.entity.Model;


/**
 * @Copyright: frSimple
 * @Date: 2024-11-17 08:28:41
 * @Author: frSimple
 */
public interface ModelService extends IService<Model> {

    IPage<ModelDto> pageList(Page<Model> page, Model model);

    void saveModel(ModelDto model);

    void editModel(ModelDto model);

    void delModel(String modelId);
}