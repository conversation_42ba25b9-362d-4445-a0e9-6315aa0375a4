package org.simple.lowcode.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import fr.opensagres.xdocreport.core.XDocReportException;
import jakarta.servlet.http.HttpServletResponse;
import org.simple.lowcode.entity.CodeTemplate;

import java.io.IOException;


/**
 * @Copyright: simple
 * @Date: 2023-02-16 20:27:05
 * @Author: frSimple
 */
public interface CodeTemplateService extends IService<CodeTemplate> {


    void  createContract(String code, JSONObject object, HttpServletResponse response) throws IOException, XDocReportException;
}