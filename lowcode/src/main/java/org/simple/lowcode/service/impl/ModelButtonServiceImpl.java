package org.simple.lowcode.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.simple.lowcode.entity.ModelButton;
import org.simple.lowcode.mapper.ModelButtonMapper;
import org.simple.lowcode.service.ModelButtonService;
import org.springframework.stereotype.Service;

/**
 * @Copyright: frSimple
 * @Date: 2025-01-21 16:44:50
 * @Author: frSimple
 */


@Service
public class ModelButtonServiceImpl
        extends ServiceImpl<ModelButtonMapper, ModelButton>
        implements ModelButtonService {

}