package org.simple.lowcode.util;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.configuration.ConfigurationException;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.WordUtils;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Component
public class GeneratorUtil {

    public static void generatorCode(Map<String, Object> tableInfo,
                                     List<Map<String, String>> columns,
                                     ZipOutputStream zip) {
        Properties prop = new Properties();
        prop.put("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        Velocity.init(prop);
        Configuration config = getConfig();
        List<Map<String, String>> columsList = new ArrayList<>();
        String pri = "";
        for (Map<String, String> column : columns) {
            Map<String, String> c = new HashMap<>(16);
            c.put("columnName", column.get("columnName"));
            c.put("dataType", column.get("dataType"));
            c.put("columnComment", column.get("columnComment"));
            c.put("extra", column.get("extra"));
            String attrName = toAttributeName(column.get("columnName"));
            c.put("attrName", attrName);
            c.put("attrname", StringUtils.uncapitalize(attrName));
            String attrType = config.getString(column.get("dataType"), "undefined");
            c.put("attrType", attrType);
            c.put("columnKey", column.get("columnKey"));
            if (null != column.get("columnKey") && column.get("columnKey").toString().equals("PRI")) {
                pri = StringUtils.uncapitalize(attrName);
            }
            columsList.add(c);
        }

        Map<String, Object> map = new HashMap<>(16);
        map.put("tableName", tableInfo.get("tableName"));
        map.put("comments", tableInfo.get("tableComment"));
        String className = removeTablePrefix(tableInfo.get("tableName").toString(),
                null != tableInfo.get("tablePrefix") ? tableInfo.get("tablePrefix").toString() : "");
        map.put("className", className);
        map.put("classname", StringUtils.uncapitalize(className));
        map.put("modeName", tableInfo.get("package").toString().substring(
                tableInfo.get("package").toString().lastIndexOf(".") + 1));
        map.put("opt", tableInfo.get("package").toString().substring(
                tableInfo.get("package").toString().lastIndexOf(".") + 1) + ":" +
                StringUtils.uncapitalize(className) + ":");
        map.put("modeDesc", tableInfo.get("modeDesc"));
        map.put("columns", columsList);
        map.put("package", tableInfo.get("package"));
        map.put("author", tableInfo.get("author"));
        map.put("email", config.getString("email"));
        map.put("company", config.getString("company"));
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("datetime", df.format(new Date()));

        //前端代码部分
        JSONArray reqJson = (JSONArray) tableInfo.get("reqJson");
        JSONArray outJson = (JSONArray) tableInfo.get("outJson");
        JSONArray formJson = (JSONArray) tableInfo.get("formJson");

        JSONArray nReqJson = new JSONArray();
        JSONArray nOutJson = new JSONArray();
        JSONArray nFormJson = new JSONArray();
        List<String> dictList = new ArrayList<>();

        reqJson.forEach(obj -> {
            JSONObject nObj = JSONObject.from(obj);
            nObj.put("attr", StringUtils.uncapitalize(toAttributeName(nObj.getString("columnName"))));
            nReqJson.add(nObj);
            if (nObj.getString("type").equals("select") && !dictList.contains(
                    nObj.getString("typeVal")
            )) {
                dictList.add(nObj.getString("typeVal"));
            }
        });
        outJson.forEach(obj -> {
            JSONObject nObj = JSONObject.from(obj);
            nObj.put("attr", StringUtils.uncapitalize(toAttributeName(nObj.getString("columnName"))));
            nOutJson.add(nObj);
        });
        formJson.forEach(obj -> {
            JSONObject nObj = JSONObject.from(obj);
            nObj.put("attr", StringUtils.uncapitalize(toAttributeName(nObj.getString("columnName"))));
            nFormJson.add(nObj);
            if (nObj.getString("type").equals("select") && !dictList.contains(
                    nObj.getString("typeVal")
            )) {
                dictList.add(nObj.getString("typeVal"));
            }
        });

        map.put("reqJson", nReqJson);
        map.put("outJson", nOutJson);
        map.put("formJson", nFormJson);
        map.put("pri", pri);
        map.put("dictList", dictList);

        VelocityContext context = new VelocityContext(map);
        List<String> templateNames = getTemplates();
        for (String name : templateNames) {
            StringWriter stringWriter = new StringWriter();
            Template template = Velocity.getTemplate(name, "UTF-8");
            template.merge(context, stringWriter);
            try {
                String pack = tableInfo.get("package").toString().replace(".", File.separator);
                zip.putNextEntry(new ZipEntry(getFileName(name, className, pack)));
                IoUtil.write(zip, StandardCharsets.UTF_8, false, stringWriter.toString());
                IoUtil.close(stringWriter);
                zip.closeEntry();
            } catch (Exception e) {
                System.out.println(map.get("tableName") + "生成失败：" + e.getMessage());
            }
        }
    }


    public static JSONArray generatorCodeToStr(Map<String, Object> tableInfo,
                                               List<Map<String, String>> columns) {
        Properties prop = new Properties();
        prop.put("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        Velocity.init(prop);
        Configuration config = getConfig();
        List<Map<String, String>> columsList = new ArrayList<>();
        String pri = "";
        for (Map<String, String> column : columns) {
            Map<String, String> c = new HashMap<>(16);
            c.put("columnName", column.get("columnName"));
            c.put("dataType", column.get("dataType"));
            c.put("columnComment", column.get("columnComment"));
            c.put("extra", column.get("extra"));
            String attrName = toAttributeName(column.get("columnName"));
            c.put("attrName", attrName);
            c.put("attrname", StringUtils.uncapitalize(attrName));
            String attrType = config.getString(column.get("dataType"), "undefined");
            c.put("attrType", attrType);
            c.put("columnKey", column.get("columnKey"));
            if (null != column.get("columnKey") && column.get("columnKey").toString().equals("PRI")) {
                pri = StringUtils.uncapitalize(attrName);
            }
            columsList.add(c);
        }

        Map<String, Object> map = new HashMap<>(16);
        map.put("tableName", tableInfo.get("tableName"));
        map.put("comments", tableInfo.get("tableComment"));
        String className = removeTablePrefix(tableInfo.get("tableName").toString(),
                null != tableInfo.get("tablePrefix") ? tableInfo.get("tablePrefix").toString() : "");
        map.put("className", className);
        map.put("classname", StringUtils.uncapitalize(className));
        map.put("modeName", tableInfo.get("package").toString().substring(
                tableInfo.get("package").toString().lastIndexOf(".") + 1));
        map.put("opt", tableInfo.get("package").toString().substring(
                tableInfo.get("package").toString().lastIndexOf(".") + 1) + ":" +
                StringUtils.uncapitalize(className) + ":");
        map.put("modeDesc", tableInfo.get("modeDesc"));
        map.put("columns", columsList);
        map.put("package", tableInfo.get("package"));
        map.put("author", tableInfo.get("author"));
        map.put("email", config.getString("email"));
        map.put("company", config.getString("company"));
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("datetime", df.format(new Date()));


        //前端代码部分
        JSONArray reqJson = (JSONArray) tableInfo.get("reqJson");
        JSONArray outJson = (JSONArray) tableInfo.get("outJson");
        JSONArray formJson = (JSONArray) tableInfo.get("formJson");

        JSONArray nReqJson = new JSONArray();
        JSONArray nOutJson = new JSONArray();
        JSONArray nFormJson = new JSONArray();
        List<String> dictList = new ArrayList<>();

        reqJson.forEach(obj -> {
            JSONObject nObj = JSONObject.from(obj);
            nObj.put("attr", StringUtils.uncapitalize(toAttributeName(nObj.getString("columnName"))));
            nReqJson.add(nObj);
            if (nObj.getString("type").equals("select") && !dictList.contains(
                    nObj.getString("typeVal")
            )) {
                dictList.add(nObj.getString("typeVal"));
            }
        });
        outJson.forEach(obj -> {
            JSONObject nObj = JSONObject.from(obj);
            nObj.put("attr", StringUtils.uncapitalize(toAttributeName(nObj.getString("columnName"))));
            nOutJson.add(nObj);
        });
        formJson.forEach(obj -> {
            JSONObject nObj = JSONObject.from(obj);
            nObj.put("attr", StringUtils.uncapitalize(toAttributeName(nObj.getString("columnName"))));
            nFormJson.add(nObj);
            if (nObj.getString("type").equals("select") && !dictList.contains(
                    nObj.getString("typeVal")
            )) {
                dictList.add(nObj.getString("typeVal"));
            }
        });

        map.put("reqJson", nReqJson);
        map.put("outJson", nOutJson);
        map.put("formJson", nFormJson);
        map.put("pri", pri);
        map.put("dictList", StrUtil.join(",",dictList));

        VelocityContext context = new VelocityContext(map);
        List<String> templateNames = getTemplates();
        JSONArray result = new JSONArray();
        for (String name : templateNames) {
            StringWriter stringWriter = new StringWriter();
            Template template = Velocity.getTemplate(name, "UTF-8");
            template.merge(context, stringWriter);
            try {
                String pack = tableInfo.get("package").toString().replace(".", File.separator);
                JSONObject obj = new JSONObject();
                obj.put("fileName", getClassName(name, className, pack));
                obj.put("fileContent", stringWriter.toString());
                result.add(obj);
            } catch (Exception e) {
                System.out.println(map.get("tableName") + "生成失败：" + e.getMessage());
            }
        }
        return result;
    }

    /**
     * 功能描述:〈获取模板〉
     *
     * @return : java.util.List<java.lang.String>
     * @throws :
     */
    public static List<String> getTemplates() {
        List<String> templates = new ArrayList<String>();
        templates.add("templates/Service.java.vm");
        templates.add("templates/ServiceImpl.java.vm");
        templates.add("templates/Controller.java.vm");
        templates.add("templates/Entity.java.vm");
        templates.add("templates/Mapper.java.vm");
        templates.add("templates/Tdesign.index.vue.vm");
        templates.add("templates/AntDesign.index.vue.vm");
        templates.add("templates/Naive.index.vue.vm");
        return templates;
    }

    /**
     * 功能描述:〈获得Java属性名称〉
     *
     * @param name 1
     * @return : java.lang.String
     * @throws :
     */
    public static String toAttributeName(String name) {
        return WordUtils.capitalizeFully(name, new char[]{'_'}).replace("_", "");
    }

    /**
     * 功能描述:〈去表的前缀〉
     *
     * @param name    1
     * @param prefixs 2
     * @return : java.lang.String
     * @throws :
     */
    public static String removeTablePrefix(String name, String prefixs) {
        name = name.replace(prefixs.toString(), "");
        return toAttributeName(name);
    }

    /**
     * 功能描述:〈获得配置信息〉
     *
     * @return : org.apache.commons.configuration.Configuration
     */
    public static Configuration getConfig() {
        try {
            return new PropertiesConfiguration("generator.properties");
        } catch (ConfigurationException e) {
            System.out.println("获取配置文件失败，" + e.getMessage());
        }
        return null;
    }

    /**
     * 功能描述:〈得到文件名〉
     *
     * @param name      1
     * @param className 2
     * @return : java.lang.String
     */
    public static String getFileName(String name, String className, String pack) {
        if (name.contains("Service.java.vm")) {
            return pack + File.separator + "service" + File.separator + className + "Service.java";
        }
        if (name.contains("ServiceImpl.java.vm")) {
            return pack + File.separator + "service" + File.separator + "impl" + File.separator + className + "ServiceImpl.java";
        }
        if (name.contains("Controller.java.vm")) {
            return pack + File.separator + "controller" + File.separator + className + "Controller.java";
        }
        if (name.contains("Entity.java.vm")) {
            return pack + File.separator + "entity" + File.separator + className + ".java";
        }
        if (name.contains("Mapper.java.vm")) {
            return pack + File.separator + "mapper" + File.separator + className + "Mapper.java";
        }
        if (name.contains("Tdesign.index.vue.vm")) {
            return pack + File.separator + "vue" + File.separator + "Tesign.vue";
        }
        if (name.contains("AntDesign.index.vue.vm")) {
            return pack + File.separator + "vue" + File.separator + "AntDesign.vue";
        }
        if (name.contains("Naive.index.vue.vm")) {
            return pack + File.separator + "vue" + File.separator + "Naive.vue";
        }
        return null;
    }

    /**
     * 功能描述:〈得到文件名〉
     *
     * @param name      1
     * @param className 2
     * @return : java.lang.String
     */
    public static String getClassName(String name, String className, String pack) {
        if (name.contains("Service.java.vm")) {
            return className + "Service.java";
        }
        if (name.contains("ServiceImpl.java.vm")) {
            return className + "ServiceImpl.java";
        }
        if (name.contains("Controller.java.vm")) {
            return className + "Controller.java";
        }
        if (name.contains("Entity.java.vm")) {
            return className + ".java";
        }
        if (name.contains("Mapper.java.vm")) {
            return className + "Mapper.java";
        }
        if (name.contains("Tdesign.index.vue.vm")) {
            return "Tesign.vue";
        }
        if (name.contains("AntDesign.index.vue.vm")) {
            return "AntDesign.vue";
        }
        if (name.contains("Naive.index.vue.vm")) {
            return "Naive.vue";
        }
        return null;
    }

    /**
     * 功能描述:〈得到输出目录〉
     *
     * @param packageName 1
     * @return : java.lang.String
     */
    private static String createFiles(String packageName) {
        File directory = new File(".");
        String path = null;
        try {
            path = directory.getCanonicalPath();
        } catch (IOException e) {
            System.out.println("获取当前目录失败");
            return null;
        }
        path += File.separator + "src" + File.separator + "main" + File.separator + "java" +
                File.separator + packageName.replace(".", File.separator) + File.separator + "out";
        File file = new File(path);
        System.out.println(path);
        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }
        return path;
    }

}
