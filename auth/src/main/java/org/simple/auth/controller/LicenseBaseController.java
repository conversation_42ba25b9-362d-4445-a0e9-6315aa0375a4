package org.simple.auth.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.service.LicenseVerifyService;
import org.simple.base.vo.FrResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;



@Slf4j
@RestController
@RequestMapping("/center/license")
@Tag(name = "授权信息表")
public class LicenseBaseController {

    @Resource
    private LicenseVerifyService licenseVerifyService;

    /**
     * 获取license信息
     * */
    @GetMapping("info")
    public FrResult<?> getLicenseInfo() {
        return FrResult.success(licenseVerifyService.getLicenseInfo());
    }

    /**
     * 更新license文件
     * */
    @PostMapping("updateLicense")
    public FrResult<?> updateLicense(@RequestParam("file") MultipartFile file) {
        try {
            return FrResult.success(licenseVerifyService.updateLicenseInfo(file));
        } catch (Exception ex) {
            return FrResult.failed(ex.getMessage());
        }
    }
}
