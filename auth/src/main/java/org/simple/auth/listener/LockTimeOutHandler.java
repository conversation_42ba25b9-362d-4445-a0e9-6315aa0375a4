package org.simple.auth.listener;

import jakarta.annotation.Resource;
import org.simple.auth.service.AuthUserService;
import org.simple.base.handler.RedisDelayQueueHandler;
import org.simple.base.constant.RedisConstant;
import org.springframework.stereotype.Component;


@Component("lockTimeOutHandler")
public class LockTimeOutHandler implements RedisDelayQueueHandler<String> {

    @Resource
    public AuthUserService authUserService;

    @Override
    public void execute(String expireKey) {
        if (expireKey.startsWith(RedisConstant.LOCK_TIME)) {
            String userId = expireKey.replace(RedisConstant.LOCK_TIME, "");
            authUserService.resetLoginError(userId);
        }
    }
}
