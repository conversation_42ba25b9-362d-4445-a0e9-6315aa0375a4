package org.simple.auth.handler;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.context.model.SaRequest;
import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import cn.dev33.satoken.oauth2.data.model.AccessTokenModel;
import cn.dev33.satoken.oauth2.data.model.loader.SaClientModel;
import cn.dev33.satoken.oauth2.data.model.request.RequestAuthModel;
import cn.dev33.satoken.oauth2.exception.SaOAuth2Exception;
import cn.dev33.satoken.oauth2.granttype.handler.SaOAuth2GrantTypeHandlerInterface;
import cn.hutool.core.util.ObjectUtil;
import jakarta.annotation.Resource;
import org.simple.auth.model.AuthUser;
import org.simple.auth.service.AuthUserService;
import org.simple.auth.util.LoginUtil;
import org.simple.base.constant.AuthConstant;
import org.simple.base.enums.LoginType;
import org.simple.base.enums.SysConfigEnum;
import org.simple.base.enums.UserType;
import org.simple.base.util.Sm2Util;
import org.simple.base.util.SysConfigUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SmsGrantTypeHandler implements SaOAuth2GrantTypeHandlerInterface {

    @Resource
    private AuthUserService authUserService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    public String getHandlerGrantType() {
        return AuthConstant.GRANT_TYPE_SMS;
    }

    @Override
    public AccessTokenModel getAccessToken(SaRequest req,
                                           String clientId,
                                           List<String> scopes) {
        String sysLoginType = SysConfigUtil.getValue(SysConfigEnum.LOGIN_TYPE.getLabel());
        if (!sysLoginType.contains("phone")) {
            throw new SaOAuth2Exception("不被允许的登陆方式");
        }
        AuthUser authUser = new AuthUser();
        String decPhone = Sm2Util.decrypt(SaHolder.getRequest().getParam("phone"));
        authUser = authUserService.queryUserByPhone(decPhone,
                UserType.USER_SYSTEM.getValue());
        LoginType loginType = LoginType.LOGIN_SMS;
        if (ObjectUtil.isEmpty(authUser)) {
            throw new SaOAuth2Exception("用户不存在");
        }
        String pwd = authUser.getPassword();
        List<String> groupIds = authUserService.queryGroups(authUser.getId());
        //校验用户数据
        try {
            LoginUtil.validUserInfo(pwd, authUser, groupIds);
        } catch (Exception e) {
            throw new SaOAuth2Exception(e.getMessage());
        }
        //登录成功记录用户最后登录时间
        authUserService.updateLastLoginTime(authUser.getId());
        //登录错误次数重置为0
        authUserService.resetLoginError(authUser.getId());
        // 3、登录
        String userId = authUser.getId(); // 模拟 userId，真实项目应该根据手机号从数据库查询

        // 4、构建 ra 对象
        RequestAuthModel ra = new RequestAuthModel();
        ra.clientId = clientId;
        ra.loginId = userId;
        ra.scopes = scopes;

        SaClientModel clientModel = SaOAuth2Manager.getDataLoader()
                .getClientModelNotNull(ra.clientId);

        long timeOut = clientModel.getAccessTokenTimeout();
        //登录成功初始化用户信息到redis中
        LoginUtil.initUser(authUser,
                authUserService.queryPermissions(authUser.getId()),
                groupIds
                , loginType.getValue(), redisTemplate, timeOut);

        // 5、生成 Access-Token
        AccessTokenModel accessTokenModel = SaOAuth2Manager.
                getDataGenerate().generateAccessToken(ra, true,
                        atm -> atm.grantType = AuthConstant.GRANT_TYPE_SMS);

        return accessTokenModel;
    }
}
