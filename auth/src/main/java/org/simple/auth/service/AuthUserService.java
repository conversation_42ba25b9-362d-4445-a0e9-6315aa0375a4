package org.simple.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletResponse;
import org.simple.auth.model.AuthUser;

import java.io.IOException;
import java.util.List;

public interface AuthUserService extends IService<AuthUser> {

    /**
     * 根据账号查询用户信息
     */
    AuthUser queryUserByUserName(String userName, String email, String phone, String type);

    /**
     * 根据手机号查询用户信息
     */
    AuthUser queryUserByPhone(String phone, String type);

    /**
     * 查询用户权限集合
     */
    List<String> queryPermissions(String userId);

    /**
     * 查询用户所属角色集合
     */
    List<String> queryGroups(String userId);

    /**
     * 增加密码错误次数
     */
    void addOneLoginError(String userId);

    /**
     * 修改最后登录时间
     */
    void updateLastLoginTime(String userId);

    /**
     * 重置登录错误次数
     */
    void resetLoginError(String userId);
    /**
     * 获取验证码
     *
     * @param response 返回
     * @param sp       时间
     * @throws IOException 异常
     */
    void getCode(HttpServletResponse response, String sp) throws IOException;
}
