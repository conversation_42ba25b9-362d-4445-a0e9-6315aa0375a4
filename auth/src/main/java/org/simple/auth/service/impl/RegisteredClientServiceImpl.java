package org.simple.auth.service.impl;

import cn.dev33.satoken.oauth2.template.SaOAuth2Util;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.simple.auth.mapper.AuthUserMapper;
import org.simple.auth.mapper.OauthClientMapper;
import org.simple.auth.model.AuthUser;
import org.simple.auth.model.OauthClient;
import org.simple.auth.service.RegisteredClientService;
import org.simple.base.enums.UserType;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.PageUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.util.Sm2Util;
import org.simple.base.vo.api.ApiUserVo;
import org.simple.base.vo.api.UserParamVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class RegisteredClientServiceImpl
        extends ServiceImpl<OauthClientMapper, OauthClient>
        implements RegisteredClientService {

    @Resource
    private AuthUserMapper authUserMapper;

    @Override
    public OauthClient queryDetailsByClientId(String clientId) {
        return baseMapper.queryDetailsByClientId(clientId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addUser(ApiUserVo apiUserVo) {
        String appId = RandomUtil.getAppId();
        String appSecret = apiUserVo.getAppSecret();
        String phone = apiUserVo.getPhone();
        String email = apiUserVo.getEmail();
        String nickName = apiUserVo.getNickName();
        //新增系统用户
        AuthUser authUser = new AuthUser();
        authUser.setId(appId);
        authUser.setTenantId("1");
        authUser.setUserName(appId);
        authUser.setEmail(email);
        authUser.setPhone(phone);
        authUser.setNickName(nickName);
        authUser.setStatus("0");
        authUser.setError(0);
        authUser.setType(UserType.USER_OAUTH2.getValue());
        authUser.setPassword(appSecret);
        authUser.setCreateDate(LocalDateTime.now());
        authUser.setUpdateDate(LocalDateTime.now());
        authUserMapper.insert(authUser);

        //新增客户端用户
        OauthClient oauthClient = new OauthClient();
        oauthClient.setClientId(appId);
        oauthClient.setClientSecret(Sm2Util.decrypt(appSecret));
        oauthClient.setAuthorizedGrantTypes("api,refresh_token");
        oauthClient.setAccessTokenValidity(apiUserVo.getTokenExp());
        oauthClient.setRefreshTokenValidity(apiUserVo.getRefTokenExp());
        oauthClient.setCreateTime(new Timestamp(new Date().getTime()));
        oauthClient.setArchived(0);
        oauthClient.setTrusted(0);
        oauthClient.setAutoapprove("false");
        oauthClient.setResourceIds("all");
        oauthClient.setScope("all");
        baseMapper.insert(oauthClient);
        return appId;
    }

    @Override
    public void editUser(ApiUserVo apiUserVo) {
        AuthUser authUser = authUserMapper.selectById(apiUserVo.getAppId());
        authUser.setEmail(apiUserVo.getEmail());
        authUser.setPhone(apiUserVo.getPhone());
        authUser.setNickName(apiUserVo.getNickName());
        authUser.setUpdateDate(LocalDateTime.now());
        authUserMapper.updateById(authUser);
        OauthClient oauthClient = baseMapper.selectById(apiUserVo.getAppId());
        oauthClient.setAccessTokenValidity(apiUserVo.getTokenExp());
        oauthClient.setRefreshTokenValidity(apiUserVo.getRefTokenExp());
        baseMapper.updateById(oauthClient);
    }

    @Override
    public void updatePwd(ApiUserVo apiUserVo) {
        AuthUser curUser = authUserMapper.selectById(AuthUtil.getUserId());
        String curPwd = Sm2Util.decrypt(curUser.getPassword());
        if (!curPwd.equals(Sm2Util.decrypt(apiUserVo.getUserPwd()))) {
            throw new RuntimeException("当前用户密码错误");
        }

        String newSecret = Sm2Util.decrypt(apiUserVo.getAppSecret());

        OauthClient oauthClient = new OauthClient();
        oauthClient.setClientSecret(newSecret);
        oauthClient.setClientId(apiUserVo.getAppId());
        baseMapper.updateById(oauthClient);
        AuthUser authUser = new AuthUser();
        authUser.setId(apiUserVo.getAppId());
        authUser.setPassword(Sm2Util.encrypt(newSecret));
        authUserMapper.updateById(authUser);
    }

    @Override
    public void delUser(String appId) {
        authUserMapper.deleteById(appId);
        baseMapper.deleteById(appId);
    }

    @Override
    public IPage<ApiUserVo> queryPageList(Page<?> page, UserParamVo userParam) {
        Page<AuthUser> userPage = (Page<AuthUser>) page;
        AuthUser user = new AuthUser();
        user.setType(UserType.USER_OAUTH2.getValue());
        if (StrUtil.isNotEmpty(userParam.getAppId())) {
            user.setId(userParam.getAppId());
        }
        QueryWrapper<AuthUser> queryWrapper = Wrappers.query(user);
        if (StrUtil.isNotEmpty(userParam.getNickName())) {
            queryWrapper.like("nick_name", userParam.getNickName());
        }
        Page<AuthUser> pageList =
                authUserMapper.selectPage(userPage, queryWrapper);
        List<AuthUser> list = pageList.getRecords();
        List<ApiUserVo> newList = new ArrayList<>();

        list.forEach(o -> {
            OauthClient oauthClient = baseMapper.selectById(o.getId());
            if (null != oauthClient) {
                ApiUserVo apiUserVo = new ApiUserVo();
                apiUserVo.setPhone(o.getPhone());
                apiUserVo.setEmail(o.getEmail());
                apiUserVo.setNickName(o.getNickName());
                apiUserVo.setAppId(o.getId());
                apiUserVo.setTokenExp(oauthClient.getAccessTokenValidity());
                apiUserVo.setRefTokenExp(oauthClient.getRefreshTokenValidity());
                apiUserVo.setStatus(o.getStatus());
                newList.add(apiUserVo);
            }
        });
        IPage<ApiUserVo> newPageList =
                PageUtil.copyNew(pageList);
        newPageList.setRecords(newList);
        return newPageList;
    }

    @Override
    public String getPassword(String appId, String userPwd) {
        AuthUser curUser = authUserMapper.selectById(AuthUtil.getUserId());
        String curPwd = Sm2Util.decrypt(curUser.getPassword());

        AuthUser authUser = authUserMapper.selectById(appId);
        if (null == authUser) {
            throw new RuntimeException("用户不存在");
        }
        if (!curPwd.equals(
                Sm2Util.decrypt(userPwd)
        )) {
            throw new RuntimeException("当前用户密码错误");
        }
        return Sm2Util.decrypt(authUser.getPassword());
    }

    @Override
    public void lockUser(String appId) {
        AuthUser user = new AuthUser();
        user.setId(appId);
        user.setStatus("1");
        authUserMapper.updateById(user);
        //如果当前用户存在则退出登录
        String token = StpUtil.getTokenValueByLoginId(appId);
        if (StrUtil.isNotEmpty(token)) {
            StpUtil.logoutByTokenValue(token);
            SaOAuth2Util.revokeAccessToken(token);
        }
    }

    @Override
    public void unlockUser(String appId) {
        AuthUser user = new AuthUser();
        user.setId(appId);
        user.setStatus("0");
        authUserMapper.updateById(user);
    }
}
