package org.simple.auth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wf.captcha.ArithmeticCaptcha;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.simple.auth.mapper.AuthUserMapper;
import org.simple.auth.model.AuthUser;
import org.simple.auth.service.AuthUserService;
import org.simple.base.constant.RedisConstant;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class AuthUserServiceImpl
        extends ServiceImpl<AuthUserMapper, AuthUser>
        implements AuthUserService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    public AuthUser queryUserByUserName(String userName,String email,String phone, String type) {
        return baseMapper.queryUserByUserName(userName,email,phone, type);
    }

    @Override
    public AuthUser queryUserByPhone(String phone, String type) {
        return baseMapper.queryUserByPhone(phone, type);
    }

    @Override
    public List<String> queryPermissions(String userId) {
        return baseMapper.queryPermissions(userId);
    }

    @Override
    public List<String> queryGroups(String userId) {
        return baseMapper.queryGroups(userId);
    }

    @Override
    public void addOneLoginError(String userId) {
        baseMapper.addOneLoginError(userId);
    }

    @Override
    public void updateLastLoginTime(String userId) {
        baseMapper.updateLastLoginTime(userId);
    }

    @Override
    public void resetLoginError(String userId) {
        baseMapper.resetLoginError(userId);
    }

    @Override
    public void getCode(HttpServletResponse response, String sp) throws IOException {
        //生成验证码
        ArithmeticCaptcha captcha = new ArithmeticCaptcha(111, 36);
        captcha.setLen(2);
        String result;
        result = captcha.text();
        //存储验证码到redis中
        redisTemplate.opsForValue().set(RedisConstant.CODE_STR + sp, result
                , RedisConstant.CODE_TIMEOUT, TimeUnit.SECONDS);
        response.setContentType(MediaType.IMAGE_JPEG_VALUE);
        // 为了设置验证码课实时刷新
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expire", 0);
        ServletOutputStream outputStream = response.getOutputStream();
        captcha.out(outputStream);
    }
}
