package org.simple.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.simple.auth.model.OauthClient;

@Mapper
public interface OauthClientMapper
        extends BaseMapper<OauthClient> {

    @Select("select * from oauth_client_details where client_id = #{clientId}")
    OauthClient queryDetailsByClientId(@Param("clientId") String clientId);
}
