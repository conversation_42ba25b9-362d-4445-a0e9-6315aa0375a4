package org.simple.openapi.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.simple.openapi.entity.ApiClientRight;

import java.io.IOException;
import java.util.List;


/**
 * @Copyright: frSimple
 * @Date: 2024-08-19 16:24:04
 * @Author: frSimple
 */
public interface ApiClientRightService
        extends IService<ApiClientRight> {

    List<Tree<String>> queryApiTree();

    List<String> getAuthApi(String appId);

    void saveAuth(ApiClientRight apiClientRight);

    XSSFWorkbook exportExcel(String appId) throws IOException, InvalidFormatException;

}