package org.simple.openapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;



/**
 * @Copyright: frSimple
 * @Desc: 客户端用户关系表实体
 * @Date: 2024-08-19 16:24:05
 * @Author: frSimple
 */

@Data
@NoArgsConstructor
@TableName(value = "api_client_user")
@Schema(description ="客户端用户关系表")
public class ApiClientUser implements Serializable{
@Serial
private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description ="主键ID")
    private String id;
    @Schema(description ="客户端用户ID")
    private String clientId;
    @Schema(description ="系统用户ID")
    private String userId;
}
