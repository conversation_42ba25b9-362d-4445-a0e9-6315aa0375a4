package org.simple.openapi.entity;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.simple.base.dto.BaseEntity;

import java.io.Serial;
import java.io.Serializable;


/**
 * @Copyright: frSimple
 * @Desc: 开放接口主表实体
 * @Date: 2024-08-19 16:24:07
 * @Author: frSimple
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "api_main", autoResultMap = true)
@Schema(description = "开放接口主表")
public class ApiMain extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "接口主键ID")
    private String no;
    @Schema(description = "接口名称")
    @TableField(condition = SqlCondition.LIKE)
    private String name;
    @Schema(description = "bean名称")
    @TableField(condition = SqlCondition.LIKE)
    private String beanName;
    @Schema(description = "bean方法")
    @TableField(condition = SqlCondition.LIKE)
    private String beanMethod;
    @Schema(description = "版本号")
    private String version;
    @Schema(description = "输入参数")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray inputParam;
    @Schema(description = "输出参数")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray outputParam;
    @Schema(description = "分类")
    private String classify;
    @Schema(description = "是否启用")
    private String status;
    @Schema(description = "接口编号")
    @TableField(condition = SqlCondition.LIKE)
    private String apiNo;
}
