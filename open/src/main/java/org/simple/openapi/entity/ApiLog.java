package org.simple.openapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * @Copyright: frSimple
 * @Desc: 接口日志表实体
 * @Date: 2024-08-21 11:02:50
 * @Author: frSimple
 */

@Data
@NoArgsConstructor
@TableName(value = "api_log")
@Schema(description = "接口日志表")
public class ApiLog implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    private String id;
    @Schema(description = "接口编号")
    private String apiNo;
    @Schema(description = "请求ip地址")
    private String ip;
    @Schema(description = "请求方式")
    private String method;
    @Schema(description = "代理对象")
    private String useragent;
    @Schema(description = "请求参数")
    private String param;
    @Schema(description = "请求用户登录名")
    private String userId;
    @Schema(description = "请求时间")
    private LocalDateTime createTime;
    @Schema(description = "请求时长（ms）")
    private String time;
    @Schema(description = "请求状态")
    private String status;
    @Schema(description = "错误原因")
    private String error;
    @Schema(description = "返回结果")
    private String outParam;
    @Schema(description = "用户appId")
    private String appId;
    @Schema(description = "版本号")
    private String version;
}
