package org.simple.openapi.controller;

import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.openapi.service.OpenApiService;
import org.simple.openapi.vo.OpenParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/openapi/call")
public class OpenController {

    @Resource
    private OpenApiService openApiService;

    @PostMapping
    public FrResult<?> openApi(@RequestBody(required = false) OpenParam openParam) {
        try {
            return openApiService.dealInterFace(openParam);
        } catch (Exception ex) {
            return FrResult.failed(ex.getMessage());
        }
    }
}
