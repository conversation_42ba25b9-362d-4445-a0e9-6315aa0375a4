# 设备管理系统移动端

基于 UniApp 开发的设备管理系统移动端应用，主要用于设备点检、巡检、维修等移动作业场景。

## 技术栈

- **框架**: UniApp
- **语言**: TypeScript
- **UI组件库**: uni-ui、uView UI
- **状态管理**: Pinia
- **网络请求**: uni.request 封装
- **图表**: uCharts
- **地图**: 高德地图 UniApp SDK

## 目录结构

```
mobile/
├── pages/                    # 页面目录
│   ├── index/               # 首页
│   ├── inspection/          # 点检模块
│   ├── patrol/              # 巡检模块
│   ├── maintenance/         # 维修模块
│   ├── asset/               # 设备档案
│   └── user/                # 用户中心
├── components/              # 组件目录
├── static/                  # 静态资源
├── store/                   # 状态管理
├── utils/                   # 工具类
├── api/                     # 接口定义
├── types/                   # 类型定义
├── manifest.json            # 应用配置
├── pages.json               # 页面配置
└── uni.scss                 # 全局样式
```

## 功能模块

### 1. 设备点检
- 点检任务列表
- 点检模板执行
- 照片上传
- 离线数据同步

### 2. 设备巡检
- 巡检路线规划
- 扫码定位设备
- 异常上报
- 巡检记录

### 3. 设备维修
- 故障上报
- 维修工单
- 配件管理
- 维修记录

### 4. 设备档案
- 设备查询
- 设备详情
- 二维码扫描
- 设备地图

## 开发规范

### 命名规范
- 页面文件：kebab-case
- 组件文件：PascalCase
- 变量/函数：camelCase
- 常量：SNAKE_CASE

### 代码规范
- 使用 TypeScript 强类型
- 遵循 ESLint 规则
- 组件化开发
- 统一错误处理

### 提交规范
- feat: 新功能
- fix: 修复问题
- docs: 文档修改
- style: 代码格式修改
- refactor: 代码重构
- test: 测试用例修改
- chore: 其他修改

## 环境配置

### 开发环境
- HBuilderX 3.8+
- Node.js 16+
- 微信开发者工具
- Android Studio / Xcode

### 依赖安装
```bash
npm install
```

### 运行项目
```bash
# 微信小程序
npm run dev:mp-weixin

# H5
npm run dev:h5

# App
npm run dev:app
```

## 部署说明

### 小程序发布
1. 使用微信开发者工具打开 dist/dev/mp-weixin
2. 上传代码到微信公众平台
3. 提交审核并发布

### App 打包
1. 使用 HBuilderX 云打包
2. 配置证书和描述文件
3. 生成安装包

### H5 部署
1. 打包生成静态文件
2. 上传到服务器
3. 配置 Nginx 代理

## 注意事项

1. **兼容性**: 确保在主流移动设备上正常运行
2. **性能**: 优化页面加载速度和内存使用
3. **离线**: 支持离线数据存储和同步
4. **安全**: 敏感数据加密传输和存储
5. **用户体验**: 友好的交互和错误提示

## 更新日志

### v1.0.0 (2024-12-07)
- 初始化项目架构
- 完成基础框架搭建
- 实现用户认证模块