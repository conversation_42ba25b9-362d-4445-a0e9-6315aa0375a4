// 通用类型定义

// API 响应基础类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 分页参数
export interface PageParams {
  current: number
  size: number
}

// 分页响应
export interface PageResult<T = any> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 用户信息
export interface UserInfo {
  id: string
  username: string
  nickname: string
  avatar?: string
  phone: string
  email?: string
  departmentId: string
  departmentName: string
  roles: string[]
  permissions: string[]
}

// 登录参数
export interface LoginParams {
  username: string
  password: string
  captcha?: string
  grant_type?: string
}

// 登录响应
export interface LoginResult {
  access_token: string
  refresh_token: string
  expires_in: number
  user: UserInfo
}

// 文件上传
export interface UploadFile {
  id: string
  name: string
  url: string
  size: number
  type: string
  uploadTime: string
}

// 选择器选项
export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
  children?: SelectOption[]
}

// 设备基础信息
export interface AssetInfo {
  id: string
  assetCode: string
  assetName: string
  categoryId: string
  categoryName: string
  departmentId: string
  departmentName: string
  locationId: string
  locationName: string
  status: string
  statusName: string
  qrCode?: string
  specification?: string
  manufacturer?: string
  model?: string
  serialNumber?: string
  purchaseDate?: string
  warrantyDate?: string
  lastMaintenanceDate?: string
  nextMaintenanceDate?: string
}

// 点检模板
export interface InspectionTemplate {
  id: string
  templateCode: string
  templateName: string
  categoryId: string
  categoryName: string
  cycleType: string
  cycleValue: number
  templateStatus: string
  items: InspectionTemplateItem[]
}

// 点检模板项目
export interface InspectionTemplateItem {
  id: string
  templateId: string
  itemCode: string
  itemName: string
  itemType: string
  dataType: string
  checkContent?: string
  checkStandard?: string
  unit?: string
  normalRange?: string
  minValue?: number
  maxValue?: number
  defaultValue?: string
  selectOptions?: string
  isRequired: string
  isKeyItem: string
  sortOrder: number
}

// 点检任务
export interface InspectionTask {
  id: string
  taskCode: string
  assetId: string
  assetCode: string
  assetName: string
  templateId: string
  templateName: string
  taskStatus: string
  assigneeId: string
  assigneeName: string
  planStartTime: string
  planEndTime: string
  actualStartTime?: string
  actualEndTime?: string
  completionRate?: number
  score?: number
  remark?: string
}

// 点检记录
export interface InspectionRecord {
  id: string
  taskId: string
  itemId: string
  itemName: string
  itemType: string
  dataType: string
  checkValue: string
  checkResult: string
  isNormal: boolean
  photos?: string[]
  remark?: string
  checkTime: string
  checkUser: string
}

// 巡检路线
export interface PatrolRoute {
  id: string
  routeCode: string
  routeName: string
  departmentId: string
  departmentName: string
  routeType: string
  estimatedDuration: number
  status: string
  checkPoints: PatrolCheckPoint[]
}

// 巡检检查点
export interface PatrolCheckPoint {
  id: string
  routeId: string
  assetId: string
  assetCode: string
  assetName: string
  locationId: string
  locationName: string
  sortOrder: number
  checkItems: string[]
  isRequired: boolean
}

// 巡检任务
export interface PatrolTask {
  id: string
  taskCode: string
  routeId: string
  routeName: string
  taskStatus: string
  assigneeId: string
  assigneeName: string
  planStartTime: string
  planEndTime: string
  actualStartTime?: string
  actualEndTime?: string
  completedPoints: number
  totalPoints: number
  completionRate: number
}

// 维修工单
export interface MaintenanceOrder {
  id: string
  orderCode: string
  assetId: string
  assetCode: string
  assetName: string
  faultType: string
  faultDescription: string
  urgencyLevel: string
  orderStatus: string
  reporterId: string
  reporterName: string
  reportTime: string
  assigneeId?: string
  assigneeName?: string
  assignTime?: string
  startTime?: string
  endTime?: string
  solution?: string
  cost?: number
  parts?: MaintenancePart[]
  photos?: string[]
}

// 维修配件
export interface MaintenancePart {
  id: string
  orderId: string
  partName: string
  partCode: string
  quantity: number
  unit: string
  unitPrice: number
  totalPrice: number
  supplier?: string
}

// 设备状态枚举
export enum AssetStatus {
  NORMAL = 'NORMAL',
  MAINTENANCE = 'MAINTENANCE',
  FAULT = 'FAULT',
  SCRAP = 'SCRAP'
}

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'PENDING',
  ASSIGNED = 'ASSIGNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// 点检周期类型枚举
export enum CycleType {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  ANNUAL = 'ANNUAL'
}

// 数据类型枚举
export enum DataType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  SELECT = 'SELECT',
  BOOLEAN = 'BOOLEAN'
}

// 通用状态
export interface CommonStatus {
  loading: boolean
  error: string | null
  refreshing: boolean
}

// 位置信息
export interface LocationInfo {
  latitude: number
  longitude: number
  address?: string
  accuracy?: number
}

// 应用配置
export interface AppConfig {
  apiBaseUrl: string
  uploadUrl: string
  version: string
  platform: string
  enableOffline: boolean
  syncInterval: number
}

// 离线数据
export interface OfflineData {
  id: string
  type: string
  data: any
  timestamp: number
  synced: boolean
}