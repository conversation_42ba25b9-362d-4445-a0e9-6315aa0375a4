// Flexbox 混合器
@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $lines;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin absolute-center-x {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

@mixin absolute-center-y {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

// 按钮样式
@mixin button-variant($bg-color, $text-color: $white, $border-color: $bg-color) {
  background-color: $bg-color;
  color: $text-color;
  border: 1rpx solid $border-color;
  border-radius: $border-radius-sm;
  padding: $spacing-sm $spacing-lg;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  text-align: center;
  transition: all $transition-fast;
  
  &:active {
    opacity: 0.8;
    transform: scale(0.98);
  }
  
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

// 卡片样式
@mixin card {
  background-color: $bg-primary;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  padding: $spacing-md;
  margin-bottom: $spacing-sm;
}

// 表单输入框样式
@mixin form-input {
  width: 100%;
  padding: $spacing-sm $spacing-md;
  border: 1rpx solid $border-color;
  border-radius: $border-radius-sm;
  font-size: $font-size-base;
  background-color: $bg-primary;
  transition: border-color $transition-fast;
  
  &:focus {
    border-color: $primary-color;
    outline: none;
  }
  
  &::placeholder {
    color: $text-hint;
  }
  
  &.error {
    border-color: $danger-color;
  }
  
  &:disabled {
    background-color: $bg-disabled;
    color: $text-disabled;
  }
}

// 状态标签样式
@mixin status-tag($bg-color, $text-color: $white) {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-sm;
  background-color: $bg-color;
  color: $text-color;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  text-align: center;
}

// 响应式设计
@mixin respond-to($breakpoint) {
  @if $breakpoint == 'sm' {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  }
  @if $breakpoint == 'md' {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  }
  @if $breakpoint == 'lg' {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  }
  @if $breakpoint == 'xl' {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
}

// 安全区域适配
@mixin safe-area-inset($property: padding, $direction: bottom) {
  #{$property}-#{$direction}: constant(safe-area-inset-#{$direction});
  #{$property}-#{$direction}: env(safe-area-inset-#{$direction});
}

// 加载动画
@mixin loading-spinner($size: 32rpx, $color: $primary-color) {
  width: $size;
  height: $size;
  border: 2rpx solid rgba($color, 0.2);
  border-left: 2rpx solid $color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 渐变背景
@mixin gradient-bg($start-color, $end-color, $direction: to bottom) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 毛玻璃效果
@mixin glass-effect($opacity: 0.8) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

// 分割线
@mixin divider($color: $border-color, $margin: $spacing-md) {
  margin: $margin 0;
  border-top: 1rpx solid $color;
}

// 列表项样式
@mixin list-item {
  @include flex-between;
  padding: $spacing-md 0;
  border-bottom: 1rpx solid $border-light;
  
  &:last-child {
    border-bottom: none;
  }
}

// 头像样式
@mixin avatar($size: 80rpx) {
  width: $size;
  height: $size;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  
  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}