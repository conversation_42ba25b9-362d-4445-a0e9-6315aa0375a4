import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, LoginParams, LoginResult } from '@/types'
import { login, getUserInfo, logout } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref<UserInfo | null>(null)
  
  // 认证令牌
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  
  // 权限列表
  const permissions = ref<string[]>([])
  
  // 角色列表  
  const roles = ref<string[]>([])
  
  // 登录状态
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  
  // 是否为管理员
  const isAdmin = computed(() => roles.value.includes('admin'))
  
  // 登录方法
  const loginAction = async (params: LoginParams): Promise<boolean> => {
    try {
      const result: LoginResult = await login(params)
      
      // 保存认证信息
      token.value = result.access_token
      refreshToken.value = result.refresh_token
      userInfo.value = result.user
      permissions.value = result.user.permissions || []
      roles.value = result.user.roles || []
      
      console.log('登录成功:', result.user.nickname)
      return true
      
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }
  
  // 退出登录
  const logoutAction = async (): Promise<void> => {
    try {
      if (token.value) {
        await logout()
      }
    } catch (error) {
      console.error('退出登录失败:', error)
    } finally {
      // 清除本地数据
      clearUserData()
    }
  }
  
  // 获取用户信息
  const getUserInfoAction = async (): Promise<void> => {
    try {
      const result = await getUserInfo()
      userInfo.value = result
      permissions.value = result.permissions || []
      roles.value = result.roles || []
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期，清除登录状态
      clearUserData()
      throw error
    }
  }
  
  // 检查token有效性
  const checkToken = async (): Promise<boolean> => {
    if (!token.value) {
      return false
    }
    
    try {
      await getUserInfoAction()
      return true
    } catch (error) {
      return false
    }
  }
  
  // 刷新token
  const refreshTokenAction = async (): Promise<boolean> => {
    if (!refreshToken.value) {
      return false
    }
    
    try {
      // TODO: 调用刷新token接口
      // const result = await refreshTokenApi(refreshToken.value)
      // token.value = result.access_token
      // refreshToken.value = result.refresh_token
      return true
    } catch (error) {
      console.error('刷新token失败:', error)
      clearUserData()
      return false
    }
  }
  
  // 清除用户数据
  const clearUserData = () => {
    userInfo.value = null
    token.value = ''
    refreshToken.value = ''
    permissions.value = []
    roles.value = []
  }
  
  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (isAdmin.value) {
      return true
    }
    return permissions.value.includes(permission)
  }
  
  // 检查角色
  const hasRole = (role: string): boolean => {
    return roles.value.includes(role)
  }
  
  // 检查多个权限（任一满足）
  const hasAnyPermission = (perms: string[]): boolean => {
    if (isAdmin.value) {
      return true
    }
    return perms.some(perm => permissions.value.includes(perm))
  }
  
  // 检查多个权限（全部满足）
  const hasAllPermissions = (perms: string[]): boolean => {
    if (isAdmin.value) {
      return true
    }
    return perms.every(perm => permissions.value.includes(perm))
  }
  
  // 更新用户信息
  const updateUserInfo = (info: Partial<UserInfo>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...info }
    }
  }
  
  // 更新头像
  const updateAvatar = (avatar: string) => {
    if (userInfo.value) {
      userInfo.value.avatar = avatar
    }
  }
  
  return {
    // 状态
    userInfo,
    token,
    refreshToken,
    permissions,
    roles,
    isLoggedIn,
    isAdmin,
    
    // 方法
    loginAction,
    logoutAction,
    getUserInfoAction,
    checkToken,
    refreshTokenAction,
    clearUserData,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    updateUserInfo,
    updateAvatar,
  }
}, {
  persist: {
    key: 'user-store',
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync,
    },
    paths: ['userInfo', 'token', 'refreshToken', 'permissions', 'roles']
  }
})