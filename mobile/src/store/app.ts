import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { AppConfig } from '@/types'

export const useAppStore = defineStore('app', () => {
  // 应用配置
  const config = ref<AppConfig>({
    apiBaseUrl: 'http://localhost:8080',
    uploadUrl: 'http://localhost:8080/api/common/upload',
    version: '1.0.0',
    platform: '',
    enableOffline: true,
    syncInterval: 30000, // 30秒
  })

  // 网络状态
  const isOnline = ref(true)
  
  // 应用状态
  const isInitialized = ref(false)
  const isBackground = ref(false)
  
  // 主题设置
  const theme = ref<'light' | 'dark'>('light')
  
  // 语言设置
  const language = ref('zh-CN')
  
  // 位置权限
  const locationPermission = ref<'authorized' | 'denied' | 'undetermined'>('undetermined')
  
  // 相机权限
  const cameraPermission = ref<'authorized' | 'denied' | 'undetermined'>('undetermined')

  // 初始化应用
  const initApp = async () => {
    try {
      // 获取系统信息
      const systemInfo = uni.getSystemInfoSync()
      config.value.platform = systemInfo.platform
      
      // 检查网络状态
      checkNetworkStatus()
      
      // 监听网络状态变化
      uni.onNetworkStatusChange((res) => {
        isOnline.value = res.isConnected
        console.log('网络状态变化:', res)
      })
      
      // 检查权限
      await checkPermissions()
      
      isInitialized.value = true
      console.log('应用初始化完成')
      
    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  }

  // 检查网络状态
  const checkNetworkStatus = () => {
    uni.getNetworkType({
      success: (res) => {
        isOnline.value = res.networkType !== 'none'
        console.log('网络类型:', res.networkType)
      }
    })
  }

  // 检查权限
  const checkPermissions = async () => {
    // 检查位置权限
    // #ifdef APP-PLUS
    const locationAuth = await new Promise((resolve) => {
      plus.android.requestPermissions(
        ['android.permission.ACCESS_FINE_LOCATION'],
        (result) => {
          resolve(result.granted.length > 0)
        },
        (error) => {
          console.error('位置权限检查失败:', error)
          resolve(false)
        }
      )
    })
    locationPermission.value = locationAuth ? 'authorized' : 'denied'
    // #endif
    
    // 检查相机权限
    // #ifdef APP-PLUS
    const cameraAuth = await new Promise((resolve) => {
      plus.android.requestPermissions(
        ['android.permission.CAMERA'],
        (result) => {
          resolve(result.granted.length > 0)
        },
        (error) => {
          console.error('相机权限检查失败:', error)
          resolve(false)
        }
      )
    })
    cameraPermission.value = cameraAuth ? 'authorized' : 'denied'
    // #endif
  }

  // 请求位置权限
  const requestLocationPermission = async () => {
    return new Promise((resolve) => {
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          locationPermission.value = 'authorized'
          resolve(true)
        },
        fail: () => {
          locationPermission.value = 'denied'
          uni.showModal({
            title: '权限申请',
            content: '需要位置权限来定位设备，请在设置中开启',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                uni.openSetting()
              }
            }
          })
          resolve(false)
        }
      })
    })
  }

  // 请求相机权限
  const requestCameraPermission = async () => {
    return new Promise((resolve) => {
      uni.authorize({
        scope: 'scope.camera',
        success: () => {
          cameraPermission.value = 'authorized'
          resolve(true)
        },
        fail: () => {
          cameraPermission.value = 'denied'
          uni.showModal({
            title: '权限申请',
            content: '需要相机权限来拍照上传，请在设置中开启',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                uni.openSetting()
              }
            }
          })
          resolve(false)
        }
      })
    })
  }

  // 更新配置
  const updateConfig = (newConfig: Partial<AppConfig>) => {
    config.value = { ...config.value, ...newConfig }
  }

  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
  }

  // 设置语言
  const setLanguage = (lang: string) => {
    language.value = lang
  }

  // 设置应用后台状态
  const setBackground = (background: boolean) => {
    isBackground.value = background
  }

  // 显示错误信息
  const showError = (message: string) => {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }

  // 显示成功信息
  const showSuccess = (message: string) => {
    uni.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    })
  }

  // 显示加载中
  const showLoading = (title: string = '加载中...') => {
    uni.showLoading({
      title,
      mask: true
    })
  }

  // 隐藏加载中
  const hideLoading = () => {
    uni.hideLoading()
  }

  // 获取当前位置
  const getCurrentLocation = async (): Promise<any> => {
    if (locationPermission.value !== 'authorized') {
      const granted = await requestLocationPermission()
      if (!granted) {
        throw new Error('位置权限被拒绝')
      }
    }

    return new Promise((resolve, reject) => {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          resolve({
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy,
            address: res.address
          })
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }

  // 扫描二维码
  const scanQRCode = async (): Promise<string> => {
    if (cameraPermission.value !== 'authorized') {
      const granted = await requestCameraPermission()
      if (!granted) {
        throw new Error('相机权限被拒绝')
      }
    }

    return new Promise((resolve, reject) => {
      uni.scanCode({
        success: (res) => {
          resolve(res.result)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }

  // 选择图片
  const chooseImage = async (count: number = 1): Promise<string[]> => {
    if (cameraPermission.value !== 'authorized') {
      const granted = await requestCameraPermission()
      if (!granted) {
        throw new Error('相机权限被拒绝')
      }
    }

    return new Promise((resolve, reject) => {
      uni.chooseImage({
        count,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: (res) => {
          resolve(res.tempFilePaths)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }

  // 预览图片
  const previewImage = (urls: string[], current: number = 0) => {
    uni.previewImage({
      urls,
      current: urls[current]
    })
  }

  return {
    // 状态
    config,
    isOnline,
    isInitialized,
    isBackground,
    theme,
    language,
    locationPermission,
    cameraPermission,
    
    // 方法
    initApp,
    checkNetworkStatus,
    checkPermissions,
    requestLocationPermission,
    requestCameraPermission,
    updateConfig,
    toggleTheme,
    setLanguage,
    setBackground,
    showError,
    showSuccess,
    showLoading,
    hideLoading,
    getCurrentLocation,
    scanQRCode,
    chooseImage,
    previewImage,
  }
}, {
  persist: {
    key: 'app-store',
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync,
    },
    paths: ['config', 'theme', 'language']
  }
})