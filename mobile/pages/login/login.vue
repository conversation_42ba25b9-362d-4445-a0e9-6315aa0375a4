<template>
  <view class="login-container">
    <!-- 自定义导航栏 -->
    <view class="navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <text class="app-title">设备管理系统</text>
      </view>
    </view>

    <!-- 登录内容 -->
    <view class="login-content">
      <!-- Logo 和标题 -->
      <view class="header">
        <image class="logo" src="/static/logo.png" mode="widthFix" />
        <text class="title">欢迎登录</text>
        <text class="subtitle">使用您的账号密码登录系统</text>
      </view>

      <!-- 登录表单 -->
      <view class="form-container">
        <uni-forms ref="formRef" :model="formData" :rules="rules" label-position="top">
          <!-- 用户名 -->
          <uni-forms-item name="username" label="用户名">
            <uni-easyinput
              v-model="formData.username"
              placeholder="请输入用户名"
              prefixIcon="person"
              :clear="false"
              :disabled="loading"
            />
          </uni-forms-item>

          <!-- 密码 -->
          <uni-forms-item name="password" label="密码">
            <uni-easyinput
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              prefixIcon="locked"
              :clear="false"
              :disabled="loading"
            />
          </uni-forms-item>

          <!-- 验证码 -->
          <uni-forms-item v-if="showCaptcha" name="captcha" label="验证码">
            <view class="captcha-container">
              <uni-easyinput
                v-model="formData.captcha"
                placeholder="请输入验证码"
                prefixIcon="checkmarkempty"
                :clear="false"
                :disabled="loading"
                class="captcha-input"
              />
              <image
                class="captcha-image"
                :src="captchaUrl"
                mode="aspectFit"
                @click="refreshCaptcha"
              />
            </view>
          </uni-forms-item>

          <!-- 记住密码 -->
          <view class="options">
            <uni-data-checkbox
              v-model="rememberPassword"
              :localdata="[{ text: '记住密码', value: true }]"
              :disabled="loading"
            />
            <text class="forgot-password" @click="handleForgotPassword">忘记密码？</text>
          </view>

          <!-- 登录按钮 -->
          <button
            class="login-btn"
            :class="{ disabled: loading }"
            :disabled="loading"
            @click="handleLogin"
          >
            <uni-icons v-if="loading" type="spinner-cycle" spin />
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </uni-forms>

        <!-- 其他登录方式 -->
        <view class="other-login">
          <view class="divider">
            <text class="divider-text">其他登录方式</text>
          </view>
          
          <view class="login-methods">
            <!-- 微信登录 -->
            <!-- #ifdef MP-WEIXIN -->
            <button class="method-btn weixin" open-type="getUserProfile" @getuserprofile="handleWeixinLogin">
              <uni-icons type="weixin" size="24" color="#fff" />
              <text>微信登录</text>
            </button>
            <!-- #endif -->
            
            <!-- 短信登录 -->
            <button class="method-btn sms" @click="handleSmsLogin">
              <uni-icons type="chatbubble" size="20" color="#fff" />
              <text>短信登录</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="footer">
      <text class="version">v{{ version }}</text>
      <text class="copyright">© 2024 设备管理系统</text>
    </view>

    <!-- 短信登录弹框 -->
    <uni-popup ref="smsPopup" type="center">
      <view class="sms-login-popup">
        <view class="popup-header">
          <text class="popup-title">短信登录</text>
          <uni-icons type="closeempty" size="20" @click="closeSmsPopup" />
        </view>
        
        <uni-forms ref="smsFormRef" :model="smsFormData" :rules="smsRules">
          <uni-forms-item name="phone" label="手机号">
            <uni-easyinput
              v-model="smsFormData.phone"
              placeholder="请输入手机号"
              type="number"
              maxlength="11"
            />
          </uni-forms-item>
          
          <uni-forms-item name="code" label="验证码">
            <view class="sms-code-container">
              <uni-easyinput
                v-model="smsFormData.code"
                placeholder="请输入验证码"
                type="number"
                maxlength="6"
                class="sms-code-input"
              />
              <button
                class="send-code-btn"
                :class="{ disabled: countdown > 0 || sendingCode }"
                :disabled="countdown > 0 || sendingCode"
                @click="sendSmsCode"
              >
                {{ countdown > 0 ? `${countdown}s` : sendingCode ? '发送中' : '发送验证码' }}
              </button>
            </view>
          </uni-forms-item>
        </uni-forms>
        
        <button class="sms-login-btn" :disabled="smsLoading" @click="handleSmsLoginSubmit">
          {{ smsLoading ? '登录中...' : '登录' }}
        </button>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { useAppStore } from '@/store/app'
import { sendSmsCode as sendSmsCodeApi } from '@/api/auth'
import type { LoginParams } from '@/types'

// 状态栏高度
const statusBarHeight = ref(0)

// Store
const userStore = useUserStore()
const appStore = useAppStore()

// 表单引用
const formRef = ref()
const smsFormRef = ref()
const smsPopup = ref()

// 加载状态
const loading = ref(false)
const smsLoading = ref(false)
const sendingCode = ref(false)

// 验证码相关
const showCaptcha = ref(false)
const captchaUrl = ref('')

// 短信验证码倒计时
const countdown = ref(0)
let countdownTimer: any = null

// 记住密码
const rememberPassword = ref(false)

// 版本信息
const version = ref('1.0.0')

// 登录表单数据
const formData = reactive<LoginParams>({
  username: '',
  password: '',
  captcha: '',
  grant_type: 'password'
})

// 短信登录表单数据
const smsFormData = reactive({
  phone: '',
  code: ''
})

// 表单验证规则
const rules = {
  username: {
    rules: [
      { required: true, errorMessage: '请输入用户名' },
      { minLength: 3, errorMessage: '用户名不能少于3个字符' }
    ]
  },
  password: {
    rules: [
      { required: true, errorMessage: '请输入密码' },
      { minLength: 6, errorMessage: '密码不能少于6个字符' }
    ]
  },
  captcha: {
    rules: [
      { required: true, errorMessage: '请输入验证码' }
    ]
  }
}

// 短信登录验证规则
const smsRules = {
  phone: {
    rules: [
      { required: true, errorMessage: '请输入手机号' },
      { pattern: /^1[3-9]\d{9}$/, errorMessage: '请输入正确的手机号' }
    ]
  },
  code: {
    rules: [
      { required: true, errorMessage: '请输入验证码' },
      { pattern: /^\d{6}$/, errorMessage: '验证码为6位数字' }
    ]
  }
}

onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 检查是否需要显示验证码
  checkCaptcha()
  
  // 读取记住的密码
  loadRememberPassword()
})

// 检查是否需要验证码
const checkCaptcha = () => {
  const loginAttempts = uni.getStorageSync('loginAttempts') || 0
  if (loginAttempts >= 3) {
    showCaptcha.value = true
    refreshCaptcha()
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  captchaUrl.value = `${appStore.config.apiBaseUrl}/api/auth/captcha?t=${Date.now()}`
}

// 读取记住的密码
const loadRememberPassword = () => {
  const savedLogin = uni.getStorageSync('savedLogin')
  if (savedLogin) {
    formData.username = savedLogin.username
    formData.password = savedLogin.password
    rememberPassword.value = true
  }
}

// 保存密码
const savePassword = () => {
  if (rememberPassword.value) {
    uni.setStorageSync('savedLogin', {
      username: formData.username,
      password: formData.password
    })
  } else {
    uni.removeStorageSync('savedLogin')
  }
}

// 处理登录
const handleLogin = async () => {
  if (loading.value) return
  
  try {
    // 验证表单
    await formRef.value.validate()
    
    loading.value = true
    
    // 执行登录
    const success = await userStore.loginAction(formData)
    
    if (success) {
      // 保存密码
      savePassword()
      
      // 清除登录失败次数
      uni.removeStorageSync('loginAttempts')
      
      appStore.showSuccess('登录成功')
      
      // 跳转到首页
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }
    
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 记录登录失败次数
    const attempts = (uni.getStorageSync('loginAttempts') || 0) + 1
    uni.setStorageSync('loginAttempts', attempts)
    
    // 3次失败后显示验证码
    if (attempts >= 3) {
      showCaptcha.value = true
      refreshCaptcha()
    }
    
    appStore.showError(error.message || '登录失败')
    
  } finally {
    loading.value = false
  }
}

// 微信登录
const handleWeixinLogin = (e: any) => {
  console.log('微信登录:', e)
  // TODO: 实现微信登录逻辑
  appStore.showError('微信登录功能开发中')
}

// 短信登录
const handleSmsLogin = () => {
  smsPopup.value.open()
}

// 关闭短信登录弹框
const closeSmsPopup = () => {
  smsPopup.value.close()
}

// 发送短信验证码
const sendSmsCode = async () => {
  if (sendingCode.value || countdown.value > 0) return
  
  try {
    // 验证手机号
    await smsFormRef.value.validateField('phone')
    
    sendingCode.value = true
    
    await sendSmsCodeApi(smsFormData.phone)
    
    appStore.showSuccess('验证码发送成功')
    
    // 开始倒计时
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer)
      }
    }, 1000)
    
  } catch (error: any) {
    appStore.showError(error.message || '发送验证码失败')
  } finally {
    sendingCode.value = false
  }
}

// 短信登录提交
const handleSmsLoginSubmit = async () => {
  if (smsLoading.value) return
  
  try {
    // 验证表单
    await smsFormRef.value.validate()
    
    smsLoading.value = true
    
    // 执行短信登录
    const success = await userStore.loginAction({
      username: smsFormData.phone,
      password: smsFormData.code,
      grant_type: 'sms'
    })
    
    if (success) {
      appStore.showSuccess('登录成功')
      closeSmsPopup()
      
      // 跳转到首页
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }
    
  } catch (error: any) {
    appStore.showError(error.message || '登录失败')
  } finally {
    smsLoading.value = false
  }
}

// 忘记密码
const handleForgotPassword = () => {
  uni.showModal({
    title: '忘记密码',
    content: '请联系管理员重置密码',
    showCancel: false
  })
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.navbar {
  .navbar-content {
    height: 88rpx;
    @include flex-center;
    
    .app-title {
      font-size: 36rpx;
      font-weight: 600;
      color: $white;
    }
  }
}

.login-content {
  flex: 1;
  padding: 0 60rpx;
  @include flex-column;
  justify-content: center;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
  
  .logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 40rpx;
  }
  
  .title {
    display: block;
    font-size: 48rpx;
    font-weight: 600;
    color: $white;
    margin-bottom: 16rpx;
  }
  
  .subtitle {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.form-container {
  background: $white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
}

.captcha-container {
  @include flex-between;
  gap: 24rpx;
  
  .captcha-input {
    flex: 1;
  }
  
  .captcha-image {
    width: 180rpx;
    height: 80rpx;
    border-radius: 8rpx;
    border: 1rpx solid $border-color;
  }
}

.options {
  @include flex-between;
  margin: 32rpx 0;
  
  .forgot-password {
    color: $primary-color;
    font-size: 28rpx;
  }
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: $primary-color;
  color: $white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  @include flex-center;
  gap: 16rpx;
  
  &.disabled {
    opacity: 0.6;
  }
  
  &:active {
    opacity: 0.8;
  }
}

.other-login {
  margin-top: 60rpx;
  
  .divider {
    position: relative;
    text-align: center;
    margin-bottom: 40rpx;
    
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1rpx;
      background: $border-color;
    }
    
    .divider-text {
      background: $white;
      padding: 0 24rpx;
      color: $text-secondary;
      font-size: 24rpx;
    }
  }
  
  .login-methods {
    @include flex-center;
    gap: 32rpx;
    
    .method-btn {
      @include flex-center;
      gap: 12rpx;
      padding: 20rpx 32rpx;
      border-radius: 44rpx;
      font-size: 28rpx;
      border: none;
      
      &.weixin {
        background: #07c160;
        color: $white;
      }
      
      &.sms {
        background: $secondary-color;
        color: $white;
      }
    }
  }
}

.footer {
  text-align: center;
  padding: 40rpx;
  
  .version, .copyright {
    display: block;
    color: rgba(255, 255, 255, 0.6);
    font-size: 24rpx;
    margin-bottom: 8rpx;
  }
}

.sms-login-popup {
  width: 600rpx;
  background: $white;
  border-radius: 24rpx;
  padding: 40rpx;
  
  .popup-header {
    @include flex-between;
    margin-bottom: 40rpx;
    
    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
    }
  }
  
  .sms-code-container {
    @include flex-between;
    gap: 24rpx;
    
    .sms-code-input {
      flex: 1;
    }
    
    .send-code-btn {
      padding: 0 24rpx;
      height: 80rpx;
      background: $primary-color;
      color: $white;
      border: none;
      border-radius: 8rpx;
      font-size: 24rpx;
      white-space: nowrap;
      
      &.disabled {
        opacity: 0.6;
      }
    }
  }
  
  .sms-login-btn {
    width: 100%;
    height: 80rpx;
    background: $primary-color;
    color: $white;
    border: none;
    border-radius: 40rpx;
    font-size: 32rpx;
    margin-top: 40rpx;
    
    &:disabled {
      opacity: 0.6;
    }
  }
}
</style>