<template>
  <view class="detail-page">
    <!-- 自定义导航栏 -->
    <view class="navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="nav-left" @click="goBack">
          <uni-icons type="arrowleft" size="20" color="#fff" />
        </view>
        <text class="nav-title">任务详情</text>
        <view class="nav-right">
          <view class="more-btn" @click="showMoreActions">
            <uni-icons type="more" size="20" color="#fff" />
          </view>
        </view>
      </view>
    </view>

    <!-- 任务基本信息 -->
    <view class="task-info-card">
      <view class="task-header">
        <text class="task-code">{{ taskDetail.taskCode }}</text>
        <view class="task-status" :class="getStatusClass(taskDetail.taskStatus)">
          {{ getStatusText(taskDetail.taskStatus) }}
        </view>
      </view>
      
      <view class="task-priority">
        <text class="priority-label">优先级:</text>
        <view class="priority-value" :class="getPriorityClass(taskDetail.priority)">
          {{ getPriorityText(taskDetail.priority) }}
        </view>
      </view>
      
      <view class="task-content">
        <view class="content-row">
          <text class="label">设备名称:</text>
          <text class="value">{{ taskDetail.assetName }}</text>
        </view>
        <view class="content-row">
          <text class="label">设备编号:</text>
          <text class="value">{{ taskDetail.assetCode }}</text>
        </view>
        <view class="content-row">
          <text class="label">设备位置:</text>
          <text class="value">{{ taskDetail.locationName }}</text>
        </view>
        <view class="content-row">
          <text class="label">点检模板:</text>
          <text class="value">{{ taskDetail.templateName }}</text>
        </view>
        <view class="content-row">
          <text class="label">执行人员:</text>
          <text class="value">{{ taskDetail.assigneeName }}</text>
        </view>
        <view class="content-row">
          <text class="label">创建时间:</text>
          <text class="value">{{ formatTime(taskDetail.createTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 计划时间 -->
    <view class="time-card">
      <view class="time-header">
        <uni-icons type="calendar" size="20" color="#007aff" />
        <text class="time-title">计划时间</text>
      </view>
      <view class="time-content">
        <view class="time-range">
          <text class="time-start">{{ formatTime(taskDetail.planStartTime) }}</text>
          <text class="time-separator">至</text>
          <text class="time-end">{{ formatTime(taskDetail.planEndTime) }}</text>
        </view>
        <view class="time-duration">
          <text>预计用时: {{ calculateDuration(taskDetail.planStartTime, taskDetail.planEndTime) }}</text>
        </view>
      </view>
    </view>

    <!-- 执行进度 -->
    <view class="progress-card" v-if="taskDetail.taskStatus === 'IN_PROGRESS'">
      <view class="progress-header">
        <uni-icons type="checkmarkempty" size="20" color="#34c759" />
        <text class="progress-title">执行进度</text>
      </view>
      <view class="progress-content">
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
        </view>
        <text class="progress-text">{{ progressPercent.toFixed(1) }}% ({{ completedItems }}/{{ totalItems }})</text>
      </view>
    </view>

    <!-- 检查项目预览 -->
    <view class="items-card">
      <view class="items-header">
        <text class="items-title">检查项目 ({{ templateItems.length }}项)</text>
        <text class="view-all" @click="viewAllItems">查看全部</text>
      </view>
      
      <view class="items-list">
        <view 
          v-for="(item, index) in templateItems.slice(0, 5)" 
          :key="item.id"
          class="item-preview"
        >
          <view class="item-info">
            <text class="item-name">{{ item.itemName }}</text>
            <view class="item-badges">
              <view v-if="item.isRequired === '1'" class="badge required">必填</view>
              <view v-if="item.isKeyItem === '1'" class="badge key">关键项</view>
            </view>
          </view>
          <view class="item-type">
            <text>{{ getItemTypeText(item.itemType) }}</text>
          </view>
        </view>
        
        <view v-if="templateItems.length > 5" class="more-items">
          <text>还有 {{ templateItems.length - 5 }} 个项目...</text>
        </view>
      </view>
    </view>

    <!-- 设备信息 -->
    <view class="asset-card">
      <view class="asset-header">
        <text class="asset-title">设备信息</text>
        <text class="asset-detail" @click="viewAssetDetail">查看详情</text>
      </view>
      
      <view class="asset-content">
        <view class="asset-row">
          <text class="label">设备型号:</text>
          <text class="value">{{ assetInfo.model || '暂无' }}</text>
        </view>
        <view class="asset-row">
          <text class="label">生产厂家:</text>
          <text class="value">{{ assetInfo.manufacturer || '暂无' }}</text>
        </view>
        <view class="asset-row">
          <text class="label">启用时间:</text>
          <text class="value">{{ assetInfo.enableTime ? formatTime(assetInfo.enableTime) : '暂无' }}</text>
        </view>
        <view class="asset-row">
          <text class="label">上次点检:</text>
          <text class="value">{{ assetInfo.lastInspectionTime ? formatTime(assetInfo.lastInspectionTime) : '暂无' }}</text>
        </view>
      </view>
    </view>

    <!-- 历史记录 -->
    <view class="history-card" v-if="historyRecords.length > 0">
      <view class="history-header">
        <text class="history-title">历史记录</text>
        <text class="view-more" @click="viewHistory">查看更多</text>
      </view>
      
      <view class="history-list">
        <view 
          v-for="record in historyRecords.slice(0, 3)" 
          :key="record.id"
          class="history-item"
        >
          <view class="history-info">
            <text class="history-action">{{ record.action }}</text>
            <text class="history-time">{{ formatTime(record.createTime) }}</text>
          </view>
          <text class="history-user">{{ record.userName }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        v-if="taskDetail.taskStatus === 'ASSIGNED'"
        class="btn btn-primary"
        @click="startTask"
      >
        开始执行
      </button>
      
      <button 
        v-if="taskDetail.taskStatus === 'IN_PROGRESS'"
        class="btn btn-primary"
        @click="continueTask"
      >
        继续执行
      </button>
      
      <button 
        v-if="taskDetail.taskStatus === 'COMPLETED'"
        class="btn btn-secondary"
        @click="viewResult"
      >
        查看结果
      </button>
      
      <button 
        v-if="canCancel"
        class="btn btn-outline"
        @click="cancelTask"
      >
        取消任务
      </button>
    </view>

    <!-- 更多操作菜单 -->
    <uni-popup ref="morePopup" type="bottom">
      <view class="more-actions">
        <view class="more-header">
          <text class="more-title">更多操作</text>
          <uni-icons type="closeempty" size="20" @click="hideMoreActions" />
        </view>
        
        <view class="action-list">
          <view class="action-item" @click="editTask">
            <uni-icons type="compose" size="20" color="#007aff" />
            <text>编辑任务</text>
          </view>
          
          <view class="action-item" @click="reassignTask">
            <uni-icons type="person" size="20" color="#ff9500" />
            <text>重新分配</text>
          </view>
          
          <view class="action-item" @click="copyTask">
            <uni-icons type="paperclip" size="20" color="#34c759" />
            <text>复制任务</text>
          </view>
          
          <view class="action-item" @click="shareTask">
            <uni-icons type="paperplane" size="20" color="#5856d6" />
            <text>分享任务</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useAppStore } from '@/store/app'
import dayjs from 'dayjs'

// Store
const appStore = useAppStore()

// 页面参数
const taskId = ref('')

// 系统状态栏高度
const statusBarHeight = ref(0)

// 弹框引用
const morePopup = ref()

// 任务详情
const taskDetail = reactive({
  id: '',
  taskCode: '',
  assetName: '',
  assetCode: '',
  locationName: '',
  templateName: '',
  assigneeName: '',
  taskStatus: '',
  priority: 1,
  createTime: '',
  planStartTime: '',
  planEndTime: ''
})

// 模板项目
const templateItems = ref([])

// 设备信息
const assetInfo = reactive({
  model: '',
  manufacturer: '',
  enableTime: '',
  lastInspectionTime: ''
})

// 历史记录
const historyRecords = ref([])

// 进度相关
const totalItems = computed(() => templateItems.value.length)
const completedItems = computed(() => templateItems.value.filter(item => item.checkValue).length)
const progressPercent = computed(() => {
  if (totalItems.value === 0) return 0
  return (completedItems.value / totalItems.value) * 100
})

// 是否可以取消
const canCancel = computed(() => {
  return ['ASSIGNED', 'IN_PROGRESS'].includes(taskDetail.taskStatus)
})

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  taskId.value = currentPage.options?.id || ''
  
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 加载任务详情
  loadTaskDetail()
})

// 加载任务详情
const loadTaskDetail = async () => {
  try {
    appStore.showLoading('加载中...')
    
    // TODO: 调用接口获取任务详情
    await loadTaskInfo()
    await loadTemplateItems()
    await loadAssetInfo()
    await loadHistoryRecords()
    
  } catch (error) {
    console.error('加载任务详情失败:', error)
    appStore.showError('加载失败')
  } finally {
    appStore.hideLoading()
  }
}

// 加载任务信息
const loadTaskInfo = async () => {
  // TODO: 调用接口获取任务信息
  Object.assign(taskDetail, {
    id: taskId.value,
    taskCode: 'TASK20241207001',
    assetName: '中央空调设备A1',
    assetCode: 'AC-001',
    locationName: 'A栋1楼大厅',
    templateName: '空调设备日检模板',
    assigneeName: '张三',
    taskStatus: 'ASSIGNED',
    priority: 2,
    createTime: '2024-12-07 08:00:00',
    planStartTime: '2024-12-07 09:00:00',
    planEndTime: '2024-12-07 11:00:00'
  })
}

// 加载模板项目
const loadTemplateItems = async () => {
  // TODO: 调用接口获取模板项目
  templateItems.value = [
    {
      id: '1',
      itemName: '运行状态检查',
      itemType: 'CHECK',
      isRequired: '1',
      isKeyItem: '1',
      checkValue: ''
    },
    {
      id: '2',
      itemName: '温度测量',
      itemType: 'MEASURE',
      isRequired: '1',
      isKeyItem: '0',
      checkValue: ''
    },
    {
      id: '3',
      itemName: '清洁程度',
      itemType: 'CHECK',
      isRequired: '0',
      isKeyItem: '0',
      checkValue: ''
    },
    {
      id: '4',
      itemName: '噪音检测',
      itemType: 'MEASURE',
      isRequired: '0',
      isKeyItem: '0',
      checkValue: ''
    },
    {
      id: '5',
      itemName: '滤网检查',
      itemType: 'CHECK',
      isRequired: '1',
      isKeyItem: '1',
      checkValue: ''
    },
    {
      id: '6',
      itemName: '电流检测',
      itemType: 'MEASURE',
      isRequired: '0',
      isKeyItem: '0',
      checkValue: ''
    }
  ]
}

// 加载设备信息
const loadAssetInfo = async () => {
  // TODO: 调用接口获取设备信息
  Object.assign(assetInfo, {
    model: 'VRV-X系列',
    manufacturer: '大金空调',
    enableTime: '2022-03-15 00:00:00',
    lastInspectionTime: '2024-12-06 10:30:00'
  })
}

// 加载历史记录
const loadHistoryRecords = async () => {
  // TODO: 调用接口获取历史记录
  historyRecords.value = [
    {
      id: '1',
      action: '任务创建',
      createTime: '2024-12-07 08:00:00',
      userName: '系统管理员'
    },
    {
      id: '2',
      action: '任务分配',
      createTime: '2024-12-07 08:05:00',
      userName: '李四'
    },
    {
      id: '3',
      action: '任务接收',
      createTime: '2024-12-07 08:10:00',
      userName: '张三'
    }
  ]
}

// 开始任务
const startTask = () => {
  uni.navigateTo({
    url: `/pages/inspection/execute/index?taskId=${taskId.value}`
  })
}

// 继续任务
const continueTask = () => {
  uni.navigateTo({
    url: `/pages/inspection/execute/index?taskId=${taskId.value}`
  })
}

// 查看结果
const viewResult = () => {
  uni.navigateTo({
    url: `/pages/inspection/record/index?taskId=${taskId.value}`
  })
}

// 取消任务
const cancelTask = async () => {
  try {
    await uni.showModal({
      title: '取消任务',
      content: '确定要取消这个任务吗？'
    })
    
    appStore.showLoading('取消中...')
    
    // TODO: 调用接口取消任务
    console.log('取消任务:', taskId.value)
    
    appStore.showSuccess('任务已取消')
    
    // 刷新页面数据
    loadTaskDetail()
    
  } catch (error) {
    if (error.confirm !== false) {
      console.error('取消任务失败:', error)
      appStore.showError('取消失败')
    }
  } finally {
    appStore.hideLoading()
  }
}

// 查看所有项目
const viewAllItems = () => {
  uni.navigateTo({
    url: `/pages/inspection/template-items/index?templateId=${taskDetail.templateId}`
  })
}

// 查看设备详情
const viewAssetDetail = () => {
  uni.navigateTo({
    url: `/pages/asset/detail/index?id=${taskDetail.assetId}`
  })
}

// 查看历史记录
const viewHistory = () => {
  uni.navigateTo({
    url: `/pages/inspection/history/index?taskId=${taskId.value}`
  })
}

// 显示更多操作
const showMoreActions = () => {
  morePopup.value.open()
}

// 隐藏更多操作
const hideMoreActions = () => {
  morePopup.value.close()
}

// 编辑任务
const editTask = () => {
  hideMoreActions()
  uni.navigateTo({
    url: `/pages/inspection/edit/index?id=${taskId.value}`
  })
}

// 重新分配
const reassignTask = () => {
  hideMoreActions()
  uni.navigateTo({
    url: `/pages/inspection/reassign/index?id=${taskId.value}`
  })
}

// 复制任务
const copyTask = () => {
  hideMoreActions()
  appStore.showToast('复制任务功能开发中')
}

// 分享任务
const shareTask = () => {
  hideMoreActions()
  appStore.showToast('分享任务功能开发中')
}

// 获取状态样式类
const getStatusClass = (status: string) => {
  return `status-${status.toLowerCase()}`
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    ASSIGNED: '已分配',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

// 获取优先级样式类
const getPriorityClass = (priority: number) => {
  return `priority-${priority}`
}

// 获取优先级文本
const getPriorityText = (priority: number) => {
  const priorityMap = {
    1: '低',
    2: '中',
    3: '高'
  }
  return priorityMap[priority] || '未知'
}

// 获取项目类型文本
const getItemTypeText = (type: string) => {
  const typeMap = {
    CHECK: '检查',
    MEASURE: '测量',
    RECORD: '记录',
    MAINTAIN: '维护',
    CLEAN: '清洁'
  }
  return typeMap[type] || type
}

// 计算时长
const calculateDuration = (startTime: string, endTime: string) => {
  const start = dayjs(startTime)
  const end = dayjs(endTime)
  const diffMinutes = end.diff(start, 'minute')
  
  if (diffMinutes < 60) {
    return `${diffMinutes}分钟`
  } else {
    const hours = Math.floor(diffMinutes / 60)
    const minutes = diffMinutes % 60
    return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`
  }
}

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 返回
const goBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.detail-page {
  min-height: 100vh;
  background: $bg-secondary;
}

.navbar {
  background: linear-gradient(135deg, #007aff, #5856d6);
  
  .navbar-content {
    height: 88rpx;
    @include flex-between;
    padding: 0 32rpx;
    
    .nav-left,
    .nav-right {
      width: 120rpx;
    }
    
    .nav-left {
      @include flex-row;
      align-items: center;
    }
    
    .nav-title {
      font-size: 32rpx;
      font-weight: 500;
      color: $white;
    }
    
    .nav-right {
      @include flex-row;
      align-items: center;
      justify-content: flex-end;
      
      .more-btn {
        width: 64rpx;
        height: 64rpx;
        @include flex-center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
      }
    }
  }
}

.task-info-card,
.time-card,
.progress-card,
.items-card,
.asset-card,
.history-card {
  background: $white;
  margin: 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  
  &:first-child {
    margin-top: 32rpx;
  }
}

.task-info-card {
  .task-header {
    @include flex-between;
    margin-bottom: 24rpx;
    
    .task-code {
      font-size: 32rpx;
      font-weight: 500;
      color: $text-primary;
    }
    
    .task-status {
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      
      &.status-assigned {
        background: rgba(0, 122, 255, 0.1);
        color: #007aff;
      }
      
      &.status-in_progress {
        background: rgba(52, 199, 89, 0.1);
        color: #34c759;
      }
      
      &.status-completed {
        background: rgba(142, 142, 147, 0.1);
        color: #8e8e93;
      }
      
      &.status-cancelled {
        background: rgba(255, 59, 48, 0.1);
        color: #ff3b30;
      }
    }
  }
  
  .task-priority {
    @include flex-row;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 24rpx;
    
    .priority-label {
      font-size: 28rpx;
      color: $text-secondary;
    }
    
    .priority-value {
      padding: 6rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      
      &.priority-1 {
        background: rgba(142, 142, 147, 0.1);
        color: #8e8e93;
      }
      
      &.priority-2 {
        background: rgba(255, 149, 0, 0.1);
        color: #ff9500;
      }
      
      &.priority-3 {
        background: rgba(255, 59, 48, 0.1);
        color: #ff3b30;
      }
    }
  }
  
  .task-content {
    .content-row {
      @include flex-row;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        width: 160rpx;
        font-size: 28rpx;
        color: $text-secondary;
      }
      
      .value {
        flex: 1;
        font-size: 28rpx;
        color: $text-primary;
      }
    }
  }
}

.time-card {
  .time-header {
    @include flex-row;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 24rpx;
    
    .time-title {
      font-size: 30rpx;
      font-weight: 500;
      color: $text-primary;
    }
  }
  
  .time-content {
    .time-range {
      @include flex-row;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 12rpx;
      
      .time-start,
      .time-end {
        font-size: 28rpx;
        color: $text-primary;
        font-weight: 500;
      }
      
      .time-separator {
        font-size: 24rpx;
        color: $text-secondary;
      }
    }
    
    .time-duration {
      font-size: 24rpx;
      color: $text-secondary;
    }
  }
}

.progress-card {
  .progress-header {
    @include flex-row;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 24rpx;
    
    .progress-title {
      font-size: 30rpx;
      font-weight: 500;
      color: $text-primary;
    }
  }
  
  .progress-content {
    .progress-bar {
      width: 100%;
      height: 8rpx;
      background: $gray-200;
      border-radius: 4rpx;
      overflow: hidden;
      margin-bottom: 12rpx;
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #34c759, #30d158);
        border-radius: 4rpx;
        transition: width 0.3s ease;
      }
    }
    
    .progress-text {
      font-size: 24rpx;
      color: $text-secondary;
    }
  }
}

.items-card {
  .items-header {
    @include flex-between;
    margin-bottom: 24rpx;
    
    .items-title {
      font-size: 30rpx;
      font-weight: 500;
      color: $text-primary;
    }
    
    .view-all {
      font-size: 24rpx;
      color: $primary-color;
    }
  }
  
  .items-list {
    .item-preview {
      @include flex-between;
      padding: 20rpx 0;
      border-bottom: 1rpx solid $border-light;
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-info {
        flex: 1;
        @include flex-row;
        align-items: center;
        gap: 16rpx;
        
        .item-name {
          font-size: 28rpx;
          color: $text-primary;
        }
        
        .item-badges {
          @include flex-row;
          gap: 8rpx;
          
          .badge {
            padding: 4rpx 8rpx;
            border-radius: 6rpx;
            font-size: 20rpx;
            
            &.required {
              background: #ff3b30;
              color: $white;
            }
            
            &.key {
              background: #ff9500;
              color: $white;
            }
          }
        }
      }
      
      .item-type {
        padding: 6rpx 12rpx;
        background: rgba(0, 122, 255, 0.1);
        color: $primary-color;
        border-radius: 8rpx;
        font-size: 20rpx;
      }
    }
    
    .more-items {
      padding: 20rpx 0;
      text-align: center;
      
      text {
        font-size: 24rpx;
        color: $text-secondary;
      }
    }
  }
}

.asset-card {
  .asset-header {
    @include flex-between;
    margin-bottom: 24rpx;
    
    .asset-title {
      font-size: 30rpx;
      font-weight: 500;
      color: $text-primary;
    }
    
    .asset-detail {
      font-size: 24rpx;
      color: $primary-color;
    }
  }
  
  .asset-content {
    .asset-row {
      @include flex-row;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        width: 160rpx;
        font-size: 28rpx;
        color: $text-secondary;
      }
      
      .value {
        flex: 1;
        font-size: 28rpx;
        color: $text-primary;
      }
    }
  }
}

.history-card {
  .history-header {
    @include flex-between;
    margin-bottom: 24rpx;
    
    .history-title {
      font-size: 30rpx;
      font-weight: 500;
      color: $text-primary;
    }
    
    .view-more {
      font-size: 24rpx;
      color: $primary-color;
    }
  }
  
  .history-list {
    .history-item {
      @include flex-between;
      padding: 20rpx 0;
      border-bottom: 1rpx solid $border-light;
      
      &:last-child {
        border-bottom: none;
      }
      
      .history-info {
        flex: 1;
        @include flex-column;
        
        .history-action {
          font-size: 28rpx;
          color: $text-primary;
          margin-bottom: 8rpx;
        }
        
        .history-time {
          font-size: 24rpx;
          color: $text-secondary;
        }
      }
      
      .history-user {
        font-size: 24rpx;
        color: $text-secondary;
      }
    }
  }
}

.action-buttons {
  @include flex-row;
  gap: 16rpx;
  padding: 32rpx;
  
  .btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    border: none;
    
    &.btn-primary {
      background: $primary-color;
      color: $white;
    }
    
    &.btn-secondary {
      background: $secondary-color;
      color: $white;
    }
    
    &.btn-outline {
      background: transparent;
      color: $primary-color;
      border: 1rpx solid $primary-color;
    }
  }
}

.more-actions {
  background: $white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx;
  
  .more-header {
    @include flex-between;
    margin-bottom: 32rpx;
    
    .more-title {
      font-size: 32rpx;
      font-weight: 500;
      color: $text-primary;
    }
  }
  
  .action-list {
    .action-item {
      @include flex-row;
      align-items: center;
      gap: 24rpx;
      padding: 32rpx 0;
      border-bottom: 1rpx solid $border-light;
      
      &:last-child {
        border-bottom: none;
      }
      
      text {
        font-size: 28rpx;
        color: $text-primary;
      }
    }
  }
}
</style>