<template>
  <div>
    <div class="sp-left-header">
      <div ref="searchRef" v-if="searchShow">
        <t-row :gutter="[10, 10]" style="padding-bottom: 10px">
          <t-col :flex="10" class="search-header-query">
            <slot name="frSimpleQuery" />
          </t-col>
          <t-col :flex="1" class="search-header-btn">
            <t-button theme="primary" :style="{ width: '50px' }" variant="outline" @click="firstFetch">查询</t-button>
            <t-button theme="default" :style="{ width: '50px' }" @click="reset">重置</t-button>
          </t-col>
        </t-row>
      </div>
      <t-row :gutter="[10, 10]" align="center" style="height: 34px">
        <t-col :flex="1">
          <slot name="frSimpleBtn" />
        </t-col>
        <t-col :flex="1" class="search-header">
          <t-space size="6px">
            <t-button
              v-if="isShowSearch"
              variant="text"
              size="small"
              @click="
                () => {
                  searchShow = !searchShow;
                }
              "
            >
              <template #icon> <SearchIcon /> </template>搜索
            </t-button>
            <t-button variant="text" size="small" @click="firstFetch">
              <template #icon> <RefreshIcon /> </template>刷新
            </t-button>
            <t-button variant="text" size="small" @click="downLoadExcel">
              <template #icon> <FileDownloadIcon /> </template>导出
            </t-button>
            <t-popup expand-animation placement="bottom" :showArrow="true" trigger="click">
              <template #content>
                <div class="unselectable" style="padding: 5px">
                  <t-space size="4px" direction="vertical">
                    <t-checkbox
                      v-for="(item, index) in colConfig"
                      :key="item.colKey"
                      v-model="item.selected"
                      :value="item.colKey"
                      @change="filterCol"
                      >{{ item.title }}</t-checkbox
                    >
                  </t-space>
                </div>
              </template>
              <t-button variant="text" size="small">
                <template #icon> <SettingIcon /> </template>设置
              </t-button>
            </t-popup>
          </t-space>
        </t-col>
      </t-row>
    </div>
    <t-table
      v-if="!isTree"
      :row-key="rowKey"
      :data="data"
      :max-height="tableMaxHeight"
      v-model:selected-row-keys="selectedRowKeys"
      :select-on-row-click="true"
      :columns="computedCol"
      :table-layout="'fixed'"
      :pagination="isPagination ? pagination : null"
      v-model:displayColumns="displayCol"
      @page-change="onPageChange"
      :loading="dataLoading"
      :loadingProps="{ size: '23px', text: '加载中...' }"
      :rowSelectionAllowUncheck="true"
    >
      <template v-for="item in columns" #[item.colKey]="{ row, rowIndex }">
        <slot :name="item.colKey" :row="row" :rowIndex="rowIndex">
          {{ row ? row[item.colKey] : '' }}
        </slot>
      </template>
    </t-table>
    <t-enhanced-table
      v-if="isTree"
      :row-key="rowKey"
      :data="data"
      :max-height="tableMaxHeight"
      v-model:selected-row-keys="selectedRowKeys"
      :columns="computedCol"
      :select-on-row-click="true"
      :table-layout="'fixed'"
      v-model:displayColumns="displayCol"
      @page-change="onPageChange"
      :loading="dataLoading"
      :loadingProps="{ size: '23px', text: '加载中...' }"
      :tree="{
        childrenKey: 'children',
        treeNodeColumnIndex: 0,
      }"
      :rowSelectionAllowUncheck="true"
    >
      <template v-for="item in columns" #[item.colKey]="{ row, rowIndex }">
        <slot :name="item.colKey" :row="row" :rowIndex="rowIndex">
          {{ row ? row[item.colKey] : '' }}
        </slot>
      </template>
    </t-enhanced-table>
  </div>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance, onMounted, computed, watch, nextTick } from 'vue';
import { SettingIcon, FileDownloadIcon, FileImportIcon, RefreshIcon, SearchIcon } from 'tdesign-icons-vue-next';
import { publicInterface, exportExcel } from '@/api/common';
import { MessagePlugin } from 'tdesign-vue-next';
import _ from 'lodash';

const tableNoPageHeight = getCurrentInstance().appContext.config.globalProperties.$frGlobal.tableNoPageHeight;
const tableHeight = getCurrentInstance().appContext.config.globalProperties.$frGlobal.tableHeight;

const searchHeight = ref(0);

const tableMaxHeight = computed(() => {
  if (props.maxHeight) {
    return props.maxHeight;
  }
  if (props.isPagination && !props.isTree) {
    let diffHeight = tableHeight.value + props.diffHeight;
    return `calc(100vh - ${diffHeight + searchHeight.value}px)`;
  } else {
    let diffHeight = tableNoPageHeight.value + props.diffHeight;
    return `calc(100vh - ${diffHeight + searchHeight.value}px)`;
  }
});

const selectedRowKeys = ref([]);
const emit = defineEmits(['update:params', 'successBack']);

const displayCol = ref([]);

type QueryData = {
  url: string;
  method: string;
};

const searchShow = ref(false);
const searchRef = ref(null);
watch(
  () => searchShow.value,
  (val, oldVal) => {
    if (val) {
      nextTick(() => {
        searchHeight.value = searchRef.value.offsetHeight;
      });
    } else {
      searchHeight.value = 0;
    }
  },
);

const props = defineProps({
  params: {
    type: Object,
    required: true,
  },
  orders: {
    type: Array,
    required: false,
  },
  columns: {
    type: Object as () => Columns,
    required: true,
  },
  request: {
    type: Object as () => QueryData,
    required: true,
  },
  isPagination: {
    type: Boolean,
    default: true,
  },
  isLoad: {
    type: Boolean,
    default: true,
  },
  maxHeight: {
    type: String,
  },
  isTree: {
    type: Boolean,
    default: false,
  },
  isShowSearch: {
    type: Boolean,
    default: true,
  },
  diffHeight: {
    type: Number,
    default: 0,
  },
  rowKey: {
    type: String,
    default: 'id',
  },
  showCheck: {
    type: Boolean,
    default: false,
  },
  selectType: {
    type: String,
    default: 'multiple',
  },
  extKey: {
    type: String,
    default: '',
  },
});

const reset = async () => {
  let res = _.mapValues(props.params, (value, key) => {
    return props.extKey.includes(key) ? value : null;
  });
  await emit('update:params', res);
  firstFetch();
};

const computedCol = computed(() => {
  let col = [] as Columns;
  if (props.showCheck) {
    col.push({
      colKey: 'row-select',
      type: props.selectType as any,
      width: 46,
    });
    col = col.concat(props.columns);
  } else {
    col = props.columns;
  }
  return col;
});

const firstFetch = () => {
  pagination.value.current = 1;
  fetchData();
};

//table列表
const data = ref([]);
const pagination = ref({
  pageSize: 20,
  total: 0,
  current: 1,
  size: 'small',
});
const onPageChange = async (pageInfo) => {
  pagination.value.current = pageInfo.current;
  pagination.value.pageSize = pageInfo.pageSize;
  await fetchData();
};
const dataLoading = ref(false);
const fetchData = async () => {
  data.value = [];
  dataLoading.value = true;
  try {
    let formData = {};
    let newParams = coverParams();
    if (props.request.method.toUpperCase() === 'GET') {
      formData['params'] = {
        ...newParams,
      };
      if (props.isPagination) {
        formData['params']['size'] = pagination.value.pageSize;
        formData['params']['current'] = pagination.value.current;
      }
    } else {
      formData['data'] = {
        ...newParams,
        orders: props.orders,
      };
      if (props.isPagination) {
        formData['data']['size'] = pagination.value.pageSize;
        formData['data']['current'] = pagination.value.current;
      }
    }

    let res = await publicInterface({
      url: props.request.url,
      method: props.request.method,
      ...formData,
    });
    if (res.code === 0) {
      if (props.isPagination) {
        data.value = res.data.records;
        pagination.value.total = res.data.total;
      } else {
        data.value = res.data;
      }
      emit('successBack', data.value);
      addRowKeyVal();
    } else {
      MessagePlugin.error(res.msg);
    }
  } catch (er) {
    MessagePlugin.error(er.message);
  } finally {
    dataLoading.value = false;
  }
};

const coverParams = () => {
  return _.mapValues(props.params, (value) => {
    if (_.isArray(value)) {
      return value.join(',');
    } else {
      return value;
    }
  });
};

//如果rowkey不存在，随机生成一个字符串作为rowkey的value值
const addRowKeyVal = () => {
  data.value = data.value.map((row) => {
    if (!row[props.rowKey]) {
      row[props.rowKey] = 'row_' + Math.random().toString(36).substring(2, 15);
    }
    return row;
  });
};

const filterCol = () => {
  let disDol = [];
  colConfig.value.map((row) => {
    if (row.selected) {
      disDol.push(row.colKey);
    }
  });
  if (props.showCheck) {
    disDol.unshift('row-select');
  }
  displayCol.value = disDol;
};

const colConfig = ref([]);
const initColConfig = () => {
  colConfig.value = [];
  props.columns.map((row) => {
    colConfig.value.push({
      colKey: row.colKey,
      title: row.title,
      selected: true,
    });
  });
};

const downLoadExcel = async () => {
  let msgLoad = MessagePlugin.loading({
    duration: 0,
    content: '正在下载中...',
  });
  let res = await exportExcel({
    column: props.columns,
    data: data.value,
  });
  MessagePlugin.close(msgLoad);
  let blob = new Blob([res]);
  let downloadElement = document.createElement('a');
  let href = window.URL.createObjectURL(blob); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = Date.now() + '.xlsx'; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
  window.URL.revokeObjectURL(href); //释放掉blob对象
};

onMounted(async () => {
  await initColConfig();
  await filterCol();
  if (props.isLoad) {
    await fetchData();
  }
});

const loadData = async (isFirst: boolean) => {
  if (isFirst) {
    pagination.value.current = 1;
  }
  await fetchData();
};

const clearData = () => {
  data.value = [];
};
const getSelectData = () => {
  return selectedRowKeys.value;
};
const getData = () => {
  return data.value;
};
const setData = (newData) => {
  data.value = newData;
};
defineExpose({
  loadData,
  clearData,
  getSelectData,
  getData,
  setData,
});
</script>

<style lang="less" scoped>
.sp-left-header {
  padding-bottom: 10px;
}
.sp-left-header .search-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}
.sp-left-header .search-header-query {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 8px;
}
.sp-left-header .search-header-btn {
  display: flex;
  align-items: start;
  justify-content: flex-end;
  gap: 6px;
}

.unselectable {
  -webkit-user-select: none; /* Safari 3.1+ */
  -moz-user-select: none; /* Firefox 2+ */
  -ms-user-select: none; /* IE 10+ */
  user-select: none; /* 标准语法 */
}
</style>
