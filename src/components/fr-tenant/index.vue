<template>
  <t-select
    clearable
    :placeholder="placeholder"
    :popup-props="{ overlayClassName: 'tdesign-demo-select__overlay-option' }"
    :multiple="false"
    :loading="addForm.loadData"
    :style="{ width: width }"
    :onChange="changeVal"
    @popup-visible-change="showPanel"
    @clear="handleClear"
    :label="label"
    v-model="selectVal.val"
    :disable="disabled"
  >
    <template #valueDisplay="{ value }">
      <t-tag v-if="value && selectVal.label">{{ selectVal.label }}</t-tag>
    </template>
    <template #panelTopContent>
      <div style="padding: 6px 6px 0 6px">
        <t-input v-model="addForm.text" placeholder="请输入机构名称模糊查询" @change="onSearch(null)" />
      </div>
    </template>
    <t-option v-for="item in options" :key="item.value" :value="item.value" :label="item.label">
      <div class="tdesign-demo__user-option">
        <t-avatar>{{ item.label.substring(0, 2) }}</t-avatar>
        <div class="tdesign-demo__user-option-info">
          <div>{{ item.label }}</div>
          <div class="tdesign-demo__user-option-desc">{{ item.description }}</div>
        </div>
      </div>
    </t-option>
  </t-select>
</template>

<script setup lang="tsx">
import { reactive, ref, watch, nextTick } from 'vue';
import { tenantList } from '@/api/system/tenant';

const props = defineProps({
  modelValue: {
    type: String,
  },
  width: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  label: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const selectVal = ref({
  val: '',
  label: '',
});
const emit = defineEmits(['update:modelValue', 'change', 'clear']);

const handleClear = async () => {
  selectVal.value.label = '';
  await emit('update:modelValue', '');
  await emit('clear');
};

const options = ref<CommonArray>([]);
const addForm = reactive({
  loadData: false,
  text: '',
});

const changeVal = async (value, context) => {
  if (value) {
    selectVal.value.label = context.option.label;
  } else {
    selectVal.value.label = '';
  }
  await emit('update:modelValue', value);
  await emit('change', value);
};

const showPanel = (visible: boolean) => {
  if (visible && options.value.length === 0) {
    onSearch();
  }
};

const initData = async (str, val) => {
  addForm.text = str;
  await onSearch();
  options.value.map((row) => {
    if (row.value === val) {
      selectVal.value.label = row.label;
    }
  });
  selectVal.value.val = val;
  emit('update:modelValue', val);
};

const initDataById = async (val) => {
  await onSearch(val);
  options.value.map((row) => {
    if (row.value === val) {
      selectVal.value.label = row.label;
    }
  });
  selectVal.value.val = val;
  emit('update:modelValue', val);
};

watch(
  () => props.modelValue,
  (newVal, oldVal) => {
    if (!newVal) {
      nextTick(() => {
        addForm.text = '';
        addForm.loadData = false;
        options.value = [];
        selectVal.value.val = '';
        selectVal.value.label = '';
      });
    }
  },
);

const clearData = () => {
  addForm.text = '';
  addForm.loadData = false;
  options.value = [];
  selectVal.value.val = '';
  selectVal.value.label = '';
};

defineExpose({
  initData,
  clearData,
  initDataById,
});

const onSearch = async (val?) => {
  addForm.loadData = true;
  let fOptions = [];
  let res;
  if (val) {
    res = await tenantList({
      id: val,
    });
  } else {
    res = await tenantList({
      name: addForm.text,
    });
  }
  if (res.data.records.length > 0) {
    res.data.records.map((row) => {
      fOptions.push({
        label: row.name,
        value: row.id,
        description: row.email,
      });
    });
  }
  options.value = fOptions;
  addForm.loadData = false;
};
</script>

<style>
.tdesign-demo__user-option {
  display: flex;
  align-items: center;
}

.tdesign-demo__user-option-desc {
  font-size: 14px;
  color: var(--td-text-color-secondary);
}

.tdesign-demo__user-option-info {
  margin-left: 16px;
}

.tdesign-demo-select__overlay-option .t-select-option {
  height: 100%;
  padding: 8px;
}
</style>
