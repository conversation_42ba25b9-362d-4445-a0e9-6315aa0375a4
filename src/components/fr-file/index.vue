<template>
  <div>
    <t-upload
      ref="uploadRef3"
      v-model="files3"
      :abridgeName="[10, 7]"
      :multiple="true"
      :max="max"
      :disabled="disabled"
      :sizeLimit="size * 1024"
      :auto-upload="autoUpload"
      :upload-all-files-in-one-request="uploadInOneRequest"
      :allow-upload-duplicate-file="true"
      :is-batch-upload="isBatchUpload"
      :format-response="formatResponse"
      :action="postUrl"
      :style="{ marginLeft: '60px' }"
      :headers="headersJson"
      :accept="accept"
      @fail="handleFail"
      @change="handleChange"
      @waiting-upload-files-change="uploadFileChange"
      @validate="onValidate"
    >
      <template #fileListDisplay>
        <div>
          <div
            v-for="(file, index) in fileAllList"
            :key="file.raw.name"
            class="t-upload__single-display-text t-upload__display-text--margin"
            style="display: flex; align-items: center; gap: 5px"
          >
            <span>{{ file.raw.name }}</span>
            <TimeFilledIcon v-if="file.status === 'waiting'" />
            <ErrorCircleFilledIcon v-if="file.status === 'fail'" />
            <t-loading v-if="file.status === 'progress'" :text="file.percent + '%'" size="small"></t-loading>
            <DeleteIcon
              v-if="file.status && (file.status === 'success' || file.status === 'fail')"
              class="t-upload__icon-delete"
              style="color: var(--td-brand-color)"
              @click="handleRemove1(index)"
            />
          </div>
          <div
            v-for="(file, index) in files3"
            :key="file.name"
            class="t-upload__single-display-text t-upload__display-text--margin"
            style="display: flex; align-items: center; gap: 6px"
          >
            <span>{{ file.name }}</span>
            <DownloadIcon
              class="t-upload__icon-delete"
              style="color: var(--td-brand-color)"
              @click="downLoad(file.response?.id, file.name)"
            />
            <DeleteIcon
              v-if="!disabled"
              style="color: var(--td-brand-color)"
              class="t-upload__icon-delete"
              @click="handleRemove(file, index)"
            />
          </div>
        </div>
      </template>
      <template #tips>
        <span>单个文件最大不超过{{ size }}M,最多上传{{ max }}个附件</span>
      </template>
    </t-upload>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin, UploadInstanceFunctions, UploadProps, UploadFile } from 'tdesign-vue-next';
import { TOKEN_NAME, TOKEN_PROFIX } from '@/config/global';
import { useUserStore } from '@/store';
import { computed } from 'vue';
import { removeFile, downLoadFile, listByIds } from '@/api/common';
import proxy from '@/config/proxy';
import { TimeFilledIcon, DownloadIcon, ErrorCircleFilledIcon, DeleteIcon } from 'tdesign-icons-vue-next';

const props = defineProps({
  modelValue: {
    type: String,
  },
  max: {
    type: Number,
    default: 10,
  },
  size: {
    type: Number,
    default: 50,
  },
  accept: {
    type: String,
    default: '*',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['update:modelValue']);

const headersJson = computed(() => {
  let headers = {};
  headers[TOKEN_NAME] = TOKEN_PROFIX + useUserStore().token;
  return headers;
});
const env = import.meta.env.MODE || 'development';
const postUrl = computed(() => {
  const host = env === 'development' ? '' : proxy[env].host;
  return `${host}/center/file/upLoad`;
});

const fileAllList = ref<Array<UploadFile>>([]);
const uploadFileChange = (context: { files: Array<UploadFile>; trigger: 'validate' | 'remove' | 'uploaded' }) => {
  console.log(context);
  fileAllList.value = context.files;
};

// 有文件数量超出时会触发，文件大小超出限制、文件同名时会触发等场景。注意如果设置允许上传同名文件，则此事件不会触发
const onValidate: UploadProps['onValidate'] = (params) => {
  const { files, type } = params;
  console.log('onValidate', type, files);
  const messageMap = {
    FILE_OVER_SIZE_LIMIT: '文件大小超出限制，已自动过滤',
    FILES_OVER_LENGTH_LIMIT: '文件数量超出限制，仅上传未超出数量的文件',
    // if you need same name files, setting allowUploadDuplicateFile={true} please
    FILTER_FILE_SAME_NAME: '不允许上传同名文件',
    BEFORE_ALL_FILES_UPLOAD: 'beforeAllFilesUpload 方法拦截了文件',
    CUSTOM_BEFORE_UPLOAD: 'beforeUpload 方法拦截了文件',
  };
  // you can also set Upload.tips and Upload.status to show warning message.
  messageMap[type] && MessagePlugin.warning(messageMap[type]);
};

const handleChange = (files: CommonArray) => {
  if (files.length > 0) {
    let str = [];
    files.map((row) => {
      str.push(row.response.id);
    });
    emit('update:modelValue', str.join(','));
  } else {
    emit('update:modelValue', '');
  }
};
const uploadRef3 = ref<UploadInstanceFunctions>();
const files3 = ref<UploadProps['value']>([]);
const uploadInOneRequest = ref(false);
const autoUpload = ref(true);
const isBatchUpload = ref(false);
const handleFail: UploadProps['onFail'] = ({ file }) => {
  MessagePlugin.error(`文件 ${file.name} 上传失败`);
};
const handleRemove1 = async (index: number) => {
  const tmpFiles = [...fileAllList.value];
  tmpFiles.splice(index, 1);
  fileAllList.value = tmpFiles;
};
const handleRemove = async (context, index: number) => {
  let res = await context.response;
  await removeFile({
    id: res.id,
  });
  const tmpFiles = [...files3.value];
  tmpFiles.splice(index, 1);
  files3.value = tmpFiles;
};

const downLoad = async (id, name) => {
  let msgLoad = MessagePlugin.loading({
    duration: 0,
    content: '正在下载中...',
  });
  let res = await downLoadFile({
    id: id,
  });
  MessagePlugin.close(msgLoad);
  let blob = new Blob([res]);
  let downloadElement = document.createElement('a');
  let href = window.URL.createObjectURL(blob); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = name; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
  window.URL.revokeObjectURL(href); //释放掉blob对象
};

const formatResponse: UploadProps['formatResponse'] = (result) => {
  console.log(result?.code === 0);
  let error = '上传失败，请重试';
  if (result?.code === 0) {
    return {
      name: result.data.fileName,
      status: 'success',
      size: result.data.fileSize,
      id: result.data.id,
      url: 'javascript:downLoad();',
    };
  }
  return {
    error,
    status: 'fail',
  };
};

const initFileList = (row: CommonArray) => {
  files3.value = [];
  if (row.length > 0) {
    row.map((data) => {
      files3.value.push({
        name: data.fileName,
        status: 'success',
        response: {
          id: data.id,
        },
      });
    });
  }
};

const initFileByIds = async (ids) => {
  files3.value = [];
  if (ids) {
    let res = await listByIds(ids);
    if (res.data && res.data.length > 0) {
      res.data.map((row) => {
        files3.value.push({
          name: row.fileName,
          status: 'success',
          response: {
            id: row.id,
          },
        });
      });
    }
  }
};
defineExpose({
  initFileList,
  initFileByIds,
});
</script>

<style lang="less" scoped>
:deep(.t-upload) {
  margin-left: 0px !important;
}
</style>
