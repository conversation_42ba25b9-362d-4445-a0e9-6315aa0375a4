<template>
  <div>
    <t-dialog
      v-model:visible="visibleModelessDrag"
      :footer="false"
      :top="30"
      :draggable="true"
      :close-on-overlay-click="false"
      :destroy-on-close="true"
      :lazy="true"
      width="80%"
      :on-confirm="() => (visibleModelessDrag = false)"
    >
      <template v-if="!appInfo.loading" #header>
        <div class="flex w-100% justify-center items-center flex-col gap-6px">
          <div class="flex items-center justify-center w-100% gap-4px">
            <ChatAvatar />
            <span>{{ appInfo.appName }}</span>
          </div>
          <div class="flex items-center justify-center w-100% gap-4px">
            <t-tag theme="primary" variant="light">{{ appInfo.modelName }}</t-tag>
          </div>
        </div>
      </template>
      <template #body>
        <div style="background-color: var(--td-bg-color-page)">
          <t-row>
            <t-col :span="3">
              <div class="p-10px">
                <t-loading :loading="loadChat">
                  <t-button block theme="primary" variant="base" @click="addNewChat"
                    >开启新会话
                    <template #icon>
                      <ChatAddIcon />
                    </template>
                  </t-button>
                  <div class="fr-chat-list pt-10px h-568px overflow-x-hidden overflow-y-scroll">
                    <div
                      v-for="(item, index) in chatArray"
                      :key="index"
                      class="item"
                      @click="handlerItem(item)"
                      :style="{ background: appInfo.chatId === item.chatId ? 'var(--td-brand-color-2)' : '#ffffff' }"
                    >
                      <div class="message">
                        <div class="title">
                          {{ item.firstQuestion }}
                        </div>
                        <span class="time">{{ item.startTime }}</span>
                      </div>
                      <div>
                        <t-button
                          theme="default"
                          variant="text"
                          shape="square"
                          size="small"
                          @click.stop="handlerDelChat(item)"
                        >
                          <DeleteIcon />
                        </t-button>
                      </div>
                    </div>
                  </div>
                </t-loading>
              </div>
            </t-col>
            <t-col :span="9">
              <div class="p-10px">
                <t-loading :loading="appInfo.chatLoad">
                  <t-chat
                    layout="both"
                    style="height: 600px; background-color: #ffffff; padding: 10px; border-radius: 15px"
                    :clear-history="false"
                    :reverse="true"
                    @on-action="operation"
                    @clear="clearConfirm"
                  >
                    <template v-for="(item, index) in chatList" :key="index">
                      <t-chat-item
                        :avatar="item.avatar"
                        :name="item.name"
                        :role="item.role"
                        :datetime="item.datetime"
                        :content="item.content"
                        :text-loading="index === 0 && loading"
                        :animation="'gradient'"
                        :variant="item.role === 'user' ? 'base' : 'outline'"
                      >
                        <template #avatar>
                          <ChatAvatar v-if="item.role === 'assistant'" />
                          <t-avatar v-if="item.role === 'user' && !item.avatar">{{ item.name.substr(0, 2) }}</t-avatar>
                          <t-avatar v-if="item.role === 'user' && item.avatar" :image="item.avatar" />
                        </template>
                        <template #content>
                          <t-chat-reasoning v-if="item.reasoning?.length > 0" expand-icon-placement="right">
                            <template #header>
                              <t-chat-loading v-if="item.content.length === 0" text="思考中..." indicator />
                              <div v-else class="flex items-center gap-5px">
                                <CheckCircleIcon style="color: var(--td-success-color-5)" />
                                <span>思考结束</span>
                              </div>
                            </template>
                            <t-chat-content v-if="item.reasoning.length > 0" :content="item.reasoning" />
                          </t-chat-reasoning>
                          <t-chat-content v-if="item.content.length > 0" :content="item.content" />
                        </template>
                        <template v-if="!loading && !isStreamLoad" #actions>
                          <t-chat-action
                            :is-good="true"
                            :is-bad="true"
                            :content="item.content"
                            @operation="(type, { e }) => handleOperation(type, { e, index })"
                          />
                        </template>
                      </t-chat-item>
                    </template>
                    <template #footer>
                      <div class="flex items-center justify-center flex-col">
                        <t-chat-input :stop-disabled="isStreamLoad" @send="inputEnter" @stop="onStop"> </t-chat-input>
                      </div>
                    </template>
                  </t-chat>
                </t-loading>
              </div>
            </t-col>
          </t-row>
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, getCurrentInstance } from 'vue';
import sseUtil from '@/utils/sse';
import { useUserStore } from '@/store';
import { get } from '@/api/aigc/app';
import { MessagePlugin } from 'tdesign-vue-next';
import dayjs from 'dayjs';
import {
  Chat as TChat,
  ChatItem as TChatItem,
  ChatAction as TChatAction,
  ChatInput as TChatInput,
  ChatReasoning as TChatReasoning,
  ChatContent as TChatContent,
  ChatLoading as TChatLoading,
} from '@tdesign-vue-next/chat';
import { CheckCircleIcon, DeleteIcon, ChatAddIcon } from 'tdesign-icons-vue-next';
import ChatAvatar from '@/assets/chat-avatar.svg?component';
import { saveMsg, listChat, del } from '@/api/aigc/app';

const userStore = useUserStore();
const props = defineProps({
  appId: {
    type: String,
  },
});

const appInfo = ref({
  appName: '',
  chatId: '',
  modelName: '',
  loading: false,
  chatLoad: false,
});

const { proxy } = getCurrentInstance();
const sse = ref();
const skLoading = ref(false);
function questionToAi(question) {
  skLoading.value = false;
  sse.value = sseUtil(
    proxy,
    `/aigc/chat/message?appId=${props.appId}&question=${encodeURIComponent(question)}&chatId=${appInfo.value.chatId}`,
  );
  sse.value.onopen = () => {
    loading.value = false;
  };
  sse.value.onmessage = async (event) => {
    let res = JSON.parse(event.data);
    if (res.code === 0) {
      if (!res.data.isEnd) {
        let answer = res.data.message;
        if (answer === '<think>') {
          skLoading.value = true;
          answer = '';
        }
        if (answer === '</think>') {
          skLoading.value = false;
          answer = '';
        }
        if (skLoading.value && answer === '\n\n') {
          answer = '';
        }
        if (!skLoading.value) {
          chatList.value[0].content = chatList.value[0].content + answer;
        } else {
          chatList.value[0].reasoning = chatList.value[0].reasoning + answer;
        }
      } else {
        if (skLoading.value) {
          skLoading.value = false;
          chatList.value[0].content = chatList.value[0].reasoning;
          chatList.value[0].reasoning = '';
        }
        saveMsg({
          chatId: appInfo.value.chatId,
          role: chatList.value[0].role,
          appId: props.appId,
          message: chatList.value[0].content,
          reasoning: chatList.value[0].reasoning,
        });
        sse.value.close();
        isStreamLoad.value = false;
        loading.value = false;
        if (isAddChat.value) {
          await initChatList();
          isAddChat.value = false;
        }
      }
    } else {
      isStreamLoad.value = false;
      loading.value = false;
      MessagePlugin.error(res.msg || '系统未知错误');
      sse.value.close();
    }
  };

  sse.value.onerror = (event) => {
    MessagePlugin.error('系统错误');
    isStreamLoad.value = false;
    loading.value = false;
    sse.value.close();
    if (chatList.value[0].role === 'assistant') {
      saveMsg({
        chatId: appInfo.value.chatId,
        role: chatList.value[0].role,
        appId: props.appId,
        message: chatList.value[0].content,
        reasoning: chatList.value[0].reasoning,
      });
    }
  };
}

const visibleModelessDrag = ref(false);
const loading = ref(false);
const isStreamLoad = ref(false);

watch(
  () => visibleModelessDrag.value,
  async (newVal, oldVal) => {
    if (!newVal) {
      if (sse.value) {
        sse.value.close();
      }
    } else {
      appInfo.value.loading = true;
      await initChatList();
      if (chatArray.value.length > 0) {
        isAddChat.value = false;
        appInfo.value.chatId = chatArray.value[0].chatId;
        await initMsgList();
      } else {
        appInfo.value.chatId = null;
        isAddChat.value = true;
        await initMsgList();
      }
      appInfo.value.loading = false;
    }
  },
);

const chatArray = ref([]);
const loadChat = ref(false);
const initChatList = async () => {
  loadChat.value = true;
  let res = await listChat(props.appId);
  chatArray.value = res.data;
  loadChat.value = false;
};
const handlerItem = (item) => {
  isAddChat.value = false;
  appInfo.value.chatId = item.chatId;
  initMsgList();
};

const isAddChat = ref(false);
const addNewChat = () => {
  if (isAddChat.value) {
    return;
  }
  isAddChat.value = true;
  appInfo.value.chatId = null;
  initMsgList();
};
const handlerDelChat = async (item) => {
  let msg = MessagePlugin.loading('删除中...');
  await del(props.appId, {
    chatId: item.chatId,
  });
  MessagePlugin.close(msg);
  if (item.chatId === appInfo.value.chatId) {
    await initChatList();
    if (chatArray.value.length > 0) {
      isAddChat.value = false;
      appInfo.value.chatId = chatArray.value[0].chatId;
      await initMsgList();
    } else {
      appInfo.value.chatId = '';
      isAddChat.value = true;
      await initMsgList();
    }
  } else {
    await initChatList();
  }
};

const initMsgList = async () => {
  chatList.value = [];
  appInfo.value.chatLoad = true;
  try {
    let res = await get(props.appId, {
      chatId: appInfo.value.chatId || null,
    });
    appInfo.value.appName = res.data.appInfo.nickName;
    appInfo.value.modelName = res.data.appInfo.model.title;
    appInfo.value.chatId = res.data.chatId;
    res.data.message.map((row) => {
      chatList.value.push({
        avatar: row.role === 'user' ? userStore.curUser.avatar : '',
        name: row.role === 'user' ? userStore.curUser.nickName : appInfo.value.appName,
        datetime: row.createTime,
        content: row.message,
        role: row.role,
        reasoning: row.role === 'user' ? '' : row.reasoning,
      });
    });
    appInfo.value.chatLoad = false;
  } catch (e) {
    appInfo.value.chatLoad = false;
  }
};
// 倒序渲染
const chatList = ref([]);

const operation = function (type, options) {
  console.log(type, options);
};
const clearConfirm = function () {
  chatList.value = [];
};
const onStop = function () {
  if (sse.value) {
    sse.value.close();
    loading.value = false;
    isStreamLoad.value = false;
    saveMsg({
      chatId: appInfo.value.chatId,
      role: chatList.value[0].role,
      appId: props.appId,
      message: chatList.value[0].content,
      reasoning: chatList.value[0].reasoning,
    });
  }
};

const inputEnter = function (inputValue) {
  if (isStreamLoad.value) {
    return;
  }
  if (!inputValue) return;
  const params = {
    avatar: userStore.curUser.avatar,
    name: userStore.curUser.nickName,
    datetime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    content: inputValue,
    role: 'user',
  };
  chatList.value.unshift(params);
  // 空消息占位
  const params2 = {
    avatar: '',
    name: appInfo.value.appName,
    datetime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    content: '',
    role: 'assistant',
    reasoning: '',
  };
  chatList.value.unshift(params2);
  handleData(inputValue);
};

const handleData = async (inputValue) => {
  loading.value = true;
  isStreamLoad.value = true;
  questionToAi(inputValue);
};

const handleOperation = function (type, options) {
  console.log('handleOperation', type, options);
  const { index } = options;
  if (type === 'good') {
  } else if (type === 'bad') {
  } else if (type === 'replay') {
  }
};

const open = () => {
  visibleModelessDrag.value = true;
};

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.fr-chat-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  .item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border-radius: 10px;
    cursor: pointer;
    &:hover {
      background-color: var(--td-brand-color-1) !important;
    }
    .message {
      display: flex;
      flex-direction: column;
      align-items: baseline;
      justify-items: baseline;
      .title {
        font-size: 16px;
        overflow: hidden;
        text-overflow: ellipsis !important;
        white-space: nowrap;
        width: 220px;
      }
      .time {
        font-size: 14px;
        color: var(--tdvns-gray-color-6);
      }
    }
  }
}
</style>
