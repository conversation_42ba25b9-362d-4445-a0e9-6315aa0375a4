<template>
  <div>
    <t-dialog
      v-model:visible="visibleModal"
      width="400"
      :closeOnOverlayClick="false"
      :header="'上传授权文件'"
      destroyOnClose
      mode="modal"
      draggable
      :confirm-btn="null"
      :cancelBtn="'关闭'"
      :close-btn="null"
    >
      <t-upload
        ref="uploadRef3"
        v-model="files3"
        :abridgeName="[10, 7]"
        :multiple="true"
        :max="max"
        :accept="accept"
        :disabled="disabled"
        :sizeLimit="1024 * 50"
        :auto-upload="autoUpload"
        :upload-all-files-in-one-request="uploadInOneRequest"
        :allow-upload-duplicate-file="true"
        :is-batch-upload="isBatchUpload"
        :format-response="formatResponse"
        :action="postUrl"
        :style="{ marginLeft: '60px' }"
        :headers="headersJson"
        @fail="handleFail"
        @change="handelSuccess"
        @waiting-upload-files-change="uploadFileChange"
      >
        <template #fileListDisplay>
          <div>
            <div
              v-for="(file, index) in fileAllList"
              :key="file.raw.name"
              class="t-upload__single-display-text t-upload__display-text--margin"
              style="display: flex; align-items: center; gap: 5px"
            >
              <span>{{ file.raw.name }}</span>
              <TimeFilledIcon v-if="file.status === 'waiting'" />
              <ErrorCircleFilledIcon v-if="file.status === 'fail'" />
              <t-loading v-if="file.status === 'progress'" :text="file.percent + '%'" size="small"></t-loading>
              <DeleteIcon
                v-if="file.status && (file.status === 'success' || file.status === 'fail')"
                class="t-upload__icon-delete"
                style="color: var(--td-brand-color)"
                @click="handleRemove1(index)"
              />
            </div>
            <div
              v-for="(file, index) in files3"
              :key="file.name"
              class="t-upload__single-display-text t-upload__display-text--margin"
              style="display: flex; align-items: center; gap: 6px"
            >
              <span>{{ file.name }}</span>
              <DeleteIcon
                style="color: var(--td-brand-color)"
                class="t-upload__icon-delete"
                @click="handleRemove(file, index)"
              />
            </div>
          </div>
        </template>
        <template #tips>
          <span>单个文件最大不超过{{ sizeLimit / 1024 }}M,最多上传{{ max }}个附件</span>
        </template>
      </t-upload>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { MessagePlugin, UploadInstanceFunctions, UploadProps, UploadFile, SuccessContext } from 'tdesign-vue-next';
import { TOKEN_NAME, TOKEN_PROFIX } from '@/config/global';
import { useUserStore } from '@/store';
import { computed } from 'vue';
import proxy from '@/config/proxy';
import { TimeFilledIcon, ErrorCircleFilledIcon, DeleteIcon } from 'tdesign-icons-vue-next';

const props = defineProps({
  modelValue: {
    type: String,
  },
  max: {
    type: Number,
    default: 1,
  },
  sizeLimit: {
    type: Number,
    default: 1024 * 5,
  },
  accept: {
    type: String,
    default: '.lic',
  },
});
const emit = defineEmits(['update']);

const headersJson = computed(() => {
  let headers = {};
  headers[TOKEN_NAME] = TOKEN_PROFIX + useUserStore().token;
  return headers;
});
const env = import.meta.env.MODE || 'development';
const postUrl = computed(() => {
  const host = env === 'development' ? '' : proxy[env].host;
  return `${host}/center/license/updateLicense`;
});

const fileAllList = ref<Array<UploadFile>>([]);
const uploadFileChange = (context: { files: Array<UploadFile>; trigger: 'validate' | 'remove' | 'uploaded' }) => {
  fileAllList.value = context.files;
};

const handelSuccess = (files: CommonArray) => {
  if (files.length > 0) {
    console.log(files[0].response);
    emit('update', files[0].response.licenseData);
    visibleModal.value = false;
  }
};
const uploadRef3 = ref<UploadInstanceFunctions>();
const files3 = ref<UploadProps['value']>([]);
const uploadInOneRequest = ref(false);
const autoUpload = ref(true);
const isBatchUpload = ref(false);
const disabled = ref(false);
const handleFail: UploadProps['onFail'] = ({ file }) => {
  MessagePlugin.error(` ${file.response.error} `);
};
const handleRemove1 = async (index: number) => {
  const tmpFiles = [...fileAllList.value];
  tmpFiles.splice(index, 1);
  fileAllList.value = tmpFiles;
};
const handleRemove = async (context, index: number) => {
  const tmpFiles = [...files3.value];
  tmpFiles.splice(index, 1);
  files3.value = tmpFiles;
};

const formatResponse: UploadProps['formatResponse'] = (result) => {
  console.log(result?.code === 0);
  let error = '上传失败:' + result.msg;
  if (result?.code === 0) {
    return {
      name: result.data.fileName,
      status: 'success',
      size: result.data.fileSize,
      id: result.data.id,
      licenseData: result.data,
      url: 'javascript:downLoad();',
    };
  }
  return {
    error,
    status: 'fail',
  };
};
const visibleModal = ref(false);
const open = () => {
  fileAllList.value = [];
  files3.value = [];
  visibleModal.value = true;
};
defineExpose({
  open,
});
</script>

<style lang="less" scoped>
:deep(.t-upload) {
  margin-left: 0px !important;
}
</style>
