<template>
  <div>
    <t-base-table
      bordered
      row-key="nodeName"
      :data="data"
      :loading="loading"
      :columns="[
        {
          width: 170,
          colKey: 'nodeName',
          title: '处理节点',
          align: 'center',
          ellipsis: true,
        },
        {
          width: 130,
          col<PERSON>ey: 'userId',
          title: '处理人',
          align: 'center',
          ellipsis: true,
        },
        {
          width: 120,
          col<PERSON>ey: 'type',
          title: '处理动作',
          align: 'center',
          ellipsis: true,
        },
        {
          width: 200,
          col<PERSON>ey: 'remark',
          title: '处理意见',
          align: 'left',
          ellipsis: true,
        },
        {
          width: 180,
          col<PERSON>ey: 'duration',
          title: '耗时',
          align: 'center',
          ellipsis: true,
        },
        {
          width: 180,
          colKey: 'startTime',
          title: '接收时间',
          align: 'center',
          ellipsis: true,
        },
        {
          width: 180,
          colKey: 'endTime',
          title: '处理时间',
          align: 'center',
          ellipsis: true,
        },
      ]"
    >
      <template #userId="{ row }">
        {{ userMap[row.userId] }}
      </template>
      <template #duration="{ row }">
        {{ !isNull(row.duration) ? dayjs.duration(row.duration).asSeconds() + '秒' : '' }}
      </template>
    </t-base-table>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { isNull } from 'lodash';
import { ref, watch } from 'vue';
import { userNameByIds } from '@/api/common';
import duration from 'dayjs/plugin/duration';

dayjs.extend(duration);

const props = defineProps({
  data: {
    type: Array<Record<string, any>>,
  },
});

const userMap = ref<CommonObject>({});
watch(
  () => props.data,
  () => {
    initUserName();
  },
);
const loading = ref(true);
const initUserName = async () => {
  let ids = [];
  loading.value = false;
  props.data.map((obj) => {
    if (obj.userId) {
      ids.push(obj.userId);
    }
  });
  if (ids.length > 0) {
    let res2 = await userNameByIds({
      ids: ids.join(','),
    });
    userMap.value = res2.data;
  }
};
</script>

<style lang="less"></style>
