<template>
  <div>
    <t-tabs :default-value="1">
      <t-tab-panel :value="1" label="模型管理">
        <p><One></One></p>
      </t-tab-panel>
      <t-tab-panel :value="2" label="表达式配置">
        <p><Two></Two></p>
      </t-tab-panel>
      <t-tab-panel :value="3" label="监听器配置">
        <p><Three></Three></p>
      </t-tab-panel>
    </t-tabs>
  </div>
</template>

<script lang="ts">
export default {
  name: 'managerFlow',
};
</script>

<script setup lang="ts">
import One from './one.vue';
import Two from './two.vue';
import Three from './three.vue';
</script>

<style lang="less" scoped></style>
