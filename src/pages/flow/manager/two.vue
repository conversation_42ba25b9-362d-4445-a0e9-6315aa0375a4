<template>
  <div>
    <div class="sp-role-left">
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
            url: '/flow/extension/list',
            method: 'get',
          }"
          :max-height="tableHeight"
        >
          <template #frSimpleBtn>
            <t-button v-if="authAdd" @click="addRow">新增</t-button>
          </template>
          <template #frSimpleQuery>
            <t-input
              placeholder="请输入名称"
              type="search"
              clearable
              v-model="params.name"
              @enter="firstFetch"
              :style="{ width: '220px' }"
            ></t-input>
          </template>
          <template #context="{ row }">
            <t-tag theme="primary" variant="light">{{ row.context }}</t-tag>
          </template>
          <template #operation="{ row }">
            <t-button v-if="authEdit" size="small" variant="text" theme="primary" @click="editRow(row)">修改</t-button>
            <t-button v-if="authDel" size="small" variant="text" theme="danger" @click="delRow(row)">删除</t-button>
          </template>
        </FrQuery>
      </t-card>
    </div>
    <t-dialog
      v-model:visible="visibleModal"
      width="600"
      :closeOnOverlayClick="false"
      :header="opt === 'add' ? '新增' : '修改'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form ref="form" :label-align="'right'" :data="extForm" :layout="'inline'" :rules="rules">
          <t-form-item label="名称" name="name">
            <t-input v-model="extForm.name" :style="{ width: '400px' }" placeholder="请输入表达式名称"></t-input>
          </t-form-item>
          <t-form-item label="表达式" name="context">
            <t-input v-model="extForm.context" :style="{ width: '400px' }" placeholder="请输入表达式"></t-input>
          </t-form-item>
          <t-form-item label="说明" name="remark">
            <t-input v-model="extForm.remark" :style="{ width: '400px' }" placeholder="请输入备注说明"></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListTenant',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, getCurrentInstance } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { add, edit, del } from '@/api/flow/extension';
import FrQuery from '@/components/fr-query/index.vue';
import { useUserStore } from '@/store';

//获取table全局高度
const tableHeight = getCurrentInstance().appContext.config.globalProperties.$frGlobal.tabHeight;
const queryRef = ref(null);
//权限控制
const userStore = useUserStore();
const authAdd = computed(() => userStore.roles.includes('wf:ext:add'));
const authEdit = computed(() => userStore.roles.includes('wf:ext:edit'));
const authDel = computed(() => userStore.roles.includes('wf:ext:del'));

const firstFetch = async () => {
  queryRef.value.loadData(true);
};

//新增/修改弹窗start
const visibleModal = ref(false);
const extForm = reactive({
  id: '',
  name: '',
  context: '',
  remark: '',
  type: '',
});
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});
const rules = {
  name: [{ required: true, message: '请输入表达式名称', type: 'error' }],
  context: [{ required: true, message: '请输入表达式', type: 'error' }],
} as Rules;
const onSubmit = async () => {
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    saveBtn.content = '保存中...';
    saveBtn.loading = true;
    let submitForm = {
      name: extForm.name,
      context: extForm.context,
      remark: extForm.remark,
      id: null,
    };
    if (opt.value === 'add') {
      try {
        let result1 = await add(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          await fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      submitForm.id = extForm.id;
      try {
        let result1 = await edit(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  }
};
//新增/修改弹窗end

//左侧角色菜单列表数据start
const columns = [
  //   {
  //     width: 140,
  //     colKey: 'id',
  //     title: '租户ID',
  //   },
  {
    width: 140,
    colKey: 'name',
    title: '名称',
    ellipsis: true,
  },
  {
    width: 160,
    colKey: 'context',
    title: '表达式',
  },
  {
    width: 80,
    colKey: 'remark',
    title: '说明',
  },
  {
    width: 160,
    colKey: 'createTime',
    title: '创建时间',
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 100,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;

const params = ref({
  name: '',
});

const opt = ref('add');
const addRow = async () => {
  opt.value = 'add';
  form.value.reset();
  extForm.id = '';
  visibleModal.value = true;
};
const editRow = async (row) => {
  opt.value = 'edit';
  form.value.reset();
  extForm.id = row.id;
  extForm.name = row.name;
  extForm.remark = row.remark;
  extForm.context = row.context;
  visibleModal.value = true;
};
const delRow = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认删除(' + row.name + ')表达式？',
    confirmBtn: '继续删除',
    //cancelBtn: '在考虑下',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      del(row.id)
        .then((res) => {
          if (res.code === 0) {
            fetchData();
            MessagePlugin.success('删除成功');
          } else {
            MessagePlugin.error('删除失败：' + res.msg);
          }
        })
        .catch((error) => {
          MessagePlugin.error('删除失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

const fetchData = async () => {
  queryRef.value.loadData();
};
//左侧角色菜单列表数据end

//vue的api
onMounted(() => {});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
