<template>
  <div>
    <div class="leftMenu" v-if="nodeList && nodeList.length > 0">
      <div v-for="(item, index) in nodeList" :key="index" class="item" @mousedown="dragNode(item)">
        <span class="iconfont" :class="[item.icon]" :style="{ fontSize: item.size, color: '#0052d9' }"></span>
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const props = defineProps({
  lf: {
    type: Object,
  },
});

const dragNode = (item: Record<string, string>) => {
  //生成节点ID满足flowable的命名规则 NCName
  props.lf.dnd.startDrag({
    id: nameRule[item.type] + '' + new Date().getTime(),
    type: item.type,
  });
};

const nameRule = {
  'bpmn:startEvent': 'StartEvent_',
  'bpmn:userTask': 'UserTask_',
  'bpmn:serviceTask': 'ServiceTask_',
  'bpmn:parallelGateway': 'ParallelGateway_',
  'bpmn:exclusiveGateway': 'ExclusiveGateway_',
  'bpmn:inclusiveGateway': 'InclusiveGateway_',
  'bpmn:endEvent': 'EndEvent_',
};
const nodeList = ref<Array<Record<string, string>>>([
  {
    name: '开始',
    icon: 'icon-shipin',
    type: 'bpmn:startEvent',
    size: '32px',
  },
  {
    name: '人工节点',
    icon: 'icon-me_zhanghaoguanli',
    type: 'bpmn:userTask',
    size: '32px',
  },
  // {
  //   name: '系统节点',
  //   icon: 'icon-shezhi',
  //   type: 'bpmn:serviceTask',
  //   size: '32px',
  // },
  {
    name: '并行',
    icon: 'icon-zengjia',
    type: 'bpmn:parallelGateway',
    size: '32px',
  },
  {
    name: '条件判断',
    icon: 'icon-guanbi',
    type: 'bpmn:exclusiveGateway',
    size: '32px',
  },
  {
    name: '合并',
    icon: 'icon-licai',
    type: 'bpmn:inclusiveGateway',
    size: '32px',
  },
  {
    name: '结束',
    icon: 'icon-guanbi1',
    type: 'bpmn:endEvent',
    size: '32px',
  },
]);
</script>
<style lang="less">
.leftMenu {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
  background: #ffffff;
  border-radius: 10px;
  overflow: hidden;
  max-height: 500px;
  width: 80px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 10px;
  gap: 15px;
  -webkit-user-select: none; /*谷歌 /Chrome*/
  -moz-user-select: none; /*火狐/Firefox*/
  -ms-user-select: none; /*IE 10+*/
  user-select: none;

  .item {
    cursor: pointer;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    span {
      font-size: 12px;
      color: black;
    }
  }
}
</style>
