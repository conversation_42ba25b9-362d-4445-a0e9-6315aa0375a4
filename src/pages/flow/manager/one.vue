<template>
  <div>
    <div class="sp-role-left">
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
            url: '/flow/design/list',
            method: 'get',
          }"
          :max-height="tableHeight"
          :show-check="true"
          select-type="single"
          row-key="id"
        >
          <template #frSimpleBtn>
            <t-space :size="8">
              <t-button v-if="authAdd" @click="addRow">新增流程</t-button>
              <t-button variant="outline" theme="primary" @click="exportSql">
                <template #icon> <FrIcon name="file-download" /></template> 导出工作流
              </t-button>
              <t-button variant="outline" theme="primary" @click="importSql">
                <template #icon> <FrIcon name="file-import" /></template> 导入工作流</t-button
              >
            </t-space>
          </template>
          <template #frSimpleQuery>
            <t-space size="6px">
              <t-input
                placeholder="请输入流程名称"
                type="search"
                clearable
                v-model="params.name"
                @enter="firstFetch"
              ></t-input>
              <t-input
                placeholder="请输入流程编号"
                type="search"
                clearable
                v-model="params.key"
                @enter="firstFetch"
              ></t-input>
            </t-space>
          </template>
          <template #category="{ row }">
            {{ categoryMap[row.category] }}
          </template>
          <template #status="{ row }">
            <t-tag v-if="row.version === '0'">未发布</t-tag>
            <t-tag v-if="row.version !== '0' && row.status" theme="primary" variant="light">已发布</t-tag>
            <t-tag v-if="row.version !== '0' && !row.status" theme="warning" variant="light">已挂起</t-tag>
          </template>
          <template #operation="{ row }">
            <t-space size="4px">
              <t-button v-if="authEdit" size="small" variant="text" theme="primary" @click="logicRender(row)"
                >设计</t-button
              >
              <t-popconfirm v-if="authPub" content="是否确认发布该流程?" @confirm="deployFlow(row.id)">
                <t-button size="small" variant="text" theme="primary">发布</t-button>
              </t-popconfirm>
              <t-dropdown
                :options="[
                  {
                    content: '修改',
                    value: 2,
                    onClick: () => {
                      btnVal = '2';
                    },
                  },
                  {
                    content: '激活',
                    value: 3,
                    onClick: () => {
                      btnVal = '3';
                    },
                  },
                  {
                    content: '挂起',
                    value: 4,
                    onClick: () => {
                      btnVal = '4';
                    },
                  },
                  {
                    content: '删除',
                    value: 1,
                    onClick: () => {
                      btnVal = '1';
                    },
                  },
                ]"
                @click="clickHandler(row)"
              >
                <t-button theme="primary" variant="text" size="small"> 更多</t-button>
              </t-dropdown>
            </t-space>
          </template>
        </FrQuery>
      </t-card>
    </div>
    <t-dialog
      v-model:visible="visibleModal"
      width="500"
      :closeOnOverlayClick="false"
      :header="opt === 'add' ? '新增流程' : '修改流程'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form ref="form" :label-align="'right'" :data="wfForm" :layout="'inline'" :rules="rules">
          <t-form-item label="编号" name="key">
            <t-input v-model="wfForm.key" :style="{ width: '400px' }" :disabled="true"></t-input>
          </t-form-item>
          <t-form-item label="名称" name="name">
            <t-input v-model="wfForm.name" :style="{ width: '400px' }" placeholder="请输入流程名称"></t-input>
          </t-form-item>
          <t-form-item label="所属分类" name="category">
            <t-select v-model="wfForm.category" :style="{ width: '400px' }" placeholder="请选择流程分类">
              <t-option v-for="(item, index) in categoryList" :key="index" :label="item.label" :value="item.value" />
            </t-select>
          </t-form-item>
          <t-form-item label="表单类型" name="formType">
            <t-select
              v-model="wfForm.formType"
              placeholder="请选择表单类型"
              :options="[
                { value: '01', label: '表单模板' },
                { value: '02', label: '自定义表单' },
              ]"
              style="width: 400px"
              @change="changeFormType"
            >
            </t-select>
          </t-form-item>
          <t-form-item v-if="wfForm.formType === '01'" label="表单模板" name="form">
            <FrData
              ref="frDataRef"
              v-model:model-value="wfForm.form"
              url="/lowcode/formTemplate/list"
              method="post"
              :params="{ genre: '01' }"
              query-key="name"
              value-key="id"
              label-key="name"
              description-key="parentName"
              searchPlaceholder="请输入表单名称模糊搜索"
              placeholder="请选择关联表单"
              :width="'400px'"
            />
          </t-form-item>
          <t-form-item v-if="wfForm.formType === '02'" label="表单路由" name="form" required>
            <t-input v-model="wfForm.form" style="width: 400px" placeholder="请输入关联表单路由地址" />
          </t-form-item>
          <t-form-item label="详情页面类型" name="viewType">
            <t-select
              v-model="wfForm.viewType"
              placeholder="请选择详情页面类型"
              :options="[
                { value: '01', label: '页面模板' },
                { value: '02', label: '自定义页面' },
              ]"
              style="width: 400px"
              @change="changeFormType1"
            >
            </t-select>
          </t-form-item>
          <t-form-item v-if="wfForm.viewType === '01'" label="详情页面模板" name="viewPage">
            <FrData
              ref="frDataRef1"
              v-model:model-value="wfForm.viewPage"
              url="/lowcode/formTemplate/list"
              method="post"
              :params="{ genre: '01' }"
              query-key="name"
              value-key="id"
              label-key="name"
              description-key="parentName"
              searchPlaceholder="请输入模板名称模糊搜索"
              placeholder="请选择关联详情页面"
              :width="'400px'"
            />
          </t-form-item>
          <t-form-item v-if="wfForm.viewType === '02'" label="详情页面路由" name="viewPage" required>
            <t-input v-model="wfForm.viewPage" style="width: 400px" placeholder="请输入关联详情页面路由地址" />
          </t-form-item>
          <t-form-item label="描述" name="remark">
            <div :style="{ width: '400px' }">
              <t-textarea v-model="wfForm.remark" :maxlength="50" placeholder="请输入流程描述" />
            </div>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
    <t-drawer
      v-model:visible="visibleModal1"
      :destroyOnClose="true"
      :size-draggable="true"
      :closeOnOverlayClick="false"
      :footer="false"
      size="88%"
      class="designContent"
    >
      <template #header>
        <div style="display: flex; justify-content: flex-end; width: 100%">
          <ToolBar
            :lf="logicFlowContent"
            :is-back="undoDisable"
            :is-next="redoDisable"
            :modelData="modelData"
            @saveData="saveXml"
            @saveAndDep="saveAndPubXml"
            @close="closeDrawer"
          ></ToolBar>
        </div>
      </template>
      <template #body>
        <div class="container" id="design">
          <t-loading attach="#design" size="26px" :loading="loadFlow.loading" :text="loadFlow.text"></t-loading>
          <div class="container" style="position: relative">
            <LeftMenu :lf="logicFlowContent"></LeftMenu>
            <div class="container" ref="container"></div>
            <RightContent ref="rightContent" :lf="logicFlowContent"></RightContent>
          </div>
        </div>
      </template>
    </t-drawer>

    <!-- 显示导出菜单 -->
    <t-dialog
      v-model:visible="visibleModal5"
      width="400"
      :closeOnOverlayClick="false"
      :header="'导入工作流'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn5"
      :on-confirm="onSubmit5"
    >
      <t-upload
        v-model="fileList"
        :auto-upload="false"
        accept=".json"
        :theme="'file'"
        :draggable="false"
        :upload-button="null"
        :cancel-upload-button="null"
      >
      </t-upload>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListTenant',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, getCurrentInstance, nextTick } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { get, add, edit, del, save, deploy, saveAndDeploy, active, suspend } from '@/api/flow/design';
import { dicVals } from '@/api/common';
import LogicFlow from '@logicflow/core';
import { DndPanel, SelectionSelect, Menu } from '@logicflow/extension';
import '@logicflow/core/dist/style/index.css';
import '@logicflow/extension/lib/style/index.css';
import ToolBar from './components/ToolBar.vue';
import LeftMenu from './components/LeftMenu.vue';
import RightContent from './components/RightContent.vue';
import FrQuery from '@/components/fr-query/index.vue';
import FrData from '@/components/fr-data/index.vue';
import FrIcon from '@/components/fr-icon';

import UserTask from './node/UserTask';
import ExclusiveGateway from './node/exclusiveGateway';
import StartTask from './node/StartTask';
import EndTask from './node/EndTask';
import InclusiveGateway from './node/inclusiveGateway';
import ParallelGateway from './node/parallelGateway';
import ServiceTask from './node/ServiceTask';
import BaseEdge from './node/Edge';
import { useUserStore } from '@/store';
import { publicInterface } from '@/api/common';

//导入工作流
const fileList = ref([]);
const onSubmit5 = async () => {
  if (!fileList.value || fileList.value.length === 0) {
    MessagePlugin.warning('请选择工作流文件');
    return;
  }
  saveBtn5.loading = true;
  saveBtn5.content = '导入中...';
  let load = MessagePlugin.loading({
    duration: 0,
    content: '正在导入中...',
  });
  try {
    let form = new FormData();
    form.append('file', fileList.value[0].raw);
    let res = await publicInterface({
      url: '/flow/design/import',
      method: 'post',
      data: form,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    if (res.code === 0) {
      visibleModal5.value = false;
      queryRef.value.loadData();
      MessagePlugin.success('导入成功!');
    } else {
      MessagePlugin.error('导入失败：' + res.msg);
    }
  } catch (e) {
    MessagePlugin.error(e.message);
  } finally {
    MessagePlugin.close(load);
    saveBtn5.loading = false;
    saveBtn5.content = '开始导入';
  }
};
const visibleModal5 = ref(false);
const saveBtn5 = reactive({
  content: '开始导入',
  loading: false,
});
const importSql = () => {
  fileList.value = [];
  visibleModal5.value = true;
};

const exportSql = async () => {
  if (!queryRef.value.getSelectData() || queryRef.value.getSelectData().length === 0) {
    MessagePlugin.warning('请选择要导出的工作流');
    return;
  }
  let selectRow = queryRef.value.getSelectData();
  console.log(selectRow);
  let load = MessagePlugin.loading({
    content: '生成下载中...',
    duration: 0,
  });
  let res = await publicInterface({
    url: '/flow/design/export',
    method: 'post',
    headers: {
      responseType: 'blob',
    },
    data: {
      id: selectRow[0],
    },
  });
  let blob = new Blob([JSON.stringify(res.data)], { type: 'text/plain;charset=utf-8' });
  let filename = res.headers['content-disposition'];
  let link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  MessagePlugin.close(load);
};

const tableHeight = getCurrentInstance().appContext.config.globalProperties.$frGlobal.tabHeight;
const queryRef = ref(null);
const closeDrawer = () => {
  visibleModal1.value = false;
};
const categoryList = ref([]);
const categoryMap = ref({});
const initData = async () => {
  let res = await dicVals('SP_FLOW_CATEGORY');
  categoryList.value = res.data;
  res.data.forEach((r) => {
    categoryMap.value[r.value] = r.label;
  });
};
const changeFormType = () => {
  wfForm.form = '';
};
const changeFormType1 = () => {
  wfForm.viewPage = '';
};

//权限控制
const userStore = useUserStore();
const authAdd = computed(() => userStore.roles.includes('wf:design:add'));
const authEdit = computed(() => userStore.roles.includes('wf:design:edit'));
const authDel = computed(() => userStore.roles.includes('wf:design:del'));
const authPub = computed(() => userStore.roles.includes('wf:design:pub'));

//更多操作
const btnVal = ref('');
const clickHandler = (data: Record<string, any>) => {
  if (btnVal.value === '1') {
    delRow(data);
  } else if (btnVal.value === '2') {
    editRow(data);
  } else if (btnVal.value === '3') {
    if (data.version === '0') {
      MessagePlugin.warning('请先设计流程并发布流程');
      return;
    }
    if (data.status) {
      MessagePlugin.warning('当前流程已经为<激活>状态');
      return;
    }
    const confirmDia = DialogPlugin({
      header: '提醒',
      body: '是否激活(' + data.name + ')流程？',
      confirmBtn: '继续激活',
      onConfirm: ({ e }) => {
        confirmDia.hide();
        active(data.processId)
          .then((res) => {
            if (res.code === 0) {
              fetchData();
              MessagePlugin.success('激活成功');
            } else {
              MessagePlugin.error('激活失败：' + res.msg);
            }
          })
          .catch((error) => {
            MessagePlugin.error('激活失败:' + error);
          });
      },
      onClose: ({ e, trigger }) => {
        confirmDia.hide();
      },
    });
  } else if (btnVal.value === '4') {
    if (data.version === '0') {
      MessagePlugin.warning('请先设计流程并发布流程');
      return;
    }
    if (!data.status) {
      MessagePlugin.warning('当前流程已经为<已挂起>状态');
      return;
    }
    const confirmDia = DialogPlugin({
      header: '提醒',
      body: '是否挂起(' + data.name + ')流程？',
      confirmBtn: '继续挂起',
      onConfirm: ({ e }) => {
        confirmDia.hide();
        suspend(data.processId)
          .then((res) => {
            if (res.code === 0) {
              fetchData();
              MessagePlugin.success('挂起成功');
            } else {
              MessagePlugin.error('挂起失败：' + res.msg);
            }
          })
          .catch((error) => {
            MessagePlugin.error('挂起失败:' + error);
          });
      },
      onClose: ({ e, trigger }) => {
        confirmDia.hide();
      },
    });
  }
};

//部署发布流程
const deployFlow = async (id: string) => {
  let loadMsg = MessagePlugin.loading('正在部署发布中...');
  try {
    let res = await deploy(id);
    MessagePlugin.close(loadMsg);
    if (res.code === 0) {
      MessagePlugin.success('发布成功');
      await fetchData();
    } else {
      console.log('错误了');
      MessagePlugin.error('发布失败:' + res.msg);
    }
  } catch (e) {
    MessagePlugin.close(loadMsg);
    MessagePlugin.error('发布失败！原因：' + e);
  }
};

//保存流程设计图和流程信息数据
const saveXml = async () => {
  let saveForm = await rightContent.value.getflowData();
  if (!saveForm.category) {
    MessagePlugin.error('请选择<流程信息>中的”分类“');
    return;
  }
  if (!saveForm.name) {
    MessagePlugin.error('请选择<流程信息>中的”名称“');
    return;
  }
  if (!saveForm.formType) {
    MessagePlugin.error('请选择<流程信息>中的”表单类型“');
    return;
  }
  if (!saveForm.form) {
    MessagePlugin.error('请选择<流程信息>中的”关联表单“');
    return;
  }
  if (!saveForm.viewType) {
    MessagePlugin.error('请选择<流程信息>中的”详情页面类型“');
    return;
  }
  if (!saveForm.viewPage) {
    MessagePlugin.error('请选择<流程信息>中的”详情页面模板/路由“');
    return;
  }
  if (!saveForm.remark) {
    MessagePlugin.error('请选择<流程信息>中的”描述“');
    return;
  }
  loadFlow.value.loading = true;
  loadFlow.value.text = '保存中...';
  let xmlStr = JSON.stringify(logicFlowContent.value.getGraphData());
  saveForm.bpmnXml = xmlStr;
  try {
    let res = await save({
      ...saveForm,
    });
    if (res.code === 0) {
      //visibleModal1.value = false;
      MessagePlugin.success('保存成功');
      //await fetchData();
    } else {
      MessagePlugin.error('保存失败!原因：' + res.msg);
    }
  } catch (e) {
  } finally {
    loadFlow.value.loading = false;
  }
};

//保存并且发布流程设计图和流程信息数据
const saveAndPubXml = async () => {
  loadFlow.value.loading = true;
  loadFlow.value.text = '保存发布中...';
  let saveForm = await rightContent.value.getflowData();
  let xmlStr = JSON.stringify(logicFlowContent.value.getGraphData());
  saveForm.bpmnXml = xmlStr;
  try {
    let res = await saveAndDeploy({
      ...saveForm,
    });
    if (res.code === 0) {
      visibleModal1.value = false;
      MessagePlugin.success('保存并发布成功');
      await fetchData();
    } else {
      MessagePlugin.error('保存并发布成功失败!原因：' + res.msg);
    }
  } catch (e) {
  } finally {
    loadFlow.value.loading = false;
  }
};

//logicflow设计图配置渲染
const visibleModal1 = ref(false);
const container = ref(null);
const logicFlowContent = ref();
const undoDisable = ref(true);
const redoDisable = ref(true);
const modelData = ref<Record<string, any>>({});
const rightContent = ref(null);
const loadFlow = ref({
  loading: false,
  text: '初始化...',
});
const logicRender = async (row: Record<string, any>) => {
  visibleModal1.value = true;
  undoDisable.value = true;
  redoDisable.value = true;
  loadFlow.value.loading = true;
  loadFlow.value.text = '初始化...';
  try {
    let res = await get(row.id);
    modelData.value = res.data;
  } catch (e) {
    MessagePlugin.error('初始化失败:' + e);
  }

  //刷新右侧流程信息
  rightContent.value.initNode('all', modelData.value, {});
  logicFlowContent.value = new LogicFlow({
    container: container.value,
    grid: false,
    plugins: [DndPanel, SelectionSelect, Menu],
    background: {
      background: '#f2f3ff',
    },
    keyboard: {
      enabled: true,
    },
  });
  logicFlowContent.value.extension.menu.setMenuConfig({
    nodeMenu: [
      {
        text: '删除',
        callback(node) {
          logicFlowContent.value.deleteNode(node.id);
        },
      },
    ], // 覆盖默认的节点右键菜单
    edgeMenu: [
      {
        text: '删除',
        callback(node) {
          logicFlowContent.value.deleteEdge(node.id);
        },
      },
    ], // 删除默认的边右键菜单
  });

  logicFlowContent.value.setTheme({
    baseNode: {
      fill: '#ffffff',
      stroke: '#0052d9',
      strokeWidth: 2,
    },
    baseEdge: {
      stroke: '#0052d9',
      strokeWidth: 2,
    },
    // edgeText: {
    //   background: {
    //     fill: 'transparent',
    //   },
    // },
    anchorLine: {
      stroke: '#0052d9',
      strokeWidth: 2,
      strokeDasharray: '3,2',
    },
    snapline: {
      stroke: '#0052d9',
      strokeWidth: 1,
    },
  });
  logicFlowContent.value.register(UserTask);
  logicFlowContent.value.register(ExclusiveGateway);
  logicFlowContent.value.register(StartTask);
  logicFlowContent.value.register(EndTask);
  logicFlowContent.value.register(ParallelGateway);
  logicFlowContent.value.register(InclusiveGateway);
  logicFlowContent.value.register(ServiceTask);
  logicFlowContent.value.register(BaseEdge);
  logicFlowContent.value.setDefaultEdgeType('sequence');

  logicFlowContent.value.on('history:change', ({ data: { undoAble, redoAble } }) => {
    undoDisable.value = !undoAble;
    redoDisable.value = !redoAble;
  });
  logicFlowContent.value.on('node:click', (e, data, position, msg) => {
    if (e.data.type.lastIndexOf('Gateway') >= 0) {
      rightContent.value.initNode('gateway', modelData.value, e.data);
    } else if (e.data.type.lastIndexOf('startEvent') >= 0) {
      rightContent.value.initNode('start', modelData.value, e.data);
    } else if (e.data.type.lastIndexOf('endEvent') >= 0) {
      rightContent.value.initNode('end', modelData.value, e.data);
    } else {
      rightContent.value.initNode('node', modelData.value, e.data);
    }
  });
  logicFlowContent.value.on('node:delete', (e, data, position, msg) => {
    rightContent.value.initNode('wf', modelData.value, {});
  });
  logicFlowContent.value.on('edge:click', (e, data, position, msg) => {
    rightContent.value.initNode('edge', modelData.value, e.data);
  });
  logicFlowContent.value.on('edge:delete', (e, data, position, msg) => {
    rightContent.value.initNode('wf', modelData.value, {});
  });
  logicFlowContent.value.on('blank:click', (e, data, position, msg) => {
    rightContent.value.initNode('wf', modelData.value, {});
  });
  if (modelData.value && modelData.value.logicFlow) {
    logicFlowContent.value.render(modelData.value.logicFlow);
  } else {
    logicFlowContent.value.render();
  }
  loadFlow.value.loading = false;
};

//logicflow渲染end
const firstFetch = async () => {};

//新增/修改弹窗start
const visibleModal = ref(false);
const wfForm = reactive({
  id: '',
  name: '',
  key: '',
  remark: '',
  form: '',
  formType: '',
  category: '',
  viewType: '',
  viewPage: '',
});
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});
const rules = {
  name: [{ required: true, message: '请输入流程名称', type: 'error' }],
  key: [{ required: true, message: '请输入流程编号', type: 'error' }],
  remark: [{ required: true, message: '请输入流程描述', type: 'error' }],
  category: [{ required: true, message: '请选择流程分类', type: 'error' }],
  formType: [{ required: true, message: '请选择表单类型', type: 'error' }],
  form: [{ required: true, message: '请选择关联表单', type: 'error' }],
  viewType: [{ required: true, message: '请选择详情页面类型', type: 'error' }],
  viewPage: [{ required: true, message: '请填写详情页面模板/路由', type: 'error' }],
} as Rules;
const onSubmit = async () => {
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    saveBtn.content = '保存中...';
    saveBtn.loading = true;
    let submitForm = {
      name: wfForm.name,
      key: wfForm.key,
      remark: wfForm.remark,
      category: wfForm.category,
      formType: wfForm.formType,
      form: wfForm.form,
      viewType: wfForm.viewType,
      viewPage: wfForm.viewPage,
      id: null,
    };
    if (opt.value === 'add') {
      try {
        let result1 = await add(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          await fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      submitForm.id = wfForm.id;
      try {
        let result1 = await edit(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  }
};
//新增/修改弹窗end

//左侧角色菜单列表数据start
const columns = [
  {
    width: 160,
    colKey: 'key',
    title: '编号',
    ellipsis: true,
  },
  {
    width: 160,
    colKey: 'name',
    title: '名称',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'category',
    title: '分类',
    align: 'center',
  },
  {
    width: 80,
    colKey: 'version',
    title: '版本',
    align: 'center',
  },
  {
    width: 80,
    colKey: 'status',
    title: '状态',
    align: 'center',
  },
  {
    width: 150,
    colKey: 'lastUpdateTime',
    title: '更新时间',
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 140,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;

const params = ref({
  name: '',
  key: '',
});

const opt = ref('add');

const addRow = async () => {
  //logicRender();
  opt.value = 'add';
  form.value.reset();
  wfForm.id = '';
  wfForm.key = 'Process_' + new Date().getTime();
  visibleModal.value = true;
};
const frDataRef = ref(null);
const frDataRef1 = ref(null);
const editRow = async (row) => {
  opt.value = 'edit';
  form.value.reset();
  wfForm.id = row.id;
  wfForm.name = row.name;
  wfForm.key = row.key;
  wfForm.remark = row.remark;
  wfForm.category = row.category;
  wfForm.formType = row.formType;
  wfForm.form = row.form;
  wfForm.viewPage = row.viewPage;
  wfForm.viewType = row.viewType;
  visibleModal.value = true;
  nextTick(() => {
    if (wfForm.formType === '01') {
      frDataRef.value.initData('id', wfForm.form);
    }
    if (wfForm.viewType === '01') {
      frDataRef1.value.initData('id', wfForm.viewPage);
    }
  });
};
const delRow = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认删除(' + row.name + ')流程？',
    confirmBtn: '继续删除',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      del(row.id)
        .then((res) => {
          if (res.code === 0) {
            fetchData();
            MessagePlugin.success('删除成功');
          } else {
            MessagePlugin.error('删除失败：' + res.msg);
          }
        })
        .catch((error) => {
          MessagePlugin.error('删除失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

const fetchData = async () => {
  queryRef.value.loadData();
};
//左侧角色菜单列表数据end

//vue的api
onMounted(() => {
  initData();
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
.container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.designContent {
  .t-drawer__body {
    padding: 0px !important;
  }
}
</style>
<style>
.designContent .t-drawer__body {
  padding: 0px !important;
}
</style>
