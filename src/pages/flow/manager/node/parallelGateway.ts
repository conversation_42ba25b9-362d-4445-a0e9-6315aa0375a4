import { HtmlNode, HtmlNodeModel } from '@logicflow/core';
import { computeColor } from './util';
import { createApp, h } from 'vue';
import ParallelNode from './ParallelNode.vue';

class parallelGatewayView extends HtmlNode {
  constructor(props) {
    super(props);
  }
  shouldUpdate() {
    const data = {
      ...this.props.model.properties,
      isSelected: this.props.model.isSelected,
      isHovered: this.props.model.isHovered,
    };
    if (this.preProperties && this.preProperties === JSON.stringify(data)) return;
    this.preProperties = JSON.stringify(data);
    return true;
  }
  setHtml(rootEl: HTMLElement) {
    const { properties } = this.props.model;
    rootEl.innerHTML = '';
    console.log(this.props);
    const div = document.createElement('div');

    const app = createApp({
      render: () =>
        h(ParallelNode, {
          properties: properties,
          text: this.props.model.text.value,
        }),
    });
    app.mount(div);
    rootEl.appendChild(div);
  }
  getText() {
    return null;
  }
}

class parallelGatewayModel extends HtmlNodeModel {
  constructor(data, graphModel) {
    data.text = {
      value: (data.text && data.text.value) || '',
      x: data.x,
      y: data.y + 50,
    };
    super(data, graphModel);
  }
  // 自定义节点形状属性
  setAttributes() {
    this.text.editable = false; // 禁止节点文本编辑
    // 设置节点宽高和锚点
    const width = 30;
    const height = 30;
    this.width = width;
    this.height = height;
  }
}

export default {
  type: 'bpmn:parallelGateway',
  view: parallelGatewayView,
  model: parallelGatewayModel,
};
