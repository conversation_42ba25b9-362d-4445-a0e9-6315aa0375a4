<template>
  <div>
    <div class="fr-bpm-user" :style="{ background: backgroundColor }">
      <span class="iconfont icon-guanbi"></span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { computeColor } from './util';

const props = defineProps({
  model: {
    type: Object,
  },
  text: {
    type: String,
  },
  graphModel: {
    type: Object,
  },
  isSelected: {
    type: Boolean,
  },
  isHovered: {
    type: Boolean,
  },
  disabled: {
    type: Boolean,
  },
  properties: {
    type: Object,
  },
});

const backgroundColor = computed(() => {
  return computeColor(props.properties).backColor;
});
</script>

<style scoped>
.fr-bpm-user {
  display: flex;
  flex-direction: row;
  height: 30px;
  align-items: center;
  width: 30px;
  border-radius: 50%;
  .icon-guanbi {
    font-size: 30px;
    color: #ffffff;
  }
}
</style>
