import { RectNode, RectNodeModel, h } from '@logicflow/core';

class ServiceTaskView extends RectNode {
  getLabelShape() {
    const { model } = this.props;
    const { x, y, width, height } = model;
    const style = model.getNodeStyle();
    return h(
      'svg',
      {
        x: x - width / 2 + 2,
        y: y - height / 2,
        width: 25,
        height: 25,
        viewBox: '0 0 1024 1024',
      },
      [
        h('path', {
          fill: style.stroke,
          d: 'M793.2416 212.5824a182.784 182.784 0 0 0-157.9008-91.1872H399.36a182.9376 182.9376 0 0 0-157.7984 91.1872l-117.76 204.1856a182.6816 182.6816 0 0 0 0 182.3744l117.76 204.2368A182.9888 182.9888 0 0 0 399.36 894.5152h235.8272a182.8352 182.8352 0 0 0 157.9008-91.136L911.36 599.04a182.9376 182.9376 0 0 0 0-182.3744z m69.12 358.4l-117.76 204.2368a126.3104 126.3104 0 0 1-109.1072 62.976H399.36a126.3616 126.3616 0 0 1-109.1584-62.976l-117.76-204.2368a126.5152 126.5152 0 0 1 0-126.0544L290.3552 240.64A126.2592 126.2592 0 0 1 399.36 177.7152h235.8272A126.2592 126.2592 0 0 1 744.448 240.64l117.76 204.1856a126.2592 126.2592 0 0 1 0.1536 126.1568z',
        }),
        h('path', {
          fill: style.stroke,
          d: 'M517.4272 343.3984a164.608 164.608 0 1 0 164.5568 164.5568 164.7616 164.7616 0 0 0-164.5568-164.5568z m0 272.8448a108.288 108.288 0 1 1 108.2368-108.288 108.4416 108.4416 0 0 1-108.2368 108.288z',
        }),
      ],
    );
  }
  getShape() {
    const { model } = this.props;
    const { x, y, width, height, radius } = model;
    const style = model.getNodeStyle();
    return h('g', {}, [
      h('rect', {
        ...style,
        x: x - width / 2,
        y: y - height / 2,
        rx: radius,
        ry: radius,
        width,
        height,
      }),
      this.getLabelShape(),
    ]);
  }
}

class ServiceTaskModel extends RectNodeModel {
  // 自定义节点形状属性
  initNodeData(data) {
    super.initNodeData(data);
    this.width = 100;
    this.height = 70;
    this.radius = 10;
  }
  getNodeStyle() {
    const style = super.getNodeStyle();
    style.stroke = '#0052d9';
    return style;
  }
  //   getTextStyle() {
  //     const style = super.getTextStyle();
  //     style.fontSize = 12;
  //     const properties = this.properties;
  //     style.color = 'rgb(0,82,217)';
  //     return style;
  //   }
  //   getNodeStyle() {
  //     const style = super.getNodeStyle();
  //     const properties = this.properties;
  //     style.stroke = 'rgb(0,82,217)';
  //     return style;
  //   }
  //   getAnchorStyle() {
  //     const style = super.getAnchorStyle({});
  //     style.stroke = 'rgb(0,82,217)';
  //     style.r = 3;
  //     style.hover.r = 8;
  //     style.hover.fill = 'rgb(0,82,217)';
  //     style.hover.stroke = 'rgb(0,82,217)';
  //     return style;
  //   }
  //   getAnchorLineStyle() {
  //     const style = super.getAnchorLineStyle({});
  //     style.stroke = 'rgb(0,82,217)';
  //     return style;
  //   }
  //   getOutlineStyle() {
  //     const style = super.getOutlineStyle();
  //     style.stroke = 'rgb(0,82,217)';
  //     style.hover.stroke = 'rgb(0,82,217)';
  //     return style;
  //   }
}

export default {
  type: 'bpmn:serviceTask',
  view: ServiceTaskView,
  model: ServiceTaskModel,
};
