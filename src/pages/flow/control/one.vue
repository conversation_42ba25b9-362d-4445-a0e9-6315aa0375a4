<template>
  <div>
    <div class="sp-role-left">
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
            url: '/flow/instance/list',
            method: 'get',
          }"
          :max-height="tableHeight"
        >
          <template #frSimpleBtn>
            <!-- <t-button @click="startFlow">发起一个流程</t-button> -->
            <!-- <t-button @click="dealTask" theme="default">处理任务</t-button> -->
          </template>
          <template #frSimpleQuery>
            <t-select
              v-model="params.status"
              placeholder="请选择实例状态"
              clearable
              @change="firstFetch"
              :style="{ width: '180px' }"
            >
              <t-option
                v-for="(item, index) in flowStatusArr"
                :key="index"
                :value="item.value"
                :label="item.label"
              ></t-option>
            </t-select>
            <t-input
              placeholder="请输入流程名称"
              type="search"
              clearable
              v-model="params.processName"
              @enter="firstFetch"
              :style="{ width: '260px' }"
            ></t-input>
            <t-input
              placeholder="请输入流程标题"
              type="search"
              clearable
              v-model="params.flowTitle"
              @enter="firstFetch"
              :style="{ width: '260px' }"
            ></t-input>
          </template>
          <template #flowTitle="{ row }">
            <t-link theme="primary" @click="viewFlow(row)">{{ row.flowTitle }}</t-link>
          </template>
          <template #status="{ row }">
            <t-tag v-if="row.status === '1' || row.status === '99'" theme="success" variant="light">{{
              flowStatus[row.status]
            }}</t-tag>
            <t-tag v-if="row.status === '0'" theme="warning" variant="light">{{ flowStatus[row.status] }}</t-tag>
            <t-tag v-if="row.status === '-1'" theme="danger" variant="light">{{ flowStatus[row.status] }}</t-tag>
          </template>
          <template #operation="{ row }">
            <t-button size="small" variant="text" theme="primary" @click="showDesign(row.processInstanceId)"
              >流程图</t-button
            >
            <t-button size="small" variant="text" theme="primary" @click="showLog(row.processInstanceId)"
              >流转日志</t-button
            >
            <t-dropdown
              :options="[
                {
                  content: '暂停',
                  value: 1,
                  disabled: row.status !== '1',
                  onClick: () => {
                    btnVal = '1';
                  },
                },
                {
                  content: '恢复',
                  value: 2,
                  disabled: row.status !== '0',
                  onClick: () => {
                    btnVal = '2';
                  },
                },
                {
                  content: '作废',
                  value: 3,
                  disabled: row.status !== '0' && row.status !== '1',
                  onClick: () => {
                    btnVal = '3';
                  },
                },
                {
                  content: '删除',
                  value: 4,
                  disabled: row.status !== '-1',
                  onClick: () => {
                    btnVal = '4';
                  },
                },
              ]"
              @click="clickHandler(row)"
            >
              <t-button variant="text" theme="primary" size="small"> 更多</t-button>
            </t-dropdown>
          </template>
        </FrQuery>
      </t-card>
    </div>
    <t-dialog
      v-model:visible="visibleModal"
      width="900"
      :closeOnOverlayClick="false"
      :header="'流转日志'"
      destroyOnClose
      mode="modal"
      draggable
      :confirm-btn="null"
      cancelBtn="关闭"
    >
      <ProcessLog :data="logArray"></ProcessLog>
    </t-dialog>

    <t-dialog
      v-model:visible="visibleModal1"
      width="400"
      :closeOnOverlayClick="false"
      :header="'作废流程'"
      mode="modal"
      draggable
      :confirm-btn="confrimBtn"
      cancelBtn="关闭"
      :onConfirm="abrogateFlow"
    >
      <t-textarea
        v-model="reason"
        :autosize="{ minRows: 5, maxRows: 7 }"
        placeholder="请输入作废原因"
        :maxlength="100"
      />
    </t-dialog>

    <t-dialog
      v-model:visible="visibleModal2"
      width="1000"
      :top="'40px'"
      :closeOnOverlayClick="false"
      :header="'流程图'"
      mode="modal"
      draggable
      :confirm-btn="null"
      cancelBtn="关闭"
    >
      <InstanceDesign ref="logicFlow"></InstanceDesign>
    </t-dialog>
    <ViewPage ref="flowViewRef"></ViewPage>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListTenant',
};
</script>

<script setup lang="ts">
import { ref, onMounted, reactive, getCurrentInstance } from 'vue';
import { list, start, stop, abrogate, active, deleteFlow, commitFlow, logList } from '@/api/flow/instance';
import { dicVals } from '@/api/common';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import ProcessLog from './../ProcessLog.vue';
import InstanceDesign from './../frame/design.vue';
import FrQuery from '@/components/fr-query/index.vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import ViewPage from './../frame/view.vue';

const queryRef = ref(null);
//获取table全局高度
const tableHeight = getCurrentInstance().appContext.config.globalProperties.$frGlobal.tabHeight;
dayjs.extend(duration);

//显示任务处理页面
const flowViewRef = ref(null);
const viewFlow = (data: CommonObject) => {
  flowViewRef.value.openWind(data);
};

//弹出流程实例图
const visibleModal2 = ref(false);
const logicFlow = ref(null);
const showDesign = (id: string) => {
  visibleModal2.value = true;
  logicFlow.value.logicRender({
    id: id,
  });
};

//作废流程需要填写原因
const visibleModal1 = ref(false);
const selectProcessId = ref('');
const reason = ref('');
const confrimBtn = reactive({
  content: '作废',
  loading: false,
});
const abrogateFlow = async () => {
  if (!reason.value) {
    MessagePlugin.warning('请输入作废原因');
    return;
  }
  confrimBtn.content = '作废中...';
  confrimBtn.loading = true;
  try {
    let res = await abrogate({
      processInstanceId: selectProcessId.value,
      reason: reason.value,
    });
    visibleModal1.value = false;
    console.log(res);
    if (res.code === 0) {
      MessagePlugin.success('作废成功');
      fetchData();
    } else {
      MessagePlugin.error('作废失败:' + res.msg);
    }
  } catch (e) {
    MessagePlugin.error('作废失败:' + e);
  } finally {
    confrimBtn.content = '作废';
    confrimBtn.loading = false;
  }
};

//发起流程
const startFlow = async () => {
  let res = await start({
    processKey: 'Process_1688393044529',
  });
  console.log(res.code, res.msg);
};
const dealTask = async () => {};

//查询流程日志
const visibleModal = ref(false);
const logArray = ref<CommonArray>([]);
const showLog = async (id) => {
  logArray.value = [];
  visibleModal.value = true;
  let res = await logList(id);
  logArray.value = res.data.records;
};

//查询缓存字典
const flowStatus = ref<CommonObject>({});
const flowStatusArr = ref<CommonArray>([]);
const initDic = async () => {
  let res = await dicVals('SP_FLOW_INSTANCESTATUS');
  flowStatusArr.value = res.data;
  flowStatusArr.value.map((obj) => {
    flowStatus.value[obj.value] = obj.label;
  });
};

const firstFetch = async () => {
  queryRef.value.loadData(true);
};

const optMap = {
  '1': '暂停',
  '2': '恢复',
  '3': '作废',
  '4': '删除',
};

//流程实例操作
const btnVal = ref('');
const clickHandler = async (row: CommonObject) => {
  if (btnVal.value === '3') {
    selectProcessId.value = row.processInstanceId;
    visibleModal1.value = true;
    return;
  }
  try {
    const confirmDia = DialogPlugin({
      header: '提醒',
      body: '是否确认' + optMap[btnVal.value] + '(' + row.flowTitle + ')流程？',
      confirmBtn: '确认',
      onConfirm: async ({ e }) => {
        confirmDia.hide();
        let loadMsg = MessagePlugin.loading('正在处理中...');
        let res;
        if (btnVal.value === '1') {
          res = await stop(row.processInstanceId);
          console.log(res);
        } else if (btnVal.value === '2') {
          res = await active(row.processInstanceId);
          console.log(res);
        } else if (btnVal.value === '4') {
          res = await deleteFlow(row.processInstanceId);
          console.log(res);
        }
        MessagePlugin.close(loadMsg);
        if (res.code === 0) {
          MessagePlugin.success('操作成功');
          await fetchData();
        } else {
          MessagePlugin.error('操作失败：' + res.msg);
        }
      },
      onClose: ({ e, trigger }) => {
        confirmDia.hide();
      },
    });
  } catch (e) {
    MessagePlugin.error('操作失败：' + e);
  }
};

//左侧角色菜单列表数据start
const columns = [
  {
    width: 200,
    colKey: 'flowTitle',
    title: '流程标题',
    align: 'left',
    ellipsis: true,
  },
  {
    width: 130,
    colKey: 'processName',
    title: '流程名称',
    align: 'center',
  },
  {
    width: 80,
    colKey: 'status',
    title: '流程状态',
    align: 'center',
  },
  {
    width: 110,
    colKey: 'currentNodes',
    title: '当前节点',
    align: 'left',
    ellipsis: true,
  },
  {
    width: 160,
    colKey: 'currentAssignees',
    title: '候选人',
    ellipsis: true,
  },
  {
    width: 80,
    colKey: 'version',
    title: '版本号',
    align: 'center',
  },
  //   {
  //     width: 160,
  //     colKey: 'processInstanceId',
  //     title: '流程实例ID',
  //   },
  {
    width: 120,
    colKey: 'startTime',
    title: '发起时间',
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 180,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;

const params = ref({
  status: '',
  flowTitle: '',
  processName: '',
});

const fetchData = async () => {
  queryRef.value.loadData();
};
//左侧角色菜单列表数据end

//vue的api
onMounted(() => {
  initDic();
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
