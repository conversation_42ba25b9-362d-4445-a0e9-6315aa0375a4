<template>
  <div>
    <div class="sp-role-left">
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
            url: '/flow/task/allTask',
            method: 'get',
          }"
          :max-height="tableHeight"
          @success-back="successBack"
        >
          <template #frSimpleQuery>
            <t-input
              placeholder="请输入流程标题"
              type="search"
              clearable
              v-model="params.flowTitle"
              @enter="firstFetch"
              :style="{ width: '260px' }"
            ></t-input>
            <t-select
              placeholder="请选择流程"
              filterable
              clearable
              v-model="params.processKey"
              :style="{ width: '260px' }"
              @change="firstFetch"
            >
              <t-option
                v-for="(item, index) in flowArray"
                :key="index"
                :value="item.processKey"
                :label="item.processName"
              ></t-option>
            </t-select>
          </template>
          <template #flowTitle="{ row }">
            <t-link theme="primary" @click="viewFlow(row)">{{ row.flowTitle }}</t-link>
          </template>
          <template #assignees="{ row }"> {{ userMap[row.assignees] }}</template>
          <template #startUser="{ row }"> {{ userMap[row.startUser] }}</template>
          <template #operation="{ row }">
            <t-space size="4px">
              <t-button size="small" variant="text" theme="primary" @click="delegateFun(row.taskId)">委托</t-button>
              <t-button size="small" variant="text" theme="primary" @click="transferFun(row.taskId)">转办</t-button>
            </t-space>
          </template>
        </FrQuery>
      </t-card>
    </div>

    <t-dialog
      v-model:visible="visibleModal1"
      width="400"
      :closeOnOverlayClick="false"
      :header="optType === 'transfer' ? '任务转办' : '任务委托'"
      mode="modal"
      draggable
      :confirm-btn="confrimBtn"
      cancelBtn="关闭"
      :onConfirm="confirmSubmit"
    >
      <FrUser v-model="checkUser" placeholder="请输入用户名称进行模糊匹配" />
    </t-dialog>
    <ViewPage ref="flowViewRef"></ViewPage>
  </div>
</template>

<script lang="ts">
export default {
  name: 'todoListPage',
};
</script>

<script setup lang="ts">
import { ref, onMounted, reactive, getCurrentInstance } from 'vue';
import { userNameByIds } from '@/api/common';
import { processList } from '@/api/flow/design';
import { transfer, delegate } from '@/api/flow/instance';
import { MessagePlugin } from 'tdesign-vue-next';
import FrQuery from '@/components/fr-query/index.vue';
import FrUser from '@/components/fr-user/index.vue';
import ViewPage from './../frame/view.vue';

const queryrRef = ref(null);
//获取table全局高度
const tableHeight = getCurrentInstance().appContext.config.globalProperties.$frGlobal.tabHeight;

//显示任务处理页面
const flowViewRef = ref(null);
const viewFlow = (data: CommonObject) => {
  flowViewRef.value.openWind(data);
};

//初始化流程名称下拉框
const flowArray = ref<CommonArray>([]);
const initFlow = async () => {
  let res = await processList({});
  flowArray.value = res.data;
};

//委托
const visibleModal1 = ref(false);
const taskId = ref('');
const options1 = ref<CommonArray>([]);
const checkUser = ref('');
const optType = ref('');

const confrimBtn = reactive({
  content: '确认委托',
  loading: false,
});

const transferFun = (id: string) => {
  taskId.value = id;
  checkUser.value = '';
  confrimBtn.content = '确认转办';
  optType.value = 'transfer';
  visibleModal1.value = true;
};
const delegateFun = (id: string) => {
  taskId.value = id;
  checkUser.value = '';
  confrimBtn.content = '确认委托';
  optType.value = 'delegate';
  visibleModal1.value = true;
};
const confirmSubmit = async () => {
  if (!checkUser.value) {
    MessagePlugin.warning('请选择人员');
    return;
  }
  confrimBtn.content = '操作中...';
  confrimBtn.loading = true;
  let toUserName = '';
  options1.value.map((obj) => {
    console.log(obj);
    if (obj.value === checkUser.value) {
      toUserName = obj.nickname;
    }
  });
  let res;
  if (optType.value === 'delegate') {
    res = await delegate({
      taskId: taskId.value,
      toUserId: checkUser.value,
      toUser: toUserName,
    });
  } else {
    res = await transfer({
      taskId: taskId.value,
      toUserId: checkUser.value,
      toUser: toUserName,
    });
  }

  if (res.code === 0) {
    visibleModal1.value = false;
    fetchData();
    MessagePlugin.success(optType.value === 'delegate' ? '委托成功' : '转办成功');
  } else {
    MessagePlugin.error(optType.value === 'delegate' ? '委托失败' : '转办失败');
  }
  confrimBtn.content = optType.value === 'delegate' ? '确认委托' : '确认转办';
  confrimBtn.loading = false;
};

const firstFetch = async () => {
  queryrRef.value.loadData(true);
};

//左侧角色菜单列表数据start
const columns = [
  {
    width: 260,
    colKey: 'flowTitle',
    title: '流程标题',
    ellipsis: true,
  },
  {
    width: 140,
    colKey: 'processName',
    title: '流程名称',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'currentAssignees',
    title: '候选群组',
    align: 'center',
  },
  {
    width: 140,
    colKey: 'currentNodes',
    title: '当前节点',
    align: 'center',
  },
  {
    width: 120,
    colKey: 'assignees',
    title: '指定候选人',
    align: 'center',
  },
  {
    width: 160,
    colKey: 'startTime',
    title: '任务接收时间',
    align: 'center',
  },
  {
    width: 120,
    colKey: 'startUser',
    title: '发起人',
    align: 'center',
  },
  {
    width: 120,
    colKey: 'version',
    title: '流程版本号',
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 120,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;

const params = ref({
  flowTitle: '',
  processName: '',
  processKey: '',
});

const successBack = async (list) => {
  let userId = [];
  list.map((row) => {
    if (row.startUser) {
      userId.push(row.startUser);
    }
    if (row.assignees) {
      userId.push(row.assignees);
    }
  });
  let res2 = await userNameByIds({
    ids: userId.join(','),
  });
  userMap.value = res2.data;
};

const userMap = ref<CommonObject>({});
const fetchData = async () => {
  queryrRef.value.loadData();
};
//左侧角色菜单列表数据end

//vue的api
onMounted(() => {
  initFlow();
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
