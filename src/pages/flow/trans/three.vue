<template>
  <div>
    <t-card :bordered="false">
      <FrQuery
        ref="queryRef"
        v-model:params="params"
        :columns="columns"
        :request="{
          url: '/flow/task/requestList',
          method: 'get',
        }"
        @success-back="successBack"
      >
        <template #frSimpleQuery>
          <t-input
            placeholder="请输入流程标题"
            type="search"
            clearable
            v-model="params.flowTitle"
            :style="{ width: '260px' }"
          ></t-input>
          <t-select
            placeholder="请选择流程"
            filterable
            clearable
            v-model="params.processKey"
            :style="{ width: '260px' }"
          >
            <t-option
              v-for="(item, index) in flowArray"
              :key="index"
              :value="item.processKey"
              :label="item.processName"
            ></t-option>
          </t-select>
        </template>
        <template #startUser="{ row }">
          {{ userMap[row.startUser] }}
        </template>
        <template #status="{ row }">
          <t-tag v-if="row.status === '1' || row.status === '99'" theme="success" variant="light">{{
            flowStatus[row.status]
          }}</t-tag>
          <t-tag v-if="row.status === '0'" theme="warning" variant="light">{{ flowStatus[row.status] }}</t-tag>
          <t-tag v-if="row.status === '-1'" theme="danger" variant="light">{{ flowStatus[row.status] }}</t-tag>
        </template>
        <template #duration="{ row }">
          {{ row.duration ? dayjs.duration(row.duration).asSeconds() + '秒' : '' }}
        </template>
        <template #operation="{ row }">
          <t-button size="small" variant="text" theme="primary" @click="viewFlow(row)">详情</t-button>
          <t-button size="small" variant="text" theme="primary" @click="showLog(row.processInstanceId)"
            >流转跟踪</t-button
          >
        </template>
      </FrQuery>
    </t-card>
    <t-dialog
      v-model:visible="visibleModal"
      width="900"
      :closeOnOverlayClick="false"
      :header="'流转日志'"
      destroyOnClose
      mode="modal"
      draggable
      :confirm-btn="null"
      cancelBtn="关闭"
    >
      <ProcessLog :data="logArray"></ProcessLog>
    </t-dialog>
    <ViewPage ref="flowViewRef"></ViewPage>
  </div>
</template>

<script lang="ts">
export default {
  name: 'haveListPage',
};
</script>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance } from 'vue';
import { logList } from '@/api/flow/instance';
import { dicVals, userNameByIds } from '@/api/common';
import { processList } from '@/api/flow/design';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import ProcessLog from './../ProcessLog.vue';
import FrQuery from '@/components/fr-query/index.vue';
import ViewPage from './../frame/view.vue';

const queryRef = ref(null);
const tableHeight = getCurrentInstance().appContext.config.globalProperties.$frGlobal.tabHeight;
dayjs.extend(duration);
//初始化流程名称下拉框
const flowArray = ref<CommonArray>([]);
const initFlow = async () => {
  let res = await processList({});
  flowArray.value = res.data;
};

//显示任务处理页面
const flowViewRef = ref(null);
const viewFlow = (data: CommonObject) => {
  flowViewRef.value.openWind(data);
};

//查询缓存字典
const flowStatus = ref<CommonObject>({});
const flowStatusArr = ref<CommonArray>([]);
const initDic = async () => {
  let res = await dicVals('SP_FLOW_INSTANCESTATUS');
  flowStatusArr.value = res.data;
  flowStatusArr.value.map((obj) => {
    flowStatus.value[obj.value] = obj.label;
  });
};

//查询流程日志
const visibleModal = ref(false);
const logArray = ref<CommonArray>([]);
const showLog = async (id) => {
  logArray.value = [];
  visibleModal.value = true;
  let res = await logList(id);
  logArray.value = res.data.records;
};

//左侧角色菜单列表数据start
const columns = [
  {
    width: 260,
    colKey: 'flowTitle',
    title: '流程标题',
    ellipsis: true,
  },
  {
    width: 140,
    colKey: 'processName',
    title: '流程名称',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'startUser',
    title: '发起人',
    align: 'center',
  },
  {
    width: 120,
    colKey: 'status',
    title: '流程状态',
    align: 'center',
  },
  {
    width: 140,
    colKey: 'currentNodes',
    title: '当前节点',
    align: 'center',
  },
  {
    width: 160,
    colKey: 'startTime',
    title: '发起时间',
    align: 'center',
  },
  {
    width: 160,
    colKey: 'endTime',
    title: '结束时间',
    align: 'center',
  },
  {
    width: 120,
    colKey: 'duration',
    title: '耗时',
    align: 'center',
  },
  {
    width: 120,
    colKey: 'version',
    title: '流程版本号',
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 90,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;

const params = ref({
  flowTitle: '',
  processName: '',
  processKey: '',
});
const userMap = ref<CommonObject>({});
const successBack = async (list) => {
  let userId = [];
  list.map((row) => {
    if (row.startUser) {
      userId.push(row.startUser);
    }
  });
  let res2 = await userNameByIds({
    ids: userId.join(','),
  });
  userMap.value = res2.data;
};
//左侧角色菜单列表数据end

//vue的api
onMounted(() => {
  initDic();
  initFlow();
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
