<template>
  <div>
    <div class="title">
      <CaretRightIcon size="13px" class="ticon" />
      <span>处理信息</span>
    </div>
    <div class="form-content">
      <t-form ref="checkFormRef" :data="data" :rules="rules" label-width="100px">
        <t-form-item label="处理结果" name="frAction">
          <t-radio-group :options="actions" v-model="data.frAction"></t-radio-group>
        </t-form-item>
        <t-form-item label="处理意见" name="frRemark">
          <t-textarea
            placeholder="请输入处理意见"
            name="description"
            :autosize="{ minRows: 3, maxRows: 5 }"
            :maxlength="150"
            v-model="data.frRemark"
          />
        </t-form-item>
      </t-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { CaretRightIcon } from 'tdesign-icons-vue-next';
import { nextAction } from '@/api/flow/trans';

const props = defineProps({
  taskId: String,
});

const data = ref({
  frAction: '',
  frRemark: '',
});
const rules = ref<Rules>({
  frAction: [{ required: true, message: '请选择处理结果' }],
  frRemark: [{ required: true, message: '请输入处理意见' }],
});
const checkFormRef = ref(null);
const actions = ref([]);

watch(
  () => props.taskId,
  async (val) => {
    let res = await nextAction({
      taskId: val,
    });
    actions.value = res.data;
  },
);

onMounted(async () => {
  if (props.taskId) {
    let res = await nextAction({
      taskId: props.taskId,
    });
    actions.value = res.data;
  }
});

const check = async () => {
  let result = await checkFormRef.value.validate();
  if (typeof result !== 'object' && result) {
    return true;
  }
  return false;
};

const getData = () => {
  return data.value;
};

defineExpose({
  check,
  getData,
});
</script>

<style scoped lang="less">
.title {
  height: 40px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 2px solid var(--td-brand-color);
  width: 90%;
  span {
    font-size: 14px;
    color: var(--td-brand-color) !important;
  }
}
.form-content {
  padding-top: 20px;
}
</style>
