<template>
  <div v-loading="loadPage">
    <t-form ref="form" :label-align="'right'" label-width="150px" :data="formData" :rules="rules">
      <t-form-item label="密码复杂度正则" name="pwdComplexity">
        <t-input style="width: 800px" v-model="formData.pwdComplexity" placeholder="请输入密码复杂度正则表达式">
        </t-input>
      </t-form-item>
      <t-form-item label="密码复杂度提示" name="pwdValidMsg">
        <t-input style="width: 400px" v-model="formData.pwdValidMsg" placeholder="请输入密码复杂度错误提示"> </t-input>
      </t-form-item>
      <t-form-item label="密码有效期" name="pwdValid">
        <t-input-number
          style="width: 400px"
          placeholder="请输入密码有效期"
          :decimalPlaces="0"
          v-model="formData.pwdValid"
          theme="column"
          :min="0"
        >
          <template #suffix><span>天</span></template>
        </t-input-number>
      </t-form-item>
      <t-form-item label="密码过期提前提醒" name="pwdValidDay">
        <t-input-number
          style="width: 400px"
          placeholder="请输入密码过期提前提醒天数"
          :decimalPlaces="0"
          v-model="formData.pwdValidDay"
          theme="column"
          :min="1"
        >
          <template #suffix><span>天</span></template>
        </t-input-number>
      </t-form-item>
      <t-form-item label="历史密码重复校验" name="pwdRepeat">
        <t-input-number
          style="width: 400px"
          placeholder="请输入历史密码重复校验次数"
          :decimalPlaces="0"
          v-model="formData.pwdRepeat"
          theme="column"
          :min="0"
        >
          <template #suffix><span>次</span></template>
        </t-input-number>
      </t-form-item>
      <t-form-item :label="null">
        <t-popconfirm content="是否确定保存?" @confirm="save">
          <t-button>保存</t-button>
        </t-popconfirm>
      </t-form-item>
    </t-form>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { password, savePassword } from '@/api/system/config';
import _ from 'lodash';
import { MessagePlugin } from 'tdesign-vue-next';

const form = ref(null);
const formData = ref({
  pwdComplexity: null,
  pwdValid: null,
  pwdRepeat: null,
  pwdValidDay: null,
  pwdValidMsg: null,
});

const rules = ref<Rules>({
  pwdComplexity: [{ required: true, message: '请输入密码复杂度正则表达式' }],
  pwdValid: [{ required: true, message: '请输入密码有效期' }],
  pwdRepeat: [{ required: true, message: '请输入历史密码重复校验次数' }],
  pwdValidDay: [{ required: true, message: '请输入密码过期提前提醒天数' }],
  pwdValidMsg: [{ required: true, message: '请输入密码复杂度错误提示' }],
});

const loadPage = ref(false);
onMounted(async () => {
  loadPage.value = true;
  let res = await password();
  formData.value.pwdComplexity = res.data.pwdComplexity;
  formData.value.pwdRepeat = res.data.pwdRepeat;
  formData.value.pwdValid = res.data.pwdValid;
  formData.value.pwdValidDay = res.data.pwdValidDay;
  formData.value.pwdValidMsg = res.data.pwdValidMsg;
  loadPage.value = false;
});
const save = async () => {
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    loadPage.value = true;
    await savePassword(formData.value);
    loadPage.value = false;
    MessagePlugin.success('保存成功');
  }
};
</script>

<style scoped lang="less"></style>
