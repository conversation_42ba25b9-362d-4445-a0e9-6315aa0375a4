<template>
  <div>
    <div class="sp-role-left">
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
            url: '/center/branch/queryOrganTree',
            method: 'get',
          }"
          :is-pagination="false"
          :is-tree="true"
        >
          <template #frSimpleQuery>
            <FrTenant
              placeholder="关联机构"
              v-model="params.tenantId"
              width="300px"
              @change="changeTenant"
              @clear="clearName"
            />
          </template>
          <template #operation="{ row }">
            <t-space size="4px">
              <t-button v-if="authAdd" size="small" variant="text" theme="primary" @click="addRow(row)"
                >新增下级组织</t-button
              >
              <t-button
                v-if="authEdit"
                :disabled="row.parentId === '0'"
                size="small"
                variant="text"
                theme="primary"
                @click="editRow(row)"
                >修改</t-button
              >
              <t-button
                v-if="authDel"
                :disabled="row.parentId === '0' || (row.children && row.children.length != 0)"
                size="small"
                variant="text"
                theme="danger"
                @click="delRow(row)"
                >删除</t-button
              >
            </t-space>
          </template>
        </FrQuery>
      </t-card>
    </div>
    <!-- 新增/修改角色 -->
    <t-dialog
      v-model:visible="visibleModal"
      width="600"
      :closeOnOverlayClick="false"
      :header="opt === 'add' ? '新增客户端用户' : '修改客户端用户'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form ref="form" :label-align="'right'" :data="tenantForm" :layout="'inline'" :rules="rules">
          <t-form-item v-if="opt === 'edit'" label="组织编号" name="id">
            <t-input v-model="tenantForm.id" :style="{ width: '400px' }" :disabled="true"></t-input>
          </t-form-item>
          <t-form-item label="所属机构" name="tenantName">
            <t-input v-model="tenantForm.tenantName" :style="{ width: '400px' }" :disabled="true"></t-input>
          </t-form-item>
          <t-form-item label="上级组织" name="parentName">
            <t-input v-model="tenantForm.parentName" :style="{ width: '400px' }" :disabled="true"></t-input>
          </t-form-item>
          <t-form-item label="组织名称" name="name">
            <t-input v-model="tenantForm.name" :style="{ width: '400px' }" placeholder="请输入组织名称"></t-input>
          </t-form-item>
          <t-form-item label="排序" name="sort">
            <t-input v-model="tenantForm.sort" :style="{ width: '400px' }" placeholder="请输入排序"></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListTenant',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { addOrgan, editOrgan, delOrgan } from '@/api/system/organ';
import FrTenant from '@/components/fr-tenant/index.vue';
import FrQuery from '@/components/fr-query/index.vue';
import { useUserStore } from '@/store';

const queryRef = ref(null);
//权限控制
const userStore = useUserStore();
const authAdd = computed(() => userStore.roles.includes('system:organ:add'));
const authEdit = computed(() => userStore.roles.includes('system:organ:edit'));
const authDel = computed(() => userStore.roles.includes('system:organ:del'));

const changeTenant = async (val) => {
  if (val) {
    queryRef.value.loadData(true);
  }
};

//新增/修改弹窗start
const visibleModal = ref(false);
const tenantForm = reactive({
  id: '',
  name: '',
  parentId: '',
  parentName: '',
  sort: '',
  tenantId: '',
  tenantName: '',
});
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});
const rules = {
  name: [{ required: true, message: '请输入组织名称', type: 'error' }],
  sort: [{ required: true, message: '请输入排序', type: 'error' }],
} as Rules;
const onSubmit = async () => {
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    saveBtn.content = '保存中...';
    saveBtn.loading = true;
    let submitForm = {
      name: tenantForm.name,
      parentId: tenantForm.parentId,
      sort: tenantForm.sort,
      tenantId: tenantForm.tenantId,
      id: null,
    };
    if (opt.value === 'add') {
      try {
        let result1 = await addOrgan(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          await fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      submitForm.id = tenantForm.id;
      try {
        let result1 = await editOrgan(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  }
};
//新增/修改弹窗end

//左侧角色菜单列表数据start
const columns = [
  {
    width: 220,
    colKey: 'name',
    title: '组织名称',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'id',
    title: '组织编号',
  },
  {
    width: 120,
    colKey: 'tenantName',
    title: '所属机构',
  },
  {
    width: 120,
    colKey: 'createTime',
    title: '创建时间',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 200,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;

const params = ref({
  tenantId: '',
});

const clearName = async () => {
  queryRef.value.loadData(true);
};
const opt = ref('add');
const addRow = async (row) => {
  opt.value = 'add';
  form.value.reset();
  tenantForm.id = '';
  tenantForm.parentId = row.id;
  tenantForm.tenantId = row.tenantId;
  tenantForm.parentName = row.name;
  tenantForm.tenantName = row.tenantName;
  visibleModal.value = true;
};
const editRow = async (row) => {
  opt.value = 'edit';
  form.value.reset();
  tenantForm.id = row.id;
  tenantForm.name = row.name;
  tenantForm.parentId = row.parentId;
  tenantForm.sort = row.sort;
  tenantForm.tenantId = row.tenantId;
  tenantForm.parentName = row.parentName;
  tenantForm.tenantName = row.tenantName;
  visibleModal.value = true;
};
const delRow = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认删除(' + row.name + ')组织？',
    confirmBtn: '继续删除',
    //cancelBtn: '在考虑下',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      delOrgan(row.id)
        .then((res) => {
          if (res.code === 0) {
            fetchData();
            MessagePlugin.success('删除成功');
          } else {
            MessagePlugin.error('删除失败：' + res.msg);
          }
        })
        .catch((error) => {
          MessagePlugin.error('删除失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

const fetchData = async () => {
  queryRef.value.loadData();
};
//左侧角色菜单列表数据end

//vue的api
onMounted(async () => {});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
