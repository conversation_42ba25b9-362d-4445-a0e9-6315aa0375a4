<template>
  <div class="sp-main-info">
    <t-loading size="small" :loading="loading" show-overlay>
      <t-form ref="form" :data="aliForm" @submit="onSubmit" labelWidth="160px" :rules="rules">
        <t-form-item label="地址" name="endpoint">
          <t-input v-model="aliForm.endpoint" clearable placeholder="请输入Ftp地址"></t-input>
        </t-form-item>
        <t-form-item label="端口号" name="region">
          <t-input v-model="aliForm.region" clearable placeholder="请输入端口号"></t-input>
        </t-form-item>
        <t-form-item label="存储路径" name="workspace">
          <t-input v-model="aliForm.workspace" clearable placeholder="请输入存储路径"></t-input>
        </t-form-item>
        <t-form-item label="用户名" name="accessoryId">
          <t-input v-model="aliForm.accessoryId" clearable placeholder="请输入用户名"></t-input>
        </t-form-item>
        <t-form-item label="密码" name="accessorySecret">
          <t-input v-model="aliForm.accessorySecret" type="password" clearable placeholder="请输入密码"></t-input>
        </t-form-item>
        <t-form-item style="padding-top: 8px">
          <t-button theme="primary" type="submit" :loading="saveBtn.loading" style="margin-right: 10px">{{
            saveBtn.text
          }}</t-button>
        </t-form-item>
      </t-form>
    </t-loading>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { addOrUpdateOss, getOssInfo } from '@/api/system/oss';

const ossType = ref('FTP');
const form = ref(null);

const loading = ref(false);
const saveBtn = reactive({
  text: '保存',
  loading: false,
});

const initData = async () => {
  form.value.reset();
  loading.value = true;
  try {
    let res = await getOssInfo(ossType.value);
    if (res.data) {
      aliForm.id = res.data.id;
      aliForm.region = res.data.region;
      aliForm.workspace = res.data.workspace;
      aliForm.accessoryId = res.data.accessoryId;
      aliForm.accessorySecret = res.data.accessorySecret;
      aliForm.endpoint = res.data.endpoint;
    }
  } catch (error) {
  } finally {
    loading.value = false;
  }
};

//表单
const rules = {
  endpoint: [{ required: true, message: '请输入地址', type: 'error', trigger: 'change' }],
  region: [{ required: true, message: '请输入端口号', type: 'error', trigger: 'change' }],
  workspace: [{ required: true, message: '请输入路径', type: 'error', trigger: 'change' }],
  accessoryId: [{ required: true, message: '请输入账号', type: 'error', trigger: 'change' }],
  accessorySecret: [{ required: true, message: '请输入密码', type: 'error', trigger: 'change' }],
} as Rules;
const aliForm = reactive({
  id: '',
  region: '',
  workspace: '',
  accessoryId: '',
  accessorySecret: '',
  endpoint: '',
});
const onSubmit = async ({ validateResult }) => {
  if (validateResult === true) {
    const confirmDia = DialogPlugin({
      header: '提醒',
      body: '是否确定保存(Ftp存储)本次的修改?',
      confirmBtn: '确定',
      //cancelBtn: '暂不',
      onConfirm: async ({ e }) => {
        saveBtn.text = '保存中...';
        saveBtn.loading = true;
        confirmDia.hide();
        let subForm = {
          id: aliForm.id,
          region: aliForm.region,
          workspace: aliForm.workspace,
          accessoryId: aliForm.accessoryId,
          accessorySecret: aliForm.accessorySecret,
          endpoint: aliForm.endpoint,
          type: ossType.value,
        };
        try {
          let res = await addOrUpdateOss(subForm);
          if (res.code === 0) {
            await initData();
            MessagePlugin.success('保存成功');
          } else {
            MessagePlugin.error('保存失败：' + res.msg);
          }
        } catch (error) {
          MessagePlugin.error('保存失败：' + error);
        } finally {
          saveBtn.text = '保存';
          saveBtn.loading = false;
        }
      },
      onClose: ({ e, trigger }) => {
        confirmDia.hide();
      },
    });
  }
};
//vue的api
onMounted(async () => {
  await initData();
});

defineExpose({
  initData,
});
</script>

<style lang="less" scoped>
@import '@/style/variables';

.sp-main-info {
  padding: 30px;
  //width: 50%;
}
</style>
