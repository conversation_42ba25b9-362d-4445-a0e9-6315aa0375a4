<template>
  <div>
    <div class="sp-role-left">
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
            url: '/auth/online',
            method: 'get',
          }"
          :row-key="'userName'"
        >
          <template #frSimpleQuery>
            <t-input
              v-model="params.name"
              placeholder="请输入登录账号/昵称"
              type="search"
              clearable
              @enter="firstFetch"
              :style="{ width: '220px' }"
            ></t-input>
          </template>
          <template #operation="{ row }">
            <t-button size="small" variant="text" theme="primary" @click="userOut(row)">踢他下线</t-button>
          </template>
        </FrQuery>
      </t-card>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListBase',
};
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { userLogout } from '@/api/system/auth';
import FrQuery from '@/components/fr-query/index.vue';

const queryRef = ref(null);
const firstFetch = async () => {
  queryRef.value.loadData(true);
};

// 左侧角色菜单列表数据start
const columns = [
  {
    width: 120,
    colKey: 'clientId',
    title: '客户端用户',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'userName',
    title: '登录用户',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'nickName',
    title: '用户昵称',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'loginDate',
    title: '登录时间',
    align: 'center',
  },
  {
    width: 100,
    colKey: 'timeout',
    title: '有效期(s)',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 100,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;

const params = ref({
  name: '',
});

const userOut = async (row) => {
  const confirmDia = DialogPlugin({
    header: '下线提醒',
    body: `是否踢(${row.nickName}-${row.userName})下线？`,
    confirmBtn: '确定',
    onConfirm: async ({ e }) => {
      confirmDia.hide();
      const res = await userLogout({
        userId: row.userId,
        token: row.token,
      });
      if (res.code === 0) {
        MessagePlugin.success(res.msg);
      } else {
        MessagePlugin.error('操作失败：' + res.msg);
      }
      await fetchData();
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};
const fetchData = async () => {
  queryRef.value.loadData();
};
// 左侧角色菜单列表数据end

// vue的api
onMounted(async () => {});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
