<template>
  <div>
    <t-alert style="margin: 10px 0px 10px 0px" theme="info" message="系统参数工具类：SysParamUtil" />
    <div class="sp-role-left">
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
            url: '/center/sysparameter/list',
            method: 'get',
          }"
          :show-check="true"
        >
          <template #frSimpleBtn>
            <t-space size="6px">
              <t-button v-if="authAdd" @click="addRow">新增参数</t-button>
              <t-popconfirm content="是否确认立即刷新系统参数缓存?" @confirm="refCacheBtn" placement="top">
                <t-button v-if="authAdd" theme="default" :disabled="loadCache">刷新缓存</t-button>
              </t-popconfirm>
              <t-button variant="outline" theme="primary" @click="exportSql">导出SQL升级脚本</t-button>
            </t-space>
          </template>
          <template #frSimpleQuery>
            <t-input
              placeholder="请输入参数Key"
              type="search"
              clearable
              v-model="params.name"
              @clear="clearName"
              @enter="firstFetch"
              :style="{ width: '220px' }"
            ></t-input>
          </template>
          <template #operation="{ row }">
            <t-space size="4px">
              <t-button v-if="authEdit" size="small" variant="text" theme="primary" @click="editRow(row)"
                >修改</t-button
              >
              <t-button v-if="authDel" size="small" variant="text" theme="danger" @click="delRow(row)">删除</t-button>
            </t-space>
          </template>
        </FrQuery>
      </t-card>
    </div>
    <!-- 新增/修改参数 -->
    <t-dialog
      v-model:visible="visibleModal"
      width="400"
      :closeOnOverlayClick="false"
      :header="opt === 'add' ? '新增参数' : '修改参数'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form ref="form" :label-align="'right'" :data="tenantForm" :layout="'inline'" :rules="rules">
          <t-form-item label="参数说明" name="remark">
            <t-input v-model="tenantForm.remark" :style="{ width: '300px' }" placeholder="请输入参数说明"></t-input>
          </t-form-item>
          <t-form-item label="参数Key" name="name">
            <t-input
              :disabled="opt === 'edit'"
              v-model="tenantForm.name"
              :style="{ width: '300px' }"
              placeholder="请输入参数Key"
            ></t-input>
          </t-form-item>
          <t-form-item label="参数Value" name="value">
            <t-input v-model="tenantForm.value" :style="{ width: '300px' }" placeholder="请输入参数Value"></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListParameter',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, getCurrentInstance } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { add, edit, del, get, refCache } from '@/api/system/parameter';
import FrQuery from '@/components/fr-query/index.vue';
import { useUserStore } from '@/store';
import { exportSqlFile } from '@/utils/index';

const exportSql = () => {
  let selected = queryRef.value.getSelectData();
  if (selected.length === 0) {
    MessagePlugin.warning('请选择将要导出的数据');
    return;
  }
  exportSqlFile('parameter', selected.join(','));
};
const queryRef = ref(null);
//权限控制
const userStore = useUserStore();
const authAdd = computed(() => userStore.roles.includes('system:parameter:add'));
const authEdit = computed(() => userStore.roles.includes('system:parameter:edit'));
const authDel = computed(() => userStore.roles.includes('system:parameter:del'));

const firstFetch = async () => {
  queryRef.value.loadData(true);
};

//刷新缓存
const loadCache = ref(false);
const refCacheBtn = async () => {
  loadCache.value = true;
  let msg = MessagePlugin.loading('正在刷新中...');
  let res = await refCache();
  if (res.code === 0) {
    MessagePlugin.success('刷新成功');
  } else {
    MessagePlugin.error('刷新失败:' + res.msg);
  }
  MessagePlugin.close(msg);
  loadCache.value = false;
};

//新增/修改弹窗start
const visibleModal = ref(false);
const tenantForm = reactive({
  id: '',
  name: '',
  value: '',
  remark: '',
});
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});
const nameValidator = async (val) => {
  if (opt.value === 'add' && val) {
    let res = await get(val);
    if (res.data.length != 0) {
      return { result: false, message: '参数名不能重复', type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const rules = {
  name: [{ required: true, message: '请输入参数Key', type: 'error' }, { validator: nameValidator }],
  value: [{ required: true, message: '请输入参数Value', type: 'error' }],
  remark: [{ required: true, message: '请输入参数说明', type: 'error' }],
} as Rules;
const onSubmit = async () => {
  saveBtn.content = '保存中...';
  saveBtn.loading = true;
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    let submitForm = {
      name: tenantForm.name,
      value: tenantForm.value,
      remark: tenantForm.remark,
      id: null,
    };
    if (opt.value === 'add') {
      try {
        let result1 = await add(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          await fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      submitForm.id = tenantForm.id;
      try {
        let result1 = await edit(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  } else {
    saveBtn.content = '保存';
    saveBtn.loading = false;
  }
};
//新增/修改弹窗end

//左侧角色菜单列表数据start
const columns = [
  {
    width: 160,
    colKey: 'id',
    title: '主键ID',
  },
  {
    width: 140,
    colKey: 'remark',
    title: '参数说明',
    ellipsis: true,
  },
  {
    width: 140,
    colKey: 'name',
    title: '参数Key',
    ellipsis: true,
    align: 'center',
  },
  {
    width: 120,
    colKey: 'value',
    title: '参数Value',
    align: 'center',
  },
  {
    width: 200,
    colKey: 'createTime',
    title: '创建时间',
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 200,
    cell: 'operation',
    align: 'center',
    fixed: 'right',
  },
] as Columns;

const params = ref({
  name: '',
});

const clearName = async () => {
  queryRef.value.loadData(true);
};
const opt = ref('add');
const addRow = async () => {
  opt.value = 'add';
  form.value.reset();
  tenantForm.id = '';
  visibleModal.value = true;
};
const editRow = async (row) => {
  opt.value = 'edit';
  form.value.reset();
  tenantForm.id = row.id;
  tenantForm.name = row.name;
  tenantForm['value'] = row.value;
  tenantForm.remark = row.remark;
  visibleModal.value = true;
};
const delRow = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认删除(' + row.name + ')参数？',
    confirmBtn: '继续删除',
    //cancelBtn: '在考虑下',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      del(row.id)
        .then((res) => {
          if (res.code === 0) {
            fetchData();
            MessagePlugin.success('删除成功');
          } else {
            MessagePlugin.error('删除失败：' + res.msg);
          }
        })
        .catch((error) => {
          MessagePlugin.error('删除失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

const fetchData = async () => {
  queryRef.value.loadData();
};
//左侧角色菜单列表数据end

//vue的api
onMounted(() => {});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
