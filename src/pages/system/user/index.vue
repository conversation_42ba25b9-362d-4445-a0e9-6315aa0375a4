<template>
  <div>
    <div class="sp-role-left">
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
            url: '/center/user/list',
            method: 'get',
          }"
        >
          <template #frSimpleBtn>
            <t-button v-if="authAdd" @click="addRow">新增用户</t-button>
          </template>
          <template #frSimpleQuery>
            <t-select
              placeholder="用户状态"
              type="search"
              clearable
              :options="statusList"
              v-model="params.status"
              @clear="clearName(0)"
              @change="statusChange"
              style="width: 140px"
            ></t-select>
            <FrTenant
              placeholder="关联机构"
              v-model="params.tenantId"
              width="300px"
              @change="changeTenant"
              @clear="clearName(1)"
            />
            <t-cascader
              v-model="params.organ"
              :keys="{ value: 'id', label: 'name' }"
              :options="organList"
              placeholder="关联组织"
              check-strictly
              clearable
              style="width: 200px"
            />
            <t-input
              placeholder="用户昵称"
              type="search"
              clearable
              v-model="params.nickName"
              @clear="clearName(2)"
              @enter="firstFetch"
              style="width: 180px"
            ></t-input>
          </template>
          <template #phone="{ row }">
            {{ row.phone + '/' + row.email }}
          </template>
          <template #status="{ row }">
            <t-tag shape="round" :theme="row.status === '0' ? 'primary' : 'warning'" variant="light" size="small">{{
              statusMap[row.status]
            }}</t-tag>
          </template>
          <template #operation="{ row }">
            <t-space size="4px">
              <t-button size="small" variant="text" theme="primary" @click="viewRow(row)">详情</t-button>
              <t-button v-if="authEdit" size="small" variant="text" theme="primary" @click="editRow(row)"
                >修改</t-button
              >
              <t-button v-if="authDel" size="small" variant="text" theme="danger" @click="delRow(row)">删除</t-button>
              <t-dropdown :options="options" @click="clickHandler(row)" maxColumnWidth="100px">
                <t-button size="small" variant="text" theme="primary"
                  >更多
                  <template #suffix> <ChevronDownIcon size="16" /></template>
                </t-button>
              </t-dropdown>
            </t-space>
          </template>
        </FrQuery>
      </t-card>
    </div>
    <!-- 新增/修改用户 -->
    <t-dialog
      v-model:visible="visibleModal"
      width="900"
      :closeOnOverlayClick="false"
      :header="opt === 'add' ? '新增用户' : '修改用户'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form ref="form" :label-align="'top'" :data="userForm" :layout="'inline'" :rules="rules">
          <t-form-item label="关联机构" name="tenantId">
            <FrTenant ref="tenantRef" placeholder="请选择关联机构" v-model="userForm.tenantId" width="400px" />
          </t-form-item>
          <t-form-item label="关联组织" name="organ" :style="{ width: '800px' }">
            <t-cascader
              v-model="userForm.organ"
              :keys="{ value: 'id', label: 'name' }"
              :options="organList"
              placeholder="请选择关联组织"
              check-strictly
              clearable
            />
          </t-form-item>
          <t-form-item label="关联角色" name="roles">
            <t-select
              v-model="userForm.roles"
              placeholder="请选择关联角色"
              :options="roleD"
              multiple
              :style="{ width: '400px' }"
            />
          </t-form-item>
          <t-form-item label="登录账号" name="userName">
            <t-input v-model="userForm.userName" :style="{ width: '400px' }" placeholder="请输入登录账号"></t-input>
          </t-form-item>
          <t-form-item v-if="!userForm.id" label="登录密码" name="password">
            <t-input
              v-model="userForm.password"
              :style="{ width: '400px' }"
              type="password"
              placeholder="请输入登录密码"
            ></t-input>
          </t-form-item>
          <t-form-item label="用户昵称" name="nickName">
            <t-input v-model="userForm.nickName" :style="{ width: '400px' }" placeholder="请输入用户昵称"></t-input>
          </t-form-item>
          <t-form-item label="手机号" name="phone">
            <t-input v-model="userForm.phone" :style="{ width: '400px' }" placeholder="请输入手机号"></t-input>
          </t-form-item>
          <t-form-item label="电子邮箱" name="email">
            <t-input v-model="userForm.email" :style="{ width: '400px' }" placeholder="请输入电子邮箱"></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 查看详情 -->
    <t-dialog
      v-model:visible="visibleModal1"
      width="900"
      :closeOnOverlayClick="false"
      :header="'用户详情'"
      mode="modal"
      draggable
      :close-btn="null"
      :confirmBtn="null"
    >
      <template #body>
        <t-form ref="form1" :disabled="true" :label-align="'top'" :data="viewForm" :layout="'inline'">
          <t-form-item label="关联机构" name="tenantName">
            <t-input v-model="viewForm.tenantName" :style="{ width: '400px' }"></t-input>
          </t-form-item>
          <t-form-item label="关联组织" name="organ" :style="{ width: '800px' }">
            <t-cascader
              v-model="viewForm.organ"
              :keys="{ value: 'id', label: 'name' }"
              :options="organList"
              check-strictly
            />
          </t-form-item>
          <t-form-item label="关联角色" name="roles">
            <t-input v-model="viewForm.roles" :style="{ width: '400px' }"></t-input>
          </t-form-item>
          <t-form-item label="登录账号" name="userName">
            <t-input v-model="viewForm.userName" :style="{ width: '400px' }"></t-input>
          </t-form-item>
          <t-form-item label="用户昵称" name="nickName">
            <t-input v-model="viewForm.nickName" :style="{ width: '400px' }"></t-input>
          </t-form-item>
          <t-form-item label="手机号" name="phone">
            <t-input v-model="viewForm.phone" :style="{ width: '400px' }"></t-input>
          </t-form-item>
          <t-form-item label="电子邮箱" name="email">
            <t-input v-model="viewForm.email" :style="{ width: '400px' }"></t-input>
          </t-form-item>
          <t-form-item label="状态" name="email">
            <t-input v-model="statusMap[viewForm.status]" :style="{ width: '400px' }"></t-input>
          </t-form-item>
          <t-form-item label="最后登录时间" name="loginDate">
            <t-input v-model="viewForm.loginDate" :style="{ width: '400px' }"></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 修改密码 -->
    <t-dialog
      v-model:visible="visibleModal2"
      width="900"
      :closeOnOverlayClick="false"
      :header="'修改用户密码'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn2"
      :on-confirm="onSubmit2"
    >
      <template #body>
        <t-form ref="form2" :label-align="'top'" :data="passwordForm" :layout="'inline'" :rules="prules">
          <t-form-item label="用户" name="userName">
            <t-input v-model="passwordForm.userName" :style="{ width: '400px' }" :disabled="true"></t-input>
          </t-form-item>
          <t-form-item label="当前密码" name="password">
            <t-input
              v-model="passwordForm.password"
              :style="{ width: '400px' }"
              placeholder="请输入当前密码"
              type="password"
            ></t-input>
          </t-form-item>
          <t-form-item label="新密码" name="nPassword">
            <t-input
              v-model="passwordForm.nPassword"
              :style="{ width: '400px' }"
              type="password"
              placeholder="请输入新密码"
            ></t-input>
          </t-form-item>
          <t-form-item label="确认新密码" name="rnPassword">
            <t-input
              v-model="passwordForm.rnPassword"
              :style="{ width: '400px' }"
              placeholder="请输入确认新密码"
              type="password"
            ></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListTenant',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, nextTick } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { queryOrganTree } from '@/api/system/organ';
import {
  addUser,
  editUser,
  delUser,
  getUser,
  lockUser,
  unlockUser,
  resetPwd,
  updatePwd,
  checkPwd,
} from '@/api/system/user';
import { roleList } from '@/api/system/role';
import { dicVals } from '@/api/common';
import { useUserStore } from '@/store';
import FrTenant from '@/components/fr-tenant/index.vue';
import { ChevronDownIcon } from 'tdesign-icons-vue-next';
import FrQuery from '@/components/fr-query/index.vue';
import { sm2Encrypt } from '@/utils/encrypt';
import { password as objPwd } from '@/api/system/config';

const queryRef = ref(null);
//权限控制
const userStore = useUserStore();
const authAdd = computed(() => userStore.roles.includes('system:user:add'));
const authEdit = computed(() => userStore.roles.includes('system:user:edit'));
const authDel = computed(() => userStore.roles.includes('system:user:del'));

const firstFetch = async () => {
  queryRef.value.loadData(true);
};

//修改密码start
const visibleModal2 = ref(false);
const form2 = ref(null);
const passwordForm = ref({
  id: '',
  userName: '',
  password: '',
  nPassword: '',
  rnPassword: '',
});
const npwValidator = async (val) => {
  if (val) {
    let rep = new RegExp(pwdConfig.value.pwdComplexity);
    if (!rep.test(val)) {
      return { result: false, message: pwdConfig.value.pwdValidMsg, type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const pwValidator1 = async (val) => {
  if (val) {
    let res = await checkPwd({
      id: passwordForm.value.id,
      password: passwordForm.value.password,
    });
    if (res.code === 1) {
      return { result: false, message: '密码错误', type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const rnPasswordValidator = async (val) => {
  if (val) {
    if (val !== passwordForm.value.nPassword) {
      return { result: false, message: '两次输入的密码不一致', type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const prules = {
  password: [
    { required: true, message: '请输入当前密码', type: 'error', trigger: 'blur' },
    { validator: pwValidator1, trigger: 'blur' },
  ],
  nPassword: [{ required: true, message: '请输入新密码', type: 'error' }, { validator: npwValidator }],
  rnPassword: [{ required: true, message: '请输入确认新密码', type: 'error' }, { validator: rnPasswordValidator }],
} as Rules;
const saveBtn2 = reactive({
  content: '保存',
  loading: false,
});
const onSubmit2 = async () => {
  let result = await form2.value.validate();
  if (typeof result !== 'object' && result) {
    saveBtn2.content = '保存中...';
    saveBtn2.loading = true;
    let submitForm = {
      id: passwordForm.value.id,
      nPassword: '',
      password: '',
    };
    submitForm.nPassword = await sm2Encrypt(passwordForm.value.nPassword);
    submitForm.password = await sm2Encrypt(passwordForm.value.password);
    try {
      let result1 = await updatePwd(submitForm);
      if (result1.code === 0) {
        visibleModal2.value = false;
        await fetchData();
        MessagePlugin.success('密码修改成功');
      } else {
        MessagePlugin.error('密码修改失败' + result1.msg);
      }
    } catch (error) {
      MessagePlugin.error('密码修改失败');
    } finally {
      saveBtn2.content = '保存';
      saveBtn2.loading = false;
    }
  }
};
//修改密码end

const statusList = ref([]);
const statusMap = ref({});
const initDicts = async () => {
  let res = await dicVals('SP_USERSTATUS');
  statusList.value = res.data;
  res.data.forEach((row) => {
    statusMap.value[row.value] = row.label;
  });
};
const statusChange = async (val) => {
  if (val) {
    params.value.status = val;
    await fetchData();
  }
};
const changeTenant = async (val) => {
  if (val) {
    params.value.tenantId = val;
    await fetchData();
  }
};

const visibleModal1 = ref(false);
const viewForm = ref({
  tenantName: '',
  roles: '',
  userName: '',
  nickName: '',
  phone: '',
  email: '',
  status: '',
  loginDate: null,
  organ: '',
});
const viewRow = async (row) => {
  viewForm.value = row;
  visibleModal1.value = true;
};

const roleD = ref([]);
const initRoles = async () => {
  let res = await roleList({
    size: 500,
    current: 1,
  });
  res.data.records.forEach((row) => {
    roleD.value.push({
      label: row.name,
      value: row.id,
    });
  });
};
//机构选择动态加载end

const btnVal = ref('');
const options = [
  {
    content: '锁定',
    value: 1,
    onClick: () => {
      btnVal.value = '1';
    },
  },
  {
    content: '解锁',
    value: 2,
    onClick: () => {
      btnVal.value = '2';
    },
  },
  {
    content: '重置密码',
    value: 3,
    onClick: () => {
      btnVal.value = '3';
    },
  },
  {
    content: '修改密码',
    value: 4,
    onClick: () => {
      btnVal.value = '4';
    },
  },
  // {
  //   content: '注销',
  //   value: 3,
  // },
];

//新增/修改弹窗start
const visibleModal = ref(false);
const userForm = reactive({
  id: '',
  userName: '',
  password: '',
  nickName: '',
  phone: '',
  email: '',
  tenantId: '',
  roles: [],
  organ: '',
});
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});
const idValidator = async (val) => {
  if (val) {
    let res = await getUser({
      userName: val,
      id: userForm.id,
    });
    if (res.data.length != 0) {
      return { result: false, message: '用户账号不能重复', type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const phoneValidator = async (val) => {
  if (val) {
    let res = await getUser({
      phone: val,
      id: userForm.id,
    });
    if (res.data.length != 0) {
      return { result: false, message: '手机号不能重复', type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const emailValidator = async (val) => {
  if (val) {
    let res = await getUser({
      email: val,
      id: userForm.id,
    });
    if (res.data.length != 0) {
      return { result: false, message: '电子邮箱不能重复', type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const userNameValidator = async (val) => {
  if (val) {
    let rex = /^[a-z0-9_-]{5,16}$/;
    if (!rex.test(val)) {
      return { result: false, message: '输入5-16位且只能输入小写字母、数字或下划线', type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const pwValidator = async (val) => {
  if (val) {
    let rep = new RegExp(pwdConfig.value.pwdComplexity);
    if (!rep.test(val)) {
      return { result: false, message: pwdConfig.value.pwdValidMsg, type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const rules = {
  userName: [
    { required: true, message: '请输入用户账号', type: 'error' },
    { validator: idValidator },
    { validator: userNameValidator },
  ],
  phone: [
    { required: true, message: '请输入手机号', type: 'error' },
    { telnumber: true, message: '请输入正确的手机号码' },
    { validator: phoneValidator },
  ],
  email: [
    { required: true, message: '请输入机构邮箱', type: 'error' },
    { email: { ignore_max_length: true }, message: '请输入正确的邮箱地址' },
    { validator: emailValidator },
  ],
  password: [{ required: true, message: '请输入用户密码', type: 'error' }, { validator: pwValidator }],
  tenantId: [{ required: true, message: '请选择关联机构', type: 'error' }],
  organ: [{ required: true, message: '请选择关联组织', type: 'error' }],
  nickName: [{ required: true, message: '请输入用户昵称', type: 'error' }],
} as Rules;
const onSubmit = async () => {
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    saveBtn.content = '保存中...';
    saveBtn.loading = true;
    let submitForm = {
      userName: userForm.userName,
      phone: userForm.phone,
      email: userForm.email,
      nickName: userForm.nickName,
      tenantId: userForm.tenantId,
      roles: userForm.roles.join(','),
      password: null,
      organ: userForm.organ,
      id: null,
    };
    if (opt.value === 'add') {
      submitForm.password = userForm.password;
      try {
        let result1 = await addUser(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          await fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      submitForm.id = userForm.id;
      try {
        let result1 = await editUser(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          await fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  }
};
//新增/修改弹窗end

//左侧角色菜单列表数据start
const clickHandler = async (row) => {
  if (btnVal.value === '1') {
    await lockRow(row);
  } else if (btnVal.value === '2') {
    await unlockRow(row);
  } else if (btnVal.value === '3') {
    await resetRow(row);
  } else if (btnVal.value === '4') {
    form2.value.reset();
    passwordForm.value.id = row.id;
    passwordForm.value.userName = row.userName + '/' + row.nickName;
    visibleModal2.value = true;
  }
};
const data = ref([]);
const columns = [
  {
    width: 100,
    colKey: 'userName',
    title: '登录账号',
  },
  {
    width: 100,
    colKey: 'nickName',
    title: '用户昵称',
    ellipsis: true,
  },
  {
    width: 160,
    colKey: 'tenantName',
    title: '关联机构',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'roles',
    title: '关联角色',
  },
  {
    width: 80,
    colKey: 'status',
    title: '状态',
  },
  {
    width: 130,
    colKey: 'loginDate',
    title: '最后登录时间',
    ellipsis: true,
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 160,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;

const params = ref({
  nickName: '',
  status: '',
  tenantId: '',
  organ: '',
});

const clearName = async (index) => {
  if (index === 0) {
    params.value.status = '';
  } else if (index === 1) {
    params.value.tenantId = '';
  } else if (index === 2) {
    params.value.nickName = '';
  }
  await fetchData();
};
const opt = ref('add');
const tenantRef = ref(null);
const addRow = async () => {
  opt.value = 'add';
  visibleModal.value = true;
  nextTick(() => {
    form.value.reset();
    tenantRef.value.clearData();
    userForm.id = '';
  });
};
const editRow = async (row) => {
  opt.value = 'edit';
  visibleModal.value = true;
  nextTick(async () => {
    form.value.reset();
    userForm.id = row.id;
    userForm.userName = row.userName;
    userForm.nickName = row.nickName;
    userForm.phone = row.phone;
    userForm.email = row.email;
    userForm.roles = row.roleIds.split(',');
    userForm.organ = row.organ;
    await tenantRef.value.initData(row.tenantName, row.tenantId);
    //userForm.tenantId = row.tenantId;
  });
};
const delRow = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认删除(' + row.userName + '/' + row.nickName + ')用户？',
    confirmBtn: '继续删除',
    //cancelBtn: '在考虑下',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      delUser(row.id)
        .then((res) => {
          if (res.code === 0) {
            fetchData();
            MessagePlugin.success('删除成功');
          } else {
            MessagePlugin.error('删除失败：' + res.msg);
          }
        })
        .catch((error) => {
          MessagePlugin.error('删除失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

const lockRow = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认锁定(' + row.userName + '/' + row.nickName + ')用户？',
    confirmBtn: '继续锁定',
    //cancelBtn: '在考虑下',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      lockUser(row.id)
        .then((res) => {
          if (res.code === 0) {
            fetchData();
            MessagePlugin.success('锁定成功');
          } else {
            MessagePlugin.error('锁定失败：' + res.msg);
          }
        })
        .catch((error) => {
          MessagePlugin.error('锁定失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

const unlockRow = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认解锁(' + row.userName + '/' + row.nickName + ')用户？',
    confirmBtn: '继续解锁',
    //cancelBtn: '在考虑下',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      unlockUser(row.id)
        .then((res) => {
          if (res.code === 0) {
            fetchData();
            MessagePlugin.success('解锁成功');
          } else {
            MessagePlugin.error('解锁失败：' + res.msg);
          }
        })
        .catch((error) => {
          MessagePlugin.error('解锁失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

const resetRow = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认重置(' + row.userName + '/' + row.nickName + ')用户密码？',
    confirmBtn: '继续重置',
    //cancelBtn: '在考虑下',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      resetPwd(row.id)
        .then((res) => {
          if (res.code === 0) {
            let alertDia = DialogPlugin.alert({
              header: '重置成功',
              body: '新密码为：' + res.data,
              theme: 'success',
              confirmBtn: {
                content: '确定',
                variant: 'base',
                theme: 'primary',
              },
              onConfirm: ({ e }) => {
                alertDia.hide();
              },
              onClose: ({ e, trigger }) => {
                alertDia.hide();
              },
            });
            fetchData();
          } else {
            MessagePlugin.error('重置失败：' + res.msg);
          }
        })
        .catch((error) => {
          MessagePlugin.error('重置失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

const fetchData = async () => {
  queryRef.value.loadData();
};
//左侧角色菜单列表数据end

//初始化查询组织树形
const organList = ref([]);
const initOrganTree = async () => {
  let res = await queryOrganTree({});
  organList.value = res.data;
};
const pwdConfig = ref({
  pwdComplexity: '',
  pwdValidMsg: '',
});
const initSysConfig = async () => {
  let res = await objPwd();
  pwdConfig.value.pwdComplexity = res.data.pwdComplexity;
  pwdConfig.value.pwdValidMsg = res.data.pwdValidMsg;
};
//vue的api
onMounted(() => {
  initRoles();
  initDicts();
  initOrganTree();
  initSysConfig();
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
