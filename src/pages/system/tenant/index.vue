<template>
  <div>
    <div class="sp-role-left">
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :request="{
            url: '/center/tenant/list',
            method: 'get',
          }"
        >
          <template #frSimpleQuery>
            <t-input
              placeholder="租户名称"
              type="search"
              clearable
              v-model="params.name"
              @enter="firstFetch"
              :style="{ width: '220px' }"
            ></t-input>
          </template>
          <template #frSimpleBtn>
            <t-button v-if="authAdd" @click="addRow">新增租户</t-button>
          </template>
          <template #operation="{ row }">
            <t-space size="4px">
              <t-button v-if="authEdit" size="small" variant="text" theme="primary" @click="editRow(row)">
                修改</t-button
              >
              <t-button v-if="authDel" size="small" variant="text" theme="danger" @click="delRow(row)"> 删除 </t-button>
            </t-space>
          </template>
        </FrQuery>
      </t-card>
    </div>
    <!-- 新增/修改角色 -->
    <t-dialog
      v-model:visible="visibleModal"
      width="600"
      :closeOnOverlayClick="false"
      :header="opt === 'add' ? '新增租户' : '修改租户'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form ref="form" :label-align="'right'" :data="tenantForm" :layout="'inline'" :rules="rules">
          <t-form-item label="租户名称" name="name">
            <t-input v-model="tenantForm.name" :style="{ width: '400px' }" placeholder="请输入租户名称"></t-input>
          </t-form-item>
          <t-form-item label="租户电话" name="phone">
            <t-input v-model="tenantForm.phone" :style="{ width: '400px' }" placeholder="请输入租户电话"></t-input>
          </t-form-item>
          <t-form-item label="租户邮箱" name="email">
            <t-input v-model="tenantForm.email" :style="{ width: '400px' }" placeholder="请输入租户邮箱"></t-input>
          </t-form-item>
          <t-form-item label="租户地址" name="address">
            <t-input v-model="tenantForm.address" :style="{ width: '400px' }" placeholder="请输入租户地址"></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListTenant',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { addTenant, editTenant, delTenant, getTenant } from '@/api/system/tenant';
import { useUserStore } from '@/store';
import FrQuery from '@/components/fr-query/index.vue';

//权限控制
const userStore = useUserStore();
const authAdd = computed(() => userStore.roles.includes('system:tenant:add'));
const authEdit = computed(() => userStore.roles.includes('system:tenant:edit'));
const authDel = computed(() => userStore.roles.includes('system:tenant:del'));

const queryRef = ref(null);
const firstFetch = async () => {
  queryRef.value.loadData(true);
};

//新增/修改弹窗start
const visibleModal = ref(false);
const tenantForm = reactive({
  id: '',
  name: '',
  phone: '',
  email: '',
  address: '',
});
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});
const idValidator = async (val) => {
  if (opt.value === 'add' && val) {
    let res = await getTenant({
      name: val,
    });
    if (res.data.length != 0) {
      return { result: false, message: '租户名称不能重复', type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};
const rules = {
  name: [{ required: true, message: '请输入租户名称', type: 'error' }, { validator: idValidator }],
  phone: [
    { required: true, message: '请输入租户电话', type: 'error' },
    { telnumber: true, message: '请输入正确的手机号码' },
  ],
  email: [
    { required: true, message: '请输入租户邮箱', type: 'error' },
    { email: { ignore_max_length: true }, message: '请输入正确的邮箱地址' },
  ],
  address: [{ required: true, message: '请输入租户地址', type: 'error' }],
} as Rules;
const onSubmit = async () => {
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    saveBtn.content = '保存中...';
    saveBtn.loading = true;
    let submitForm = {
      name: tenantForm.name,
      phone: tenantForm.phone,
      email: tenantForm.email,
      address: tenantForm.address,
      id: null,
    };
    if (opt.value === 'add') {
      try {
        let result1 = await addTenant(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          await queryRef.value.loadData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      submitForm.id = tenantForm.id;
      try {
        let result1 = await editTenant(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          queryRef.value.loadData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  }
};
//新增/修改弹窗end

//左侧角色菜单列表数据start
const columns = [
  {
    width: 140,
    colKey: 'id',
    title: '租户ID',
  },
  {
    width: 220,
    colKey: 'name',
    title: '租户名称',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'phone',
    title: '电话',
  },
  {
    width: 180,
    colKey: 'email',
    title: '邮箱',
  },
  {
    width: 340,
    colKey: 'address',
    title: '地址',
    ellipsis: true,
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 100,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;
const params = ref({
  name: '',
});
const opt = ref('add');
const addRow = async () => {
  opt.value = 'add';
  form.value.reset();
  tenantForm.id = '';
  visibleModal.value = true;
};
const editRow = async (row) => {
  opt.value = 'edit';
  form.value.reset();
  tenantForm.id = row.id;
  tenantForm.name = row.name;
  tenantForm.phone = row.phone;
  tenantForm.email = row.email;
  tenantForm.address = row.address;
  visibleModal.value = true;
};
const delRow = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认删除(' + row.name + ')租户？',
    confirmBtn: '继续删除',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      delTenant(row.id)
        .then((res) => {
          if (res.code === 0) {
            queryRef.value.loadData();
            MessagePlugin.success('删除成功');
          } else {
            MessagePlugin.error('删除失败：' + res.msg);
          }
        })
        .catch((error) => {
          MessagePlugin.error('删除失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

//左侧角色菜单列表数据end

//vue的api
onMounted(async () => {});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
