<template>
  <div>
    <div class="secondary-notification">
      <t-tabs v-model="tabValue" @change="changeTabs">
        <t-tab-panel v-for="(tab, tabIndex) in TAB_LIST" :key="tabIndex" :value="tab.value" :label="tab.label">
          <t-loading size="small" :loading="loading" show-overlay text="加载中...">
            <t-list
              v-if="tableData.length > 0"
              class="secondary-msg-list"
              :async-loading="asyncLoading"
              split
              @load-more="loadData(1)"
            >
              <t-list-item v-for="(item, index) in tableData" :key="index">
                <t-list-item-meta>
                  <template #description>
                    <t-space>
                      <t-tag theme="primary" variant="light">{{ typeMap[item.type] }}</t-tag>
                      <span class="time_span">{{ item.createTime }}</span>
                      <!-- <span class="title_span">{{ item.title }}</span> -->
                      <t-link theme="primary" hover="color" @click="viewDeail(item)"> {{ item.title }} </t-link>
                    </t-space>
                  </template>
                </t-list-item-meta>

                <template #action>
                  <span>
                    <t-link
                      v-if="item.isRead === '1'"
                      @click="noReadMsg(item)"
                      theme="primary"
                      hover="color"
                      style="margin-left: 16px"
                      >设置未读</t-link
                    >
                    <t-link
                      v-if="item.isRead === '0'"
                      @click="readMsg(item)"
                      theme="primary"
                      hover="color"
                      style="margin-left: 16px"
                      >已读</t-link
                    >
                    <t-popconfirm content="是否删除该消息?" @confirm="delMsg(item)" placement="top">
                      <t-link theme="primary" hover="color" style="margin-left: 16px">删除</t-link>
                    </t-popconfirm>
                  </span>
                </template>
              </t-list-item>
            </t-list>
            <div v-else class="secondary-msg-list__empty-list">
              <empty-icon></empty-icon>
              <p>暂无通知</p>
            </div>
          </t-loading>
        </t-tab-panel>
      </t-tabs>
    </div>

    <t-dialog
      v-model:visible="visible"
      :confirm-btn="selectMsg.isRead === '1' ? '设置未读' : '已读'"
      cancel-btn="关闭"
      :on-confirm="onConfirm"
    >
      <template #header>
        <t-space>
          <span>{{ selectMsg.title }} </span>
        </t-space>
      </template>
      <template #body>
        <span class="time_span">{{ selectMsg.createTime }}</span>
        <div v-html="selectMsg.content"></div>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'DetailSecondary',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import EmptyIcon from '@/assets/assets-empty.svg?component';
import { useNotificationStore } from '@/store';
import { list as msgList, read, del, noRead } from '@/api/system/msg';
import { dicVals } from '@/api/common';

//初始化字典
const typeMap = ref({});
const initDicts = async () => {
  let res = await dicVals('SP_MSGTYPE');
  res.data.forEach((row) => {
    typeMap.value[row.value] = row.label;
  });
};
const visible = ref(false);
const TAB_LIST = [
  {
    label: '全部通知',
    value: '',
  },
  {
    label: '未读通知',
    value: '0',
  },
  {
    label: '已读通知',
    value: '1',
  },
];
const tabValue = ref('');
const store = useNotificationStore();
const pageInfo = ref({
  size: 10,
  current: 1,
});
const initPage = () => {
  pageInfo.value.size = 10;
  pageInfo.value.current = 1;
  tableData.value = [];
};
const tableData = ref([]);
const loading = ref(true);
const changeTabs = async () => {
  loading.value = true;
  initPage();
  await loadData(0);
};

const asyncLoadingRadio = ref('load-more');
const asyncLoading = computed(() => {
  if (asyncLoadingRadio.value === 'loading-custom') {
    return '没有更多数据了';
  }
  return asyncLoadingRadio.value;
});
const loadData = (value) => {
  asyncLoadingRadio.value = 'loading';
  msgList({
    ...pageInfo.value,
    isRead: tabValue.value,
  }).then((res) => {
    tableData.value = tableData.value.concat(res.data.records);
    if (res.data.records.length !== 0) {
      console.log(res.data.records.length < pageInfo.value.size);
      if (res.data.records.length < pageInfo.value.size) {
        asyncLoadingRadio.value = 'loading-custom';
      }
      if ((res.data.records.length = pageInfo.value.size)) {
        asyncLoadingRadio.value = 'load-more';
        pageInfo.value.current++;
      }
    } else {
      asyncLoadingRadio.value = 'loading-custom';
    }
    loading.value = false;
  });
};

const selectMsg = ref({
  id: '',
  title: '',
  createTime: '',
  isRead: '',
  content: '',
});
const viewDeail = (item) => {
  selectMsg.value = item;
  visible.value = true;
};
const onConfirm = () => {
  if (selectMsg.value.isRead === '1') {
    noReadMsg(selectMsg.value);
  } else {
    readMsg(selectMsg.value);
  }
  visible.value = false;
};
//未读/已读/删除
const readMsg = async (item) => {
  await read(item.id);
  changeTabs();
  store.initMsg();
};

const noReadMsg = async (item) => {
  await noRead(item.id);
  changeTabs();
  store.initMsg();
};

const delMsg = async (item) => {
  await del(item.id);
  changeTabs();
  store.initMsg();
};

onMounted(() => {
  initDicts();
  loadData('');
});
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

.secondary-notification {
  background-color: var(--tdvns-bg-color-container);
  border-radius: var(--tdvns-border-radius);
  padding: var(--tdvns-spacer-3) var(--tdvns-spacer-4);

  .t-tabs__content {
    padding-top: 0;
  }
}

.secondary-msg-list {
  height: 70vh;

  .t-list-item {
    cursor: pointer;
    padding: 13px 24px 13px 0;

    &:hover {
      background-color: var(--tdvns-bg-color-container-hover);

      .msg-date {
        display: none;
      }

      .msg-action {
        display: flex;
        align-items: center;

        &-icon {
          display: flex;
          align-items: center;
        }
      }
    }

    .t-tag.t-size-s {
      margin-right: var(--tdvns-spacer-1);
      margin-left: 0;
    }
  }

  .content {
    font-family: PingFangSC-Medium;
    font-size: 14px;
    color: var(--tdvns-text-color-placeholder);
    text-align: left;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .unread {
    color: var(--tdvns-brand-color-8);
  }

  .msg-action {
    display: none;

    .set-read-icon {
      margin-right: 24px;
    }
  }

  &__empty-list {
    min-height: 443px;
    padding-top: 170px;
    text-align: center;
    color: var(--tdvns-text-color-primary);
  }
}

&::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

&::-webkit-scrollbar-thumb {
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
  background-color: var(--td-scrollbar-color);
}

.time_span {
  color: var(--tdvns-text-color-secondary) !important;
  font-size: 12px;
}

.title_span {
  color: var(--tdvns-text-color-primary) !important;
}
</style>
