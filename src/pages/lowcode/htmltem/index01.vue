<template>
  <div class="container">
    <div class="sp-role-left">
      <t-card :bordered="false">
        <div class="sp-role-left-header">
          <t-row :gutter="10" justify="end">
            <t-col :span="12" class="search-header">
              <!-- <t-select
                  v-model="params.dataId"
                  :options="optionsData"
                  clearable
                  @change="statusChange"
                  @clear="clearName(0)"
                  placeholder="请选择数据源"
                  :style="{ width: '220px' }"
                /> -->
              <t-input
                placeholder="请输入数据库表名"
                type="search"
                clearable
                v-model="params.tableName"
                @clear="clearName(1)"
                @enter="firstFetch"
                :style="{ width: '220px' }"
              ></t-input>
              <t-button theme="primary" variant="outline" @click="firstFetch" :disabled="!params.dataId">查询</t-button>
              <!-- <t-button @click="visible = true">数据源配置</t-button> -->
            </t-col>
          </t-row>
        </div>
        <t-table
          row-key="tableName"
          :data="data"
          :max-height="'calc(98vh - 175px)'"
          :columns="columns"
          :loading="dataLoading"
          :loadingProps="{ size: '23px', text: '加载中...' }"
        >
          <template #operation="{ row }">
            <t-button size="small" variant="text" theme="primary" @click="initCfg(row)">配置</t-button>
            <t-button size="small" variant="text" theme="primary" :disabled="!row.id" @click="createCode(row)"
              >生成代码</t-button
            >
            <t-button size="small" variant="text" theme="primary" :disabled="!row.id" @click="createCodeStr(row)"
              ><template #icon><BrowseIcon /></template>预览代码</t-button
            >
          </template>
        </t-table>
      </t-card>
    </div>

    <t-dialog
      v-model:visible="visibleModal1"
      width="1000"
      :closeOnOverlayClick="false"
      :header="'预览代码'"
      mode="modal"
      draggable
      :cancelBtn="null"
      :confirmBtn="null"
      :top="30"
    >
      <t-tabs
        :value="tabSelect"
        @change="
          (value) => {
            tabSelect = value;
          }
        "
        placement="left"
      >
        <t-tab-panel v-for="(item, index) in viewList" :value="index" :label="item.fileName">
          <p style="margin: 20px">
            <codemirror
              :ref="'editor_' + index"
              v-model="item.fileContent"
              :style="{ height: '600px' }"
              :autofocus="true"
              :indent-with-tab="true"
              :tabSize="2"
              :extensions="cmOptions"
            />
          </p>
        </t-tab-panel>
      </t-tabs>
    </t-dialog>

    <!-- 数据源配置左侧弹框 -->
    <t-drawer
      v-model:visible="visible"
      header="数据源配置"
      :size="'800px'"
      :close-btn="true"
      :footer="false"
      :close-on-overlay-click="false"
    >
      <p style="margin-bottom: 20px"><t-button theme="primary" @click="addDs">新增数据源</t-button></p>
      <t-table row-key="id" :data="data1" :columns="columns1" bordered :loading="dataLoading1">
        <template #operation="{ row }">
          <t-button size="small" variant="outline" theme="danger" @click="delDs(row)">删除</t-button>
        </template>
      </t-table>
    </t-drawer>

    <!-- 新增数据源 -->
    <t-dialog
      v-model:visible="visibleModal2"
      width="450"
      :close-on-overlay-click="false"
      :header="'新增数据源'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn1"
      :on-confirm="onSubmit1"
    >
      <template #body>
        <t-form ref="form1" :label-align="'top'" :data="cfgForm1" :layout="'inline'" :rules="rules1">
          <t-form-item label="数据源名称" name="name">
            <t-input v-model="cfgForm1.name" :style="{ width: '400px' }" placeholder="请输入数据源名称"></t-input>
          </t-form-item>
          <t-form-item label="链接地址" name="url">
            <t-textarea
              v-model="cfgForm1.url"
              :style="{ width: '400px' }"
              :autosize="{ minRows: 5, maxRows: 5 }"
              placeholder="请输入链接地址"
            ></t-textarea>
          </t-form-item>
          <t-form-item label="用户名" name="user">
            <t-input v-model="cfgForm1.user" :style="{ width: '400px' }" placeholder="请输入用户名"></t-input>
          </t-form-item>
          <t-form-item label="密码" name="pwd">
            <t-input
              v-model="cfgForm1.pwd"
              type="password"
              :style="{ width: '400px' }"
              placeholder="请输入密码"
            ></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 修改配置项 -->
    <t-dialog
      v-model:visible="visibleModal"
      :top="30"
      width="1050"
      :closeOnOverlayClick="false"
      :header="'修改配置'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form ref="form" :label-align="'right'" :data="cfgForm" :layout="'inline'" :rules="rules">
          <t-form-item label="表名" name="tableName">
            <t-input
              v-model="cfgForm.tableName"
              :style="{ width: '400px' }"
              placeholder="请输入表名"
              :disabled="true"
            ></t-input>
          </t-form-item>
          <t-form-item label="所属模块" name="modeDesc">
            <t-input v-model="cfgForm.modeDesc" :style="{ width: '400px' }" placeholder="接口中文注解"></t-input>
          </t-form-item>
          <t-form-item label="表前缀" name="fix">
            <t-input
              v-model="cfgForm.fix"
              :style="{ width: '400px' }"
              placeholder="请输入表前缀，生成自动去除"
            ></t-input>
          </t-form-item>
          <t-form-item label="包路径" name="pkg">
            <t-input-adornment prepend="org.simple.">
              <t-input v-model="cfgForm.pkg" :style="{ width: '190px' }" placeholder="请输入包路径"></t-input>
            </t-input-adornment>
          </t-form-item>
          <t-form-item label="注释作者" name="auth">
            <t-input v-model="cfgForm.auth" :style="{ width: '400px' }" placeholder="请输入注释作者"></t-input>
          </t-form-item>
        </t-form>
        <div style="padding-top: 20px; padding-left: 10px">
          <t-radio-group variant="default-filled" v-model="selectModel">
            <t-radio-button :value="'1'">列表-查询条件</t-radio-button>
            <t-radio-button :value="'2'">列表-显示列表</t-radio-button>
            <t-radio-button :value="'3'">表单字段</t-radio-button>
          </t-radio-group>
        </div>
        <div v-if="selectModel === '1'" style="padding-top: 10px; padding-left: 10px">
          <t-table row-key="key" :columns="columns2" :data="data2" table-layout="auto" bordered>
            <template #isUse="{ row }">
              <t-select v-model="row.isUse" :style="{ width: '100px' }">
                <t-option key="0" label="不显示" value="0" />
                <t-option key="1" label="显示" value="1" />
              </t-select>
            </template>
            <template #columnName="{ row }">
              {{ row.columnName + '（' + row.dataType + '）' }}
            </template>
            <template #columnComment="{ row }">
              <t-input v-model="row.columnComment" placeholder="请输入显示名称" :style="{ width: '180px' }"></t-input>
            </template>
            <template #type="{ row }">
              <t-select v-model="row.type" :style="{ width: '200px' }" placeholder="请选择显示类型" label="类型：">
                <t-option key="input" label="输入框" value="input" />
                <t-option key="select" label="下拉框" value="select" />
                <t-option key="date" label="日期" value="date" />
              </t-select>

              <t-select
                v-if="row.type === 'select'"
                v-model="row.typeVal"
                :style="{ width: '200px', marginTop: '5px' }"
                placeholder="请选择字典"
                label="字典："
              >
                <t-option v-for="(item, index) in dictList" :key="index" :label="item.label" :value="item.code" />
              </t-select>
            </template>
          </t-table>
        </div>
        <div v-if="selectModel === '2'" style="padding-top: 10px; padding-left: 10px">
          <t-table row-key="key" :columns="columns3" :data="data3" table-layout="auto" bordered>
            <template #isUse="{ row }">
              <t-select v-model="row.isUse" :style="{ width: '100px' }">
                <t-option key="0" label="不显示" value="0" />
                <t-option key="1" label="显示" value="1" />
              </t-select>
            </template>
            <template #columnName="{ row }">
              {{ row.columnName + '（' + row.dataType + '）' }}
            </template>
            <template #columnComment="{ row }">
              <t-input v-model="row.columnComment" placeholder="请输入显示名称" :style="{ width: '180px' }"></t-input>
            </template>
            <template #align="{ row }">
              <t-select v-model="row.align" :style="{ width: '200px' }" placeholder="请选择显示类型">
                <t-option key="left" label="左对齐" value="left" />
                <t-option key="center" label="居中" value="center" />
                <t-option key="right" label="右对齐" value="right" />
              </t-select>
            </template>
          </t-table>
        </div>
        <div v-if="selectModel === '3'" style="padding-top: 10px; padding-left: 10px">
          <t-table row-key="key" :columns="columns4" :data="data4" table-layout="auto" bordered>
            <template #isUse="{ row }">
              <t-select v-model="row.isUse" :style="{ width: '100px' }">
                <t-option key="0" label="不显示" value="0" />
                <t-option key="1" label="显示" value="1" />
              </t-select>
            </template>
            <template #columnName="{ row }">
              {{ row.columnName + '（' + row.dataType + '）' }}
            </template>
            <template #columnComment="{ row }">
              <t-input v-model="row.columnComment" placeholder="请输入显示名称" :style="{ width: '180px' }"></t-input>
            </template>
            <template #type="{ row }">
              <t-select v-model="row.type" :style="{ width: '200px' }" placeholder="请选择显示类型" label="类型：">
                <t-option key="input" label="输入框" value="input" />
                <t-option key="select" label="下拉框" value="select" />
                <t-option key="date" label="日期" value="date" />
              </t-select>

              <t-select
                v-if="row.type === 'select'"
                v-model="row.typeVal"
                :style="{ width: '200px', marginTop: '5px' }"
                placeholder="请选择字典"
                label="字典："
              >
                <t-option v-for="(item, index) in dictList" :key="index" :label="item.label" :value="item.code" />
              </t-select>
            </template>
          </t-table>
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListBase',
};
</script>

<script setup lang="ts">
import { ref, onMounted, reactive, getCurrentInstance } from 'vue';
import { MessagePlugin, DialogPlugin, Input, Select } from 'tdesign-vue-next';
import { BrowseIcon } from 'tdesign-icons-vue-next';

import {
  queryDs,
  queryTableList,
  updateTableCfg,
  codeCreate,
  codeCreateToStr,
  addDataSource,
  editDataSource,
  delDataSource,
  checkDataSource,
  queryColumnList,
} from '@/api/lowcode/code';
import { listDict } from '@/api/system/dict';
import { Codemirror } from 'vue-codemirror';
import { oneDark } from '@codemirror/theme-one-dark';
import { java } from '@codemirror/lang-java';

//列表编辑
const selectModel = ref('1');
const data2 = ref([]);
const columns2 = ref<Columns>([
  {
    width: 80,
    colKey: 'isUse',
    title: '是否显示',
    ellipsis: true,
  },
  {
    width: 80,
    colKey: 'columnName',
    title: '字段名（字段类型）',
    ellipsis: true,
  },
  // {
  //   width: 100,
  //   colKey: 'dataType',
  //   title: '字段类型',
  //   ellipsis: true,
  // },
  {
    width: 120,
    colKey: 'columnComment',
    title: '显示名称',
    ellipsis: true,
  },
  {
    width: 180,
    colKey: 'type',
    title: '显示类型',
    ellipsis: true,
  },
]);

const data3 = ref([]);
const columns3 = ref<Columns>([
  {
    width: 80,
    colKey: 'isUse',
    title: '是否显示',
    ellipsis: true,
  },
  {
    width: 80,
    colKey: 'columnName',
    title: '字段名（字段类型）',
    ellipsis: true,
  },
  // {
  //   width: 100,
  //   colKey: 'dataType',
  //   title: '字段类型',
  //   ellipsis: true,
  // },
  {
    width: 120,
    colKey: 'columnComment',
    title: '显示名称',
    ellipsis: true,
  },
  {
    width: 180,
    colKey: 'align',
    title: '对齐方式',
    ellipsis: true,
  },
]);

const data4 = ref([]);
const columns4 = ref<Columns>([
  {
    width: 80,
    colKey: 'isUse',
    title: '是否显示',
    ellipsis: true,
  },
  {
    width: 80,
    colKey: 'columnName',
    title: '字段名（字段类型）',
    ellipsis: true,
  },
  // {
  //   width: 100,
  //   colKey: 'dataType',
  //   title: '字段类型',
  //   ellipsis: true,
  // },
  {
    width: 120,
    colKey: 'columnComment',
    title: '显示名称',
    ellipsis: true,
  },
  {
    width: 180,
    colKey: 'type',
    title: '显示类型',
    ellipsis: true,
  },
]);

//预览代码
const visibleModal1 = ref(false);
const tabSelect = ref<any>(0);
const viewList = ref([]);
const cmOptions = [java(), oneDark];
const createCodeStr = async (item) => {
  visibleModal1.value = true;
  let res = await codeCreateToStr({
    id: item.id,
    tableComment: item.tableComment,
  });
  viewList.value = res.data;
  tabSelect.value = 0;
};

//生成代码
const createCode = async (row) => {
  let res = await codeCreate({
    id: row.id,
    tableComment: row.tableComment,
  });
  let blob = new Blob([res], { type: 'application/zip' });
  let filename = row.tableName + '.zip';
  let link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
};

//数据源配置
const visibleModal2 = ref(false);
const visible = ref(false);
const data1 = ref([]);
const dataLoading1 = ref(false);
const columns1 = [
  {
    width: 180,
    colKey: 'name',
    title: '数据源名称',
    ellipsis: true,
  },
  {
    width: 300,
    colKey: 'url',
    title: '链接地址',
    ellipsis: true,
  },
  {
    width: 80,
    colKey: 'user',
    title: '用户名',
    ellipsis: true,
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 80,
    align: 'center',
    cell: 'operation',
    fixed: 'right',
  },
] as Columns;
const cfgDs = async () => {
  visible.value = true;
  await initDataSource();
};
const addDs = () => {
  form1.value.reset();
  visibleModal2.value = true;
};
const form1 = ref(null);
const saveBtn1 = reactive({
  content: '保存',
  loading: false,
});
const rules1 = {
  name: [{ required: true, message: '请输入数据源名称', type: 'error' }],
  url: [{ required: true, message: '请输入链接地址', type: 'error' }],
  user: [{ required: true, message: '请输入用户名', type: 'error' }],
  pwd: [{ required: true, message: '请输入密码', type: 'error' }],
} as Rules;
const cfgForm1 = reactive({
  name: '',
  url: '',
  user: '',
  pwd: '',
});
const onSubmit1 = async () => {
  const result = await form1.value.validate();
  if (typeof result !== 'object' && result) {
    saveBtn1.content = '保存中...';
    saveBtn1.loading = true;
    const submitForm = {
      name: cfgForm1.name,
      url: cfgForm1.url,
      user: cfgForm1.user,
      pwd: cfgForm1.pwd,
    };
    try {
      const res = await addDataSource(submitForm);
      if (res.code === 0) {
        visibleModal2.value = false;
        MessagePlugin.success('保存成功');
        await initDataSource();
      } else {
        MessagePlugin.error('保存失败：' + res.msg);
      }
    } catch (error) {
      MessagePlugin.error(error.message);
    } finally {
      saveBtn1.content = '保存';
      saveBtn1.loading = false;
    }
  }
};

const delDs = (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: `是否确定删除数据源(${row.name})？`,
    confirmBtn: '继续删除',
    // cancelBtn: '在考虑下',
    onConfirm: ({ e }) => {
      confirmDia.hide();
      delDataSource(row.id)
        .then((res) => {
          initDataSource();
          MessagePlugin.success(res.message);
        })
        .catch((error) => {
          MessagePlugin.error('删除失败');
        });
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};
// 数据源配置end

//修改配置项start
const columnList = ref([]);
const initCfg = async (row) => {
  form.value.reset();
  visibleModal.value = true;
  let res = await queryColumnList({
    data: params.dataId,
    table: row.tableName,
  });
  columnList.value = res.data;
  if (row.id) {
    cfgForm.id = row.id;
    cfgForm.tableName = row.tableName;
    cfgForm.fix = row.fix;
    cfgForm.pkg = row.pkg.replace('org.simple.', '');
    cfgForm.auth = row.auth;
    cfgForm.modeDesc = row.modeDesc;
    data2.value = [];
    data3.value = [];
    data4.value = [];
    if (!row.reqJson) {
      data2.value = JSON.parse(JSON.stringify(columnList.value)); //查询条件
    } else {
      columnList.value.map((obj) => {
        let curObj = JSON.parse(JSON.stringify(obj));
        row.reqJson.map((row1) => {
          if (curObj.columnName === row1.columnName) {
            curObj.isUse = row1.isUse;
            curObj.columnComment = row1.columnComment;
            curObj.type = row1.type;
            curObj.typeVal = row1.typeVal;
          }
        });
        data2.value.push(curObj);
      });
    }

    if (!row.outJson) {
      data3.value = JSON.parse(JSON.stringify(columnList.value)); //查询列表
    } else {
      columnList.value.map((obj) => {
        row.outJson.map((row1) => {
          if (obj.columnName === row1.columnName) {
            obj.isUse = row1.isUse;
            obj.columnComment = row1.columnComment;
            obj.align = row1.align;
          }
        });
        data3.value.push(obj);
      });
    }

    if (!row.formJson) {
      data4.value = JSON.parse(JSON.stringify(columnList.value)); //表单对象
    } else {
      columnList.value.map((obj) => {
        row.formJson.map((row1) => {
          if (obj.columnName === row1.columnName) {
            obj.isUse = row1.isUse;
            obj.columnComment = row1.columnComment;
            obj.type = row1.type;
            obj.typeVal = row1.typeVal;
          }
        });
        data4.value.push(obj);
      });
    }
  } else {
    cfgForm.tableName = row.tableName;
    data2.value = JSON.parse(JSON.stringify(columnList.value)); //查询条件
    data3.value = JSON.parse(JSON.stringify(columnList.value)); //查询列表
    data4.value = JSON.parse(JSON.stringify(columnList.value)); //表单对象
  }
};
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});
const rules = {
  auth: [{ required: true, message: '请输入注释作者', type: 'error' }],
  modeDesc: [{ required: true, message: '请输入所属模块', type: 'error' }],
  pkg: [{ required: true, message: '请输入包路径', type: 'error' }],
} as Rules;
const cfgForm = reactive({
  id: '',
  tableName: '',
  fix: '',
  pkg: '',
  auth: '',
  modeDesc: '',
});
const visibleModal = ref(false);
const onSubmit = async () => {
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    //校验必填
    for (let i = 0; i < data2.value.length; i++) {
      let row = data2.value[i];
      if (!row.isUse || !row.columnComment || !row.type || (row.type === 'select' && !row.typeVal)) {
        MessagePlugin.error('<列表-查询条件>必填项为填写');
        return;
      }
    }
    for (let i = 0; i < data3.value.length; i++) {
      let row = data3.value[i];
      if (!row.isUse || !row.columnComment || !row.align) {
        MessagePlugin.error('<列表-显示列表>必填项为填写');
        return;
      }
    }
    for (let i = 0; i < data4.value.length; i++) {
      let row = data4.value[i];
      if (!row.isUse || !row.columnComment || !row.type || (row.type === 'select' && !row.typeVal)) {
        MessagePlugin.error('<表单字段>必填项为填写');
        return;
      }
    }
    saveBtn.content = '保存中...';
    saveBtn.loading = true;
    let submitForm = {
      id: cfgForm.id,
      tableName: cfgForm.tableName,
      fix: cfgForm.fix,
      pkg: 'org.simple.' + cfgForm.pkg,
      auth: cfgForm.auth,
      dataSource: params.dataId,
      modeDesc: cfgForm.modeDesc,
      reqJson: data2.value,
      outJson: data3.value,
      formJson: data4.value,
    };
    try {
      let result1 = await updateTableCfg(submitForm);
      if (result1.code === 0) {
        visibleModal.value = false;
        MessagePlugin.success('保存成功');
        await fetchData();
      } else {
        MessagePlugin.error('保存失败：' + result1.msg);
      }
    } catch (error) {
      MessagePlugin.error('保存失败');
    } finally {
      saveBtn.content = '保存';
      saveBtn.loading = false;
    }
  }
};
//修改配置项end

const firstFetch = async () => {
  pagination.value.current = 1;
  await fetchData();
};

const optionsData = ref([]);
//左侧角色菜单列表数据start
const data = ref([]);
const columns = [
  {
    width: 180,
    colKey: 'tableComment',
    title: '表中文名称',
    ellipsis: true,
  },
  {
    width: 150,
    colKey: 'tableName',
    title: '表英文名称',
    ellipsis: true,
  },
  {
    width: 130,
    colKey: 'tableCollation',
    title: '排序规则',
    ellipsis: true,
  },
  {
    width: 120,
    colKey: 'createTime',
    title: '创建时间',
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 220,
    align: 'center',
    cell: 'operation',
    fixed: 'right',
  },
] as Columns;
const dataLoading = ref(false);
const pagination = ref({
  pageSize: 20,
  total: 0,
  current: 1,
});
const params = reactive({
  dataId: '',
  tableName: '',
});
const statusChange = async (value) => {
  params.dataId = value;
  if (params.dataId) {
    await fetchData();
  } else {
    data.value = [];
  }
};
const clearName = async (index) => {
  if (index === 0) {
    params.dataId = '';
  } else if (index === 1) {
    params.tableName = '';
  }
  //pagination.value.current = 1;
  if (!params.dataId) {
    data.value = [];
  } else {
    await fetchData();
  }
};
const fetchData = async () => {
  data.value = [];
  dataLoading.value = true;
  try {
    let res = await queryTableList({
      dataId: params.dataId,
      tableName: params.tableName,
    });
    if (res.code === 0) {
      data.value = res.data;
      //pagination.value.total = res.data.total;
    }
  } catch (er) {
  } finally {
    dataLoading.value = false;
  }
};
//左侧角色菜单列表数据end

//vue的api
onMounted(() => {
  //await fetchData();
  initDataSource();
  initDict();
});

//初始化字典
const dictList = ref([]);
const initDict = async () => {
  let res = await listDict({
    size: 5000,
  });
  dictList.value = res.data.records;
};

//初始化数据源
const initDataSource = async () => {
  optionsData.value = [];
  data1.value = [];
  dataLoading1.value = true;
  let res = await queryDs({});
  res.data.forEach((row) => {
    data1.value.push(row);
    optionsData.value.push({
      value: row.id,
      label: row.name,
    });
  });
  dataLoading1.value = false;
};

const initDataList = (dataId) => {
  statusChange(dataId);
};

defineExpose({
  initDataList,
});
</script>
<style lang="less" scoped>
@import '@/style/variables';
.t-tag {
  cursor: pointer;
}
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
.container {
  position: relative;
}
</style>
