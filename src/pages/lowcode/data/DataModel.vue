<template>
  <div>
    <t-row :gutter="10">
      <t-col :span="3">
        <div class="sp-menu-tree">
          <t-card class="leftCard" :bordered="false">
            <template #header>
              <t-space direction="vertical" style="width: 100%">
                <div style="width: 100%; display: flex; gap: 10px">
                  <t-button block variant="outline" theme="primary" @click="addTypeBtn">新增分类</t-button>
                  <t-button @click="showExportMenu1" block variant="outline" theme="default">导出SQL</t-button>
                </div>
                <t-input v-model="demo1Text" placeholder="请输入分类名称模糊检索" @change="demo1Input" />
              </t-space>
            </template>
            <t-loading size="small" :loading="menuLoad" show-overlay>
              <t-tree
                ref="menuTree"
                :data="items"
                activable
                hover
                :icon="true"
                transition
                :keys="{ value: 'id', label: 'name' }"
                :expandAll="true"
                :expand-on-click-node="false"
                v-model:actived="activeNode"
                :filter="demo1Filter"
                @active="activeChange"
                :style="{ height: 'calc(100vh - 270px)', overflow: 'auto' }"
              >
                <template #label="{ node }">
                  <div style="text-align: left">
                    {{ node.data.name }}
                  </div>
                </template>
                <template #operations="{ node }">
                  <t-dropdown v-if="selectNode.id === node.data.id" :options="getOptions(node.data)" trigger="click">
                    <t-space>
                      <t-button variant="text" theme="default" size="small">
                        <template #icon> <fr-icon name="menu-application" size="16" /></template>
                      </t-button>
                    </t-space>
                  </t-dropdown>
                </template>
              </t-tree>
            </t-loading>
          </t-card>
        </div>
      </t-col>
      <t-col :span="9">
        <t-row>
          <t-col :span="12">
            <t-card :bordered="false" :style="{ borderRadius: '8px' }">
              <FrQuery
                ref="queryRef"
                v-model:params="params"
                :columns="columns"
                :request="{
                  url: '/lowcode/model/list',
                  method: 'post',
                }"
                :orders="[
                  {
                    asc: false,
                    column: 'update_time',
                  },
                ]"
                :is-load="false"
                :showCheck="true"
              >
                <template #frSimpleQuery>
                  <t-input
                    placeholder="模型名称"
                    type="search"
                    clearable
                    v-model="params.name"
                    @enter="firstFetch"
                    :style="{ width: '220px' }"
                  ></t-input>
                </template>
                <template #frSimpleBtn>
                  <t-space size="8px">
                    <t-button v-if="authAdd" :disabled="!selectNode.id" @click="addForm">新建模型</t-button>
                    <t-popconfirm content="是否确认刷新缓存？" placement="bottom" @confirm="handleRefCache">
                      <t-button theme="default" variant="outline">刷新缓存</t-button>
                    </t-popconfirm>
                    <t-button @click="showExportMenu" theme="default">导出SQL升级脚本</t-button>
                  </t-space>
                </template>
                <template #outType="{ row }">
                  {{ dict['SP_DATASOURCE_MODELRESULT'].map.get(row.outType) }}
                </template>
                <template #isPublic="{ row }">
                  {{ row.isPublic === '1' ? '是' : '否' }}
                </template>
                <template #operation="{ row }">
                  <t-space size="4px">
                    <t-button v-if="authEdit" size="small" variant="text" theme="primary" @click="editForm(row)">
                      修改</t-button
                    >
                    <t-popconfirm v-if="authDel" content="是否确认删除?" @confirm="delForm(row)">
                      <t-button size="small" variant="text" theme="danger"> 删除 </t-button>
                    </t-popconfirm>
                    <t-dropdown :min-column-width="88">
                      <t-button variant="text" size="small" theme="primary"
                        >更多
                        <template #suffix>
                          <FrIcon name="chevron-down" />
                        </template>
                      </t-button>
                      <t-dropdown-menu>
                        <t-dropdown-item :value="2" @click="copyForm(row)"> 复制模型 </t-dropdown-item>
                        <t-dropdown-item :value="1" @click="openTest(row)"> 预览页面 </t-dropdown-item>
                      </t-dropdown-menu>
                    </t-dropdown>
                  </t-space>
                </template>
              </FrQuery>
            </t-card>
          </t-col>
        </t-row>
      </t-col>
    </t-row>
    <!-- 新增/修改 -->
    <t-drawer
      v-model:visible="visibleModal"
      :destroyOnClose="true"
      :size-draggable="true"
      :closeOnOverlayClick="false"
      :header="
        !menuForm.id ? '新建模型(所属分类：' + selectNode.name + ')' : '修改模型(所属分类：' + selectNode.name + ')'
      "
      size="90%"
    >
      <template #footer>
        <t-space :size="5" class="flex justify-start">
          <t-button @click="visibleModal = false" theme="default">关闭</t-button>
          <t-button theme="primary" :loading="saveBtn.loading" @click="onSubmit">
            {{ saveBtn.content }}
          </t-button>
        </t-space>
      </template>
      <template #body>
        <div v-loading="pageLoad">
          <t-tabs v-model:value="tabVal">
            <template #action>
              <div v-if="tabVal !== -1 && tabVal !== 2" style="padding-top: 10px; padding-right: 10px">
                <t-button size="small" @click="addDataRow">添加参数</t-button>
              </div>
            </template>
            <t-tab-panel :value="-1" label="基本信息" :destroyOnHide="false">
              <div style="padding: 20px">
                <t-form
                  ref="form"
                  :label-width="80"
                  :label-align="'left'"
                  :data="menuForm"
                  :layout="'inline'"
                  :rules="rules"
                >
                  <t-form-item label="名称" name="name">
                    <t-input v-model="menuForm.name" :style="{ width: '50vw' }" placeholder="请输入名称"></t-input>
                  </t-form-item>
                  <!-- <t-form-item label="类型" name="type" :style="{ width: '605px' }">
                    <t-radio-group
                      v-model:value="menuForm.type"
                      name="type"
                      :options="dict['SP_DATASOURCE_MODELTYPE']?.list"
                    ></t-radio-group>
                  </t-form-item> -->

                  <t-form-item v-if="menuForm.type === '02'" label="数据源" name="dataSource">
                    <FrData
                      ref="frDataRef"
                      v-model:model-value="menuForm.dataSource"
                      url="/lowcode/code/queryDs"
                      method="post"
                      query-key="dataName"
                      value-key="id"
                      label-key="dataName"
                      ext-label-key="appName"
                      description-key="name"
                      searchPlaceholder="请输入数据源名称进行模糊搜索"
                      placeholder="请选择关联数据源"
                      width="350px"
                    />
                  </t-form-item>
                  <t-form-item v-if="menuForm.type === '02'" label="Sql脚本" name="sqlText">
                    <Codemirror
                      v-model="menuForm.sqlText"
                      :style="{ height: '350px', width: '50vw' }"
                      :autofocus="false"
                      :indent-with-tab="true"
                      :tabSize="2"
                      :extensions="cmOptions"
                    />
                  </t-form-item>
                  <t-form-item v-if="menuForm.type === '02'" label="排序" name="sort">
                    <t-input-adornment prepend="order by">
                      <t-input
                        v-model="menuForm.sort"
                        placeholder="请输入排序字段，多个逗号隔开"
                        :style="{ width: '605px' }"
                      />
                    </t-input-adornment>
                  </t-form-item>
                  <t-form-item v-if="menuForm.type === '01'" label="请求链接" name="url">
                    <t-input v-model="menuForm.url" :style="{ width: '605px' }" placeholder="请输入请求链接"></t-input>
                  </t-form-item>
                  <t-form-item label="输出类型" name="outType" :style="{ width: '80vw' }">
                    <t-radio-group v-model:value="menuForm.outType" :options="dict['SP_DATASOURCE_MODELRESULT']?.list">
                    </t-radio-group>
                  </t-form-item>
                  <t-form-item label="关联权限" name="isPublic">
                    <t-switch v-model="menuForm.isPublic">
                      <template #label="slotProps">{{ slotProps.value ? '是' : '否' }}</template>
                    </t-switch>
                  </t-form-item>
                  <t-form-item v-if="menuForm.isPublic" label="选择权限" name="roleGroup">
                    <t-tree-select
                      v-model="menuForm.roleGroup"
                      :data="options"
                      class="demo-space"
                      multiple
                      clearable
                      placeholder="请选择权限"
                      :min-collapsed-num="1"
                      style="width: 385px"
                    >
                    </t-tree-select>
                  </t-form-item>
                </t-form>
              </div>
            </t-tab-panel>
            <t-tab-panel :value="0" label="查询条件" :destroyOnHide="false">
              <div style="padding: 10px; max-height: calc(100vh - 205px); overflow-y: auto; overflow-x: hidden">
                <t-table
                  drag-sort="row"
                  row-key="index"
                  :bordered="true"
                  :data="inData"
                  :columns="inCloumns"
                  @drag-sort="onDragSort"
                >
                  <template #no="{ row }">
                    <t-input v-model="row.no"></t-input>
                  </template>
                  <template #name="{ row }">
                    <t-input v-model="row.name"></t-input>
                  </template>
                  <template #field="{ row }">
                    <t-input v-model="row.field"></t-input>
                  </template>
                  <template #type="{ row }">
                    <t-space size="3px" direction="vertical">
                      <t-select v-model="row.type" :options="typeArray" :style="{ width: '110px' }"></t-select>
                      <!-- <t-tooltip
                        v-if="row.type === 'date' || row.type === 'datetime'"
                        :content="
                          row.type === 'date'
                            ? '格式：2024-11-11'
                            : row.type === 'datetime'
                              ? '格式：2024-11-11 12:00:00'
                              : ''
                        "
                      >
                        <FrIcon name="chat-bubble-error" style="color: red" />
                      </t-tooltip> -->
                      <FrData
                        v-show="row.type === 'select'"
                        :ref="'dictRef_' + row.id"
                        v-model="row.dict"
                        url="/center/dict/list"
                        method="get"
                        query-key="label"
                        value-key="code"
                        label-key="label"
                        searchPlaceholder="请输入字典名称模糊搜索"
                        placeholder="请选择关联字典"
                        width="220px"
                      />
                    </t-space>
                  </template>
                  <template #con="{ row }">
                    <t-select v-model="row.con" :options="conList"></t-select>
                  </template>
                  <template #required="{ row }">
                    <t-switch v-model="row.required">
                      <template #label="slotProps">{{ slotProps.value ? '是' : '否' }}</template>
                    </t-switch>
                  </template>
                  <template #isShow="{ row }">
                    <t-switch v-model="row.isShow">
                      <template #label="slotProps">{{ slotProps.value ? '是' : '否' }}</template>
                    </t-switch>
                  </template>
                  <template #options="{ row }">
                    <t-button theme="danger" size="small" variant="text" @click="delRow(row.id)">删除</t-button>
                  </template>
                </t-table>
              </div>
            </t-tab-panel>
            <t-tab-panel :value="1" label="显示列表" :destroyOnHide="false">
              <div style="padding: 10px; max-height: calc(100vh - 205px); overflow-y: auto; overflow-x: hidden">
                <t-table
                  drag-sort="row"
                  row-key="index"
                  :bordered="true"
                  :data="outData"
                  :columns="outCloumns"
                  @drag-sort="onDragSort1"
                >
                  <template #no="{ row }">
                    <t-input v-model="row.no"></t-input>
                  </template>
                  <template #name="{ row }">
                    <t-input v-model="row.name"></t-input>
                  </template>
                  <template #field="{ row }">
                    <t-input v-model="row.field"></t-input>
                  </template>
                  <template #type="{ row }">
                    <t-select v-model="row.type" :options="outTypeArray"></t-select>
                    <FrData
                      v-show="row.type === 'select'"
                      :ref="'dictRef_' + row.id"
                      v-model="row.dict"
                      url="/center/dict/list"
                      method="get"
                      query-key="label"
                      value-key="code"
                      label-key="label"
                      searchPlaceholder="请输入字典名称模糊搜索"
                      placeholder="请选择关联字典"
                    />
                  </template>
                  <template #width="{ row }">
                    <t-input-number v-model="row.width" :min="1" />
                  </template>
                  <template #align="{ row }">
                    <t-select :options="alignArray" v-model="row.align"></t-select>
                  </template>
                  <template #isShow="{ row }">
                    <t-switch v-model="row.isShow">
                      <template #label="slotProps">{{ slotProps.value ? '是' : '否' }}</template>
                    </t-switch>
                  </template>
                  <template #options="{ row }">
                    <t-button theme="danger" variant="text" size="small" @click="delRow(row.id)">删除</t-button>
                  </template>
                </t-table>
              </div>
            </t-tab-panel>
            <t-tab-panel :value="2" label="按钮配置" :destroyOnHide="false">
              <div class="flex justify-end pb-10px pt-10px">
                <t-button size="small" @click="addBtnRow" theme="primary">添加按钮</t-button>
              </div>
              <div style="padding: 10px; max-height: calc(100vh - 250px); overflow-y: auto; overflow-x: hidden">
                <VueDraggable
                  v-if="btnData && btnData.length > 0"
                  class="drag-area"
                  :animation="150"
                  target=".btn-content"
                  v-model="btnData"
                  group="g1"
                >
                  <t-row class="btn-content" v-if="btnData && btnData.length > 0" :gutter="[10, 10]">
                    <t-col v-for="(item, index) in btnData" :key="index" :span="4">
                      <t-card>
                        <t-descriptions tableLayout="auto" :labelStyle="{ width: '120px' }" :column="1" bordered>
                          <t-descriptions-item label="名称" label-align="right">
                            {{ item.name }}
                          </t-descriptions-item>
                          <t-descriptions-item label="业务主键" label-align="right">
                            {{ item.businessKey }}
                          </t-descriptions-item>
                          <t-descriptions-item label="操作类型" label-align="right">
                            {{ computedName(item.optType, optTypeArray) }}
                          </t-descriptions-item>
                          <t-descriptions-item
                            v-if="item.optType === 'add' || item.optType === 'edit'"
                            label="页面类型"
                            label-align="right"
                          >
                            {{ computedName(item.type, pageTypeArray) }}
                          </t-descriptions-item>
                          <t-descriptions-item v-if="item.optType === 'del'" label="删除表配置" label-align="right">
                            {{ computedDelConfig(item.delConfig) }}
                          </t-descriptions-item>
                          <t-descriptions-item
                            v-if="(item.optType === 'add' || item.optType === 'edit') && item.type === '01'"
                            label="关联表单模型"
                            label-align="right"
                          >
                            {{ item.extConfig.formName }}
                          </t-descriptions-item>
                          <t-descriptions-item
                            v-if="(item.optType === 'add' || item.optType === 'edit') && item.type === '02'"
                            label="关联表单模型"
                            label-align="right"
                          >
                            {{ item.formId }}
                          </t-descriptions-item>
                          <t-descriptions-item label="位置" label-align="right">
                            {{ item.position === '1' ? '操作列' : '工具栏' }}
                          </t-descriptions-item>
                          <t-descriptions-item label="是否显示" label-align="right">
                            {{ item.isShow ? '是' : '否' }}
                          </t-descriptions-item>
                          <t-descriptions-item label="是否关联权限" label-align="right">
                            {{ item.isAuth ? '是' : '否' }}
                          </t-descriptions-item>
                          <t-descriptions-item v-if="item.isAuth" label="关联权限" label-align="right">
                            {{ findAllParentNames(options, item.authorize) }}
                          </t-descriptions-item>
                        </t-descriptions>
                        <template #footer>
                          <div class="flex justify-center items-center gap-10px">
                            <t-button theme="primary" size="small" variant="text" @click="editBtn(item)">修改</t-button>
                            <t-button theme="danger" size="small" variant="text" @click="delBtn(item.id)"
                              >删除</t-button
                            >
                          </div>
                        </template>
                      </t-card>
                    </t-col>
                  </t-row>
                </VueDraggable>
                <t-empty v-else :description="'暂未配置按钮'"></t-empty>
              </div>
            </t-tab-panel>
          </t-tabs>
        </div>
      </template>
    </t-drawer>
    <!-- 新增/修改分类信息 -->
    <t-dialog
      v-model:visible="visibleModal1"
      width="350"
      :closeOnOverlayClick="false"
      :header="!menuForm1.id ? (selectNode1.id ? '新增分类' : '新增一级分类') : '修改分类'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn1"
      :on-confirm="onSubmit1"
    >
      <template #body>
        <t-form ref="form1" :label-align="'left'" :data="menuForm1" :layout="'inline'" :rules="rules1">
          <t-form-item v-if="selectNode1.id" label="上级分类">
            <t-input v-model="selectNode1.name" :style="{ width: '300px' }" :disabled="true"></t-input>
          </t-form-item>
          <t-form-item label="分类名称" name="name">
            <t-input v-model="menuForm1.name" :style="{ width: '300px' }" placeholder="请输入分类名称"></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 测试接口 -->
    <t-dialog
      v-model:visible="visibleModal3"
      width="1200"
      :top="100"
      :closeOnOverlayClick="false"
      :header="'预览页面（' + selectRow.name + '）'"
      mode="modal"
      draggable
      :lazy="true"
      :confirm-btn="null"
      :cancel-btn="null"
      :destroy-on-close="true"
    >
      <template #body>
        <QueryModel :modelId="queryModelId" />
        <!-- <t-form ref="form2" :label-align="'left'" :layout="'inline'">
          <t-form-item label="请求地址">
            <t-space size="6px">
              <t-tag theme="primary" variant="light">POST</t-tag> <b>{{ queryUrl }}</b>
            </t-space>
          </t-form-item>
          <t-form-item label="请求参数">
            <t-base-table row-key="index" :bordered="true" :data="queryData" :columns="reqCloumn">
              <template #required="{ row }">
                <t-tag variant="outline" :theme="row.required ? 'danger' : 'success'">{{
                  row.required ? '是' : '否'
                }}</t-tag>
              </template>
              <template #value="{ row }">
                <t-input v-if="row.type === 'text'" v-model="row.value" />
                <t-date-picker v-if="row.type === 'date'" v-model="row.value"></t-date-picker>
                <t-date-picker
                  v-if="row.type === 'datetime'"
                  v-model="row.value"
                  enable-time-picker
                  allow-input
                  clearable
                ></t-date-picker>
              </template>
            </t-base-table>
          </t-form-item>
          <t-form-item label="返回结果">
            <Codemirror
              v-model="queryResult"
              :style="{ height: '350px', width: '50vw' }"
              :autofocus="false"
              :indent-with-tab="true"
              :tabSize="2"
              :disabled="true"
              :extensions="cmOptions"
            />
          </t-form-item>
        </t-form> -->
      </template>
    </t-dialog>
    <!-- 显示导出菜单 -->
    <t-dialog
      v-model:visible="visibleModal5"
      width="500"
      :closeOnOverlayClick="false"
      :header="'导出SQL脚本'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn5"
      :on-confirm="onSubmit5"
    >
      <t-tree-select
        v-model="checkMenu"
        :data="menuOptions"
        class="demo-space"
        multiple
        :tree-props="{
          valueModel: 'all',
          checkStrictly: true,
        }"
        clearable
        placeholder="请选择导出的菜单"
        :min-collapsed-num="1"
        style="width: 385px"
      >
      </t-tree-select>
    </t-dialog>

    <t-dialog
      v-model:visible="visibleModal4"
      :width="700"
      :closeOnOverlayClick="false"
      :header="btnForm.id ? '修改按钮' : '新增按钮'"
      :destroy-on-close="true"
      :lazy="true"
      mode="modal"
      draggable
    >
      <t-form ref="btnFormRef" :label-width="'110px'" :data="btnForm" :rules="btnRule">
        <t-form-item label="名称" name="name">
          <t-input v-model="btnForm.name" placeholder="请填写名称"></t-input>
        </t-form-item>
        <t-form-item label="业务主键" name="businessKey">
          <t-select
            v-model="btnForm.businessKey"
            :options="outData"
            placeholder="请选择业务主键"
            :keys="{ label: 'name', value: 'no' }"
          ></t-select>
        </t-form-item>
        <t-form-item label="操作类型" name="optType">
          <t-select v-model="btnForm.optType" :options="optTypeArray" placeholder="请选择操作类型"></t-select>
        </t-form-item>
        <t-form-item v-if="btnForm.optType !== 'del'" label="页面类型" name="type">
          <t-select
            v-model="btnForm.type"
            :options="pageTypeArray"
            placeholder="请选择页面类型"
            @change="changePageType"
          ></t-select>
        </t-form-item>
        <t-form-item v-if="btnForm.optType === 'del'" label="删除表配置" name="delConfig">
          <div class="flex flex-col">
            <t-button theme="primary" size="small" class="mb-5px" @click="addTableCfg" style="width: 50px"
              >新增</t-button
            >
            <t-table
              :data="btnForm.delConfig"
              :columns="[
                { colKey: 'tableName', width: 140, title: '表名称' },
                { colKey: 'tableKey', width: 140, title: '主键字段' },
                { colKey: 'options', width: 140, title: '操作' },
              ]"
              :bordered="true"
              rowKey="id"
            >
              <template #tableName="{ row }">
                <t-input v-model="row.tableName" placeholder="请输入表名称"></t-input>
              </template>
              <template #tableKey="{ row }">
                <t-input v-model="row.tableKey" placeholder="请输入主键字段"></t-input>
              </template>
              <template #options="{ row }">
                <t-button theme="danger" variant="text" size="small" @click="delTableCfg(row.id)">删除</t-button>
              </template>
            </t-table>
          </div>
        </t-form-item>
        <t-form-item
          v-if="btnForm.optType && btnForm.optType !== 'del' && btnForm.type === '01'"
          name="formId"
          label="关联页面模型"
        >
          <FrData
            ref="pageRef"
            v-model="btnForm.formId"
            url="/lowcode/formTemplate/list"
            method="post"
            :params="{ genre: '03' }"
            query-key="name"
            value-key="id"
            label-key="name"
            description-key="parentName"
            searchPlaceholder="请输入表单名称模糊搜索"
            placeholder="请选择关联表单"
            @change="changeForm1"
          />
        </t-form-item>
        <t-form-item
          v-if="btnForm.optType && btnForm.optType !== 'del' && btnForm.type === '02'"
          name="formId"
          label="页面路径"
        >
          <t-input-adornment prepend="pages/lowcode/pages/">
            <t-input v-model="btnForm.formId" placeholder="请填写页面路径" />
          </t-input-adornment>
        </t-form-item>
        <t-form-item name="position" label="位置">
          <t-radio-group v-model="btnForm.position">
            <t-radio value="1">操作列</t-radio>
            <t-radio value="2">工具栏</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item name="isShow" label="是否显示">
          <t-switch v-model="btnForm.isShow">
            <template #label="slotProps">{{ slotProps.value ? '是' : '否' }}</template>
          </t-switch>
        </t-form-item>
        <t-form-item label="关联权限" name="isAuth">
          <t-switch v-model="btnForm.isAuth">
            <template #label="slotProps">{{ slotProps.value ? '是' : '否' }}</template>
          </t-switch>
        </t-form-item>
        <t-form-item v-if="btnForm.isAuth" label="选择权限" name="authorize">
          <t-tree-select
            v-model="btnForm.authorize"
            :data="options"
            class="demo-space"
            :multiple="false"
            clearable
            placeholder="请选择权限"
            :min-collapsed-num="1"
            style="width: 385px"
          >
          </t-tree-select>
        </t-form-item>
      </t-form>
      <template #footer>
        <t-space :size="5">
          <t-button @click="visibleModal4 = false" theme="default">关闭</t-button>
          <t-button theme="primary" @click="onSubmit4"> 确定 </t-button>
        </t-space>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, reactive, ref, computed, nextTick, getCurrentInstance } from 'vue';
import { listType, addType, editType, delType, add, edit, del, refCache } from '@/api/lowcode/model';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { useUserStore } from '@/store';
import FrIcon from '@/components/fr-icon';
import FrQuery from '@/components/fr-query/index.vue';
import { getDict } from '@/utils/index';
import { modelMenuTree } from '@/api/system/role';
import FrData from '@/components/fr-data/index.vue';
import { Codemirror } from 'vue-codemirror';
import { sql } from '@codemirror/lang-sql';
import { exportSqlFile, findAllParentNames } from '@/utils/index';
import { sm2Encrypt } from '@/utils/encrypt';
import { publicInterface } from '@/api/common';
import { typeArray, outTypeArray, optTypeArray, pageTypeArray, alignArray } from './dict';
import QueryModel from '@/pages/lowcode/QueryModel.vue';
import { VueDraggable } from 'vue-draggable-plus';
import _ from 'lodash';

const cmOptions = [sql()];

const app = getCurrentInstance();

const dict = ref<DictType>({});
const initDict = async () => {
  dict.value = await getDict('SP_DATASOURCE_MODELTYPE,SP_DATASOURCE_MODELRESULT,SP_DATASOURCE_SERVICE');
};
const options = ref([]);

const onDragSort = (params) => {
  inData.value = params.newData;
};
const onDragSort1 = (params) => {
  outData.value = params.newData;
  console.log(outData.value);
};

//新增按钮
const pageRef = ref(null);
const editBtn = (row) => {
  console.log(row);
  visibleModal4.value = true;
  nextTick(() => {
    btnFormRef.value.reset();
    btnForm.value.id = row.id;
    btnForm.value.modelId = row.modelId;
    btnForm.value.name = row.name;
    btnForm.value.type = row.type;
    btnForm.value.formId = row.formId;
    btnForm.value.delConfig = row.delConfig;
    btnForm.value.position = row.position;
    btnForm.value.optType = row.optType;
    btnForm.value.isShow = _.isBoolean(row.isShow) ? row.isShow : row.isShow === 'true';
    btnForm.value.extConfig = row.extConfig;
    btnForm.value.businessKey = row.businessKey;
    btnForm.value.isAuth = _.isBoolean(row.isAuth) ? row.isAuth : row.isAuth === 'true';
    btnForm.value.authorize = row.authorize;
    nextTick(() => {
      if ((btnForm.value.optType === 'add' || btnForm.value.optType === 'edit') && btnForm.value.type === '01') {
        pageRef.value.initData('id', btnForm.value.formId);
      }
    });
  });
};
const delBtn = (id) => {
  btnData.value = btnData.value.filter((row) => {
    return row.id !== id;
  });
};
const computedName = (val, array: Array<any>) => {
  let name = '';
  array.map((row) => {
    if (val === row.value) {
      name = row.label;
    }
  });
  return name;
};
const computedDelConfig = (val) => {
  let str = [];
  val.map((row) => {
    str.push(row.tableName + '.' + row.tableKey);
  });
  return str.join(' | ');
};
const visibleModal4 = ref(false);
const btnForm = ref({
  id: '',
  modelId: '',
  name: '',
  type: '',
  formId: '',
  delConfig: [],
  position: '',
  optType: '',
  isShow: true,
  extConfig: {
    formName: '',
  },
  businessKey: '',
  isAuth: false,
  authorize: '',
});
const btnRule = {
  name: [{ required: true, message: '请输入按钮名称' }],
  type: [{ required: true, message: '请选择跳转页面' }],
  formId: [{ required: true, message: '请选择表单模型' }],
  delConfig: [{ required: true, message: '请选择表单模型' }],
  position: [{ required: true, message: '请选择位置' }],
  optType: [{ required: true, message: '请选择操作类型' }],
  isShow: [{ required: true, message: '请选择是否显示' }],
  businessKey: [{ required: true, message: '请选择业务主键' }],
  isAuth: [{ required: true, message: '请选择是否关联权限' }],
  authorize: [{ required: true, message: '请选择关联权限' }],
};
const btnFormRef = ref(null);
const addBtnRow = () => {
  btnForm.value.modelId = menuForm.value.id;
  visibleModal4.value = true;
  nextTick(() => {
    btnFormRef.value.reset();
    btnForm.value.id = '';
    btnForm.value.name = '';
    btnForm.value.type = '';
    btnForm.value.formId = '';
    btnForm.value.delConfig = [];
    btnForm.value.position = '';
    btnForm.value.optType = '';
    btnForm.value.isShow = true;
    btnForm.value.extConfig = { formName: '' };
    btnForm.value.isAuth = false;
    btnForm.value.authorize = '';
    btnForm.value.businessKey = '';
  });
};
const changePageType = (val) => {
  btnForm.value.formId = '';
  if (val === '01') {
    nextTick(() => {
      pageRef.value.clearData();
    });
  }
};
const addTableCfg = () => {
  btnForm.value.delConfig.push({
    id: 'id_' + Math.random().toString(36).substring(2, 15),
    tableName: '',
    tableKey: '',
  });
};
const delTableCfg = (id) => {
  btnForm.value.delConfig = btnForm.value.delConfig.filter((row) => {
    return row.id !== id;
  });
};
const changeForm1 = (val, data) => {
  console.log(data);
  btnForm.value.extConfig.formName = data.name;
};
const onSubmit4 = async () => {
  let result = await btnFormRef.value.validate();
  if (typeof result !== 'object' && result) {
    try {
      if (btnForm.value.optType === 'del') {
        btnForm.value.delConfig.map((row) => {
          if (!row.tableName || !row.tableKey) {
            throw new Error('请填写完整的<删除表配置>信息');
          }
        });
      }
      if (btnForm.value.id) {
        btnData.value = btnData.value.map((row) => {
          if (row.id === btnForm.value.id) {
            return _.cloneDeep(btnForm.value);
          } else {
            return row;
          }
        });
        console.log(btnForm.value, btnData.value);
      } else {
        btnForm.value.id = 'btn_' + Math.random().toString(36).substring(2, 15);
        btnData.value.push(_.cloneDeep(btnForm.value));
      }

      visibleModal4.value = false;
    } catch (er) {
      MessagePlugin.warning(er.message);
    }
  }
};

//end 新增按钮

const visibleModal3 = ref(false);
const selectRow = ref<CommonObject>({});
const queryData = ref<CommonArray>([]);
const queryUrl = ref('');
const queryResult = ref('');
const saveBtn2 = reactive({
  content: '发起请求',
  loading: false,
});
const sendRequest = async () => {
  let param = {};
  console.log(queryData.value);
  queryData.value.map((row) => {
    param[row.no] = row.value;
  });
  param['modelId'] = selectRow.value.id;
  let res = await publicInterface({
    url: queryUrl.value,
    method: 'post',
    data: param,
  });
  if (res.code === 0) {
    MessagePlugin.success('请求完成');
  }
  queryResult.value = JSON.stringify(res, null, 2);
};
const queryModelId = ref('');
const openTest = (row) => {
  selectRow.value = row;
  queryData.value = row.reqList.map((row) => {
    let obj = row.extAttr;
    obj['value'] = null;
    return row.extAttr;
  });
  queryResult.value = '';
  queryModelId.value = row.id;
  queryUrl.value = dict.value['SP_DATASOURCE_SERVICE'].map.get(row.appName) + '/frQuick/list';
  visibleModal3.value = true;
};
const reqCloumn = ref<Columns>([
  {
    colKey: 'name',
    title: '标题',
    width: 150,
    align: 'center',
  },
  {
    colKey: 'no',
    title: '参数名',
    width: 150,
    align: 'center',
  },
  {
    colKey: 'required',
    title: '必传',
    width: 60,
    align: 'center',
  },

  {
    colKey: 'con',
    title: '条件',
    width: 80,
    align: 'center',
  },
  {
    colKey: 'value',
    title: '参数值',
    width: 200,
    align: 'center',
  },
]);

const handleRefCache = async () => {
  let load = MessagePlugin.loading({
    duration: 0,
    content: '刷新缓存中...',
  });
  try {
    let res = await refCache();
    MessagePlugin.close(load);
    if (res.code === 0) {
      MessagePlugin.success('刷新成功');
    } else {
      MessagePlugin.error(res.msg);
    }
  } catch (e) {
    MessagePlugin.close(load);
  }
};

const frDataRef = ref(null);
const conList = [
  {
    label: '大于',
    value: '>',
  },
  {
    label: '大于等于',
    value: '>=',
  },
  {
    label: '小于',
    value: '<',
  },
  {
    label: '小于等于',
    value: '<=',
  },
  {
    label: '等于',
    value: '=',
  },
  {
    label: '模糊搜索',
    value: 'like',
  },
];
const delRow = (id) => {
  if (tabVal.value === 0) {
    inData.value = inData.value.filter((row) => {
      return row.id !== id;
    });
  } else if (tabVal.value === 1) {
    outData.value = outData.value.filter((row) => {
      return row.id !== id;
    });
  }
};
const addDataRow = () => {
  if (tabVal.value === 0) {
    inData.value.push({
      id: 'field_' + Math.random().toString(36).substring(2, 15),
      no: '',
      type: '',
      con: '',
      required: false,
      name: '',
      field: '',
      isShow: true,
      dict: '',
    });
  } else if (tabVal.value === 1) {
    outData.value.push({
      id: 'field_' + Math.random().toString(36).substring(2, 15),
      no: '',
      type: '',
      field: '',
      isShow: true,
      dict: '',
      width: 100,
      align: 'center',
    });
  }
};
const tabVal = ref(-1);
const inCloumns = ref<Columns>([
  {
    colKey: 'name',
    title: '说明',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'field',
    title: '字段名',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'no',
    title: '参数名',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'type',
    title: '类型',
    width: 200,
    align: 'center',
  },
  {
    colKey: 'con',
    title: '条件',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'required',
    title: '必传',
    width: 60,
    align: 'center',
  },
  {
    colKey: 'isShow',
    title: '是否显示',
    width: 60,
    align: 'center',
  },
  {
    colKey: 'options',
    title: '操作',
    width: 60,
    align: 'center',
  },
]);
const outCloumns = ref<Columns>([
  {
    colKey: 'name',
    title: '标题',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'field',
    title: '字段名',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'no',
    title: '参数名',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'type',
    title: '类型',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'width',
    title: '宽度',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'align',
    title: '对齐方式',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'isShow',
    title: '是否显示',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'options',
    title: '操作',
    width: 100,
    align: 'center',
  },
]);
const inData = ref([]);
const outData = ref([]);
const btnData = ref([]);

const queryRef = ref(null);
const demo1Text = ref(null);
const demo1Filter = ref(null);
const demo1Input = (state) => {
  console.info('demo1 input:', state);
  if (demo1Text.value) {
    // 存在过滤文案，才启用过滤
    demo1Filter.value = (node) => {
      const rs = node.data.name.indexOf(demo1Text.value) >= 0;
      // 命中的节点会强制展示
      // 命中节点的路径节点会锁定展示
      // 未命中的节点会隐藏
      return rs;
    };
  } else {
    // 过滤文案为空，则还原 tree 为无过滤状态
    demo1Filter.value = null;
  }
};

const getOptions = (row) => {
  let options = [
    {
      content: '修改',
      value: 1,
      onClick: () => {
        editTypeBtn(row);
      },
    },
    {
      content: '删除',
      value: 2,
      onClick: () => {
        delTypeBtn(row);
      },
    },
  ];
  if (row.parentId === '0') {
    options.push({
      content: '添加下级',
      value: 1,
      onClick: () => {
        addTypeBtn(row);
      },
    });
  }
  return options;
};
//权限控制
const userStore = useUserStore();
const authAdd = computed(() => userStore.roles.includes('lowcode:model:add'));
const authEdit = computed(() => userStore.roles.includes('lowcode:model:edit'));
const authDel = computed(() => userStore.roles.includes('lowcode:model:del'));

//导出SQL
const checkMenu = ref([]);
const menuOptions = ref([]);
const onSubmit5 = async () => {
  if (checkMenu.value && checkMenu.value.length > 0) {
    exportSqlFile('data_model_type', checkMenu.value.join(','));
    visibleModal5.value = false;
  } else {
    MessagePlugin.warning('请选择要导出的数据');
  }
};
const visibleModal5 = ref(false);
const saveBtn5 = reactive({
  content: '导出',
  loading: false,
});
const showExportMenu1 = async () => {
  checkMenu.value = [];
  visibleModal5.value = true;
  let res = await listType();
  menuOptions.value = res.data || [];
};

const showExportMenu = () => {
  if (!queryRef.value.getSelectData() || queryRef.value.getSelectData().length === 0) {
    MessagePlugin.warning('请选择将要导出的数据');
    return;
  }
  exportSqlFile('data_model', queryRef.value.getSelectData().join(','));
};

//菜单权限按钮部分-start
const visibleModal1 = ref(false);
const form1 = ref(null);
const saveBtn1 = reactive({
  content: '保存',
  loading: false,
});
const menuForm1 = reactive({
  id: '',
  name: '',
});
const selectNode1 = reactive({
  id: '',
  name: '',
});
const rules1 = {
  name: [{ required: true, message: '请输入分类名称', type: 'error' }],
} as Rules;
const addTypeBtn = async (row) => {
  if (row) {
    selectNode1.id = row.id;
    selectNode1.name = row.name;
  } else {
    selectNode1.id = undefined;
    selectNode1.name = undefined;
  }
  visibleModal1.value = true;
  nextTick(() => {
    form1.value.reset();
    menuForm1.id = '';
  });
};
const editTypeBtn = async (row) => {
  visibleModal1.value = true;
  selectNode1.id = undefined;
  selectNode1.name = undefined;
  nextTick(() => {
    form1.value.reset();
    menuForm1.id = row.id;
    menuForm1.name = row.name;
  });
};
const delTypeBtn = async (row) => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认删除分类<' + row.name + '>？',
    confirmBtn: '继续删除',
    onConfirm: async ({ e }) => {
      confirmDia.hide();
      let res = await delType(row.id);
      if (res.code === 0) {
        if (row.id === selectNode.id) {
          selectNode.id = '';
          selectNode.name = '';
          selectNode.parentId = '';
          queryRef.value.clearData();
        }
        MessagePlugin.success('删除成功');
        //重新加载数据
        fetchData();
      } else {
        MessagePlugin.error('删除失败：' + res.msg);
      }
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};
const onSubmit1 = async () => {
  let result = await form1.value.validate();
  if (typeof result !== 'object' && result) {
    saveBtn1.content = '保存中...';
    saveBtn1.loading = true;
    let submitForm = {
      name: menuForm1.name,
      parentId: null,
      id: null,
    };
    if (!menuForm1.id) {
      submitForm.parentId = selectNode1.id || '0';
      try {
        let result1 = await addType(submitForm);
        if (result1.code === 0) {
          visibleModal1.value = false;
          //重新加载数据
          fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn1.content = '保存';
        saveBtn1.loading = false;
      }
    } else {
      submitForm.id = menuForm1.id;
      try {
        let result1 = await editType(submitForm);
        if (result1.code === 0) {
          visibleModal1.value = false;
          //重新加载数据
          fetchData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn1.content = '保存';
        saveBtn1.loading = false;
      }
    }
  }
};
//菜单权限按钮部分-end

const form = ref(null);
const visibleModal = ref(false);
const items = ref([]);
const menuLoad = ref(false);
const menuTree = ref(null);
const activeNode = ref([]);
const expandedRowKeys = ref([]);
const menuForm = ref({
  id: '',
  name: '',
  type: '',
  dataSource: '',
  outType: '',
  roleGroup: [],
  url: '',
  classify: '',
  sqlText: '',
  isPublic: false,
  sort: '',
});
const params = ref({
  classify: '',
  name: '',
});

const selectNode = reactive({
  id: '',
  name: '',
  parentId: '',
});

//新增/修改菜单信息
const saveBtn = reactive({
  content: '保存',
  loading: false,
});

const copyForm = async (row) => {
  visibleModal.value = true;
  inData.value = [];
  outData.value = [];
  let res = await modelMenuTree();
  options.value = res.data;
  tabVal.value = -1;
  nextTick(() => {
    form.value.reset();
    menuForm.value.id = '';
    menuForm.value.name = '';
    menuForm.value.type = row.type;
    menuForm.value.classify = row.classify;
    menuForm.value.dataSource = row.dataSource;
    menuForm.value.isPublic = row.isPublic === '1' ? true : false;
    menuForm.value.outType = row.outType;
    menuForm.value.sqlText = row.sqlText;
    menuForm.value.sort = row.sort;
    menuForm.value.roleGroup = row.roleGroup ? row.roleGroup.split(',') : [];
    nextTick(() => {
      frDataRef.value.initData('id', row.dataSource);
    });
    inData.value = row.reqList.map((row) => {
      return row.extAttr;
    });
    outData.value = row.outList.map((row) => {
      return row.extAttr;
    });
  });
};

const pageLoad = ref(false);
const addForm = async () => {
  visibleModal.value = true;
  nextTick(async () => {
    pageLoad.value = true;
    inData.value = [];
    outData.value = [];
    btnData.value = [];
    let res = await modelMenuTree();
    tabVal.value = -1;
    options.value = res.data;
    form.value.reset();
    menuForm.value.id = '';
    menuForm.value.type = '02';
    menuForm.value.outType = '03';
    menuForm.value.isPublic = false;
    menuForm.value.classify = selectNode.id;
    menuForm.value.sort = '';
    menuForm.value.sqlText = '';
    menuForm.value.roleGroup = [];
    nextTick(() => {
      frDataRef.value.clearData();
    });
    pageLoad.value = false;
  });
};
const editForm = async (row) => {
  visibleModal.value = true;
  nextTick(async () => {
    pageLoad.value = true;
    inData.value = [];
    outData.value = [];
    let res = await modelMenuTree();
    options.value = res.data;
    tabVal.value = -1;
    form.value.reset();
    menuForm.value.id = row.id;
    menuForm.value.name = row.name;
    menuForm.value.type = row.type;
    menuForm.value.classify = row.classify;
    menuForm.value.dataSource = row.dataSource;
    menuForm.value.isPublic = row.isPublic === '1' ? true : false;
    menuForm.value.outType = row.outType;
    menuForm.value.sqlText = row.sqlText;
    menuForm.value.sort = row.sort;
    menuForm.value.roleGroup = row.roleGroup ? row.roleGroup.split(',') : [];
    nextTick(() => {
      frDataRef.value.initData('id', row.dataSource);
    });
    inData.value = row.reqList.map((row) => {
      return row.extAttr;
    });
    outData.value = row.outList.map((row) => {
      return row.extAttr;
    });
    let cBtnList = row.btnList || [];
    btnData.value = cBtnList.map((row) => {
      row.isShow = row.isShow === 'true';
      row.isAuth = row.isAuth === 'true';
      return row;
    });
    nextTick(() => {
      inData.value.map((row) => {
        if (row.type === 'select') {
          let ref = app.refs['dictRef_' + row.id] as any;
          ref.initData('code', row.dict);
        }
      });
      outData.value.map((row) => {
        if (row.type === 'select') {
          let ref = app.refs['dictRef_' + row.id] as any;
          ref.initData('code', row.dict);
        }
      });
    });
    pageLoad.value = false;
  });
};
const delForm = async (row) => {
  let res = await del(row.id);
  if (res.code === 0) {
    MessagePlugin.success('删除成功');
    //重新加载数据
    fetchTreeData();
  } else {
    MessagePlugin.error('删除失败：' + res.msg);
  }
};
const onSubmit = async () => {
  //检查数据输入和输出数据是否必填
  if (inData.value.length === 0 || outData.value.length === 0) {
    MessagePlugin.error('请至少填写一个查询条件/显示列表');
    return;
  }
  let isCanGo = true;
  let inParam = [];
  inData.value.map((row) => {
    inParam.push(row['no']);
    if (
      !row['no'] ||
      !row['type'] ||
      !row['con'] ||
      !row['name'] ||
      !row['field'] ||
      (row['type'] === 'select' && !row.dict)
    ) {
      isCanGo = false;
    }
  });
  if (_.uniq(inParam).length !== inData.value.length) {
    tabVal.value = 0;
    MessagePlugin.error('查询条件中<参数名>不能重复');
    return;
  }
  if (!isCanGo) {
    tabVal.value = 0;
    MessagePlugin.error('查询条件信息填写不完整');
    return;
  }
  isCanGo = true;
  let outParam = [];
  outData.value.map((row) => {
    outParam.push(row['no']);
    if (
      !row['no'] ||
      !row['type'] ||
      !row['field'] ||
      (row['type'] === 'select' && !row.dict) ||
      !row['align'] ||
      !row['width']
    ) {
      isCanGo = false;
    }
  });
  if (_.uniq(outParam).length !== outData.value.length) {
    tabVal.value = 1;
    MessagePlugin.error('显示列表中<参数名>不能重复');
    return;
  }
  if (!isCanGo) {
    tabVal.value = 1;
    MessagePlugin.error('显示列表信息填写不完整');
    return;
  }
  saveBtn.content = '保存中...';
  saveBtn.loading = true;
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    let sqlEn = await sm2Encrypt(menuForm.value.sqlText);
    let submitForm = {
      id: null,
      name: menuForm.value.name,
      type: menuForm.value.type,
      dataSource: menuForm.value.dataSource,
      outType: menuForm.value.outType,
      classify: menuForm.value.classify,
      sqlText: sqlEn,
      isPublic: menuForm.value.isPublic ? '1' : '0',
      roleGroup: menuForm.value.isPublic ? menuForm.value.roleGroup.join(',') : '',
      reqData: inData.value,
      outData: outData.value,
      sort: menuForm.value.sort,
      btnList: btnData.value || [],
    };
    if (!menuForm.value.id) {
      try {
        let result1 = await add(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          await fetchTreeData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败');
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      submitForm.id = menuForm.value.id;
      try {
        let result1 = await edit(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          await fetchTreeData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败');
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  } else {
    tabVal.value = -1;
    saveBtn.content = '保存';
    saveBtn.loading = false;
  }
};
const rules = {
  name: [{ required: true, message: '请输入表单名称', type: 'error' }],
  type: [{ required: true, message: '请选择类型', type: 'error' }],
  dataSource: [{ required: true, message: '请选择数据源', type: 'error' }],
  outType: [{ required: true, message: '请选择输出类型', type: 'error' }],
  roleGroup: [{ required: true, message: '请选择关联权限', type: 'error' }],
  url: [{ required: true, message: '请输入接口地址', type: 'error' }],
  sqlText: [{ required: true, message: '请输入Sql脚本', type: 'error' }],
  isPublic: [{ required: true, message: '请选择是否权限控制', type: 'error' }],
} as Rules;
//结束

const activeChange = async (value: any, context: { node: any }) => {
  expandedRowKeys.value = [];
  if (value.length == 0) {
    selectNode.id = '';
    selectNode.name = '';
    selectNode.parentId = '';
    queryRef.value.clearData();
  } else {
    selectNode.parentId = context.node.data.parentId;
    selectNode.id = context.node.data.id;
    selectNode.name = context.node.data.name;
    params.value.classify = context.node.value;
    await fetchTreeData();
  }
};
const fetchData = async () => {
  menuLoad.value = true;
  try {
    const res = await listType();
    if (res.code === 0) {
      items.value = res.data;
    }
  } catch (e) {
    console.log(e);
  } finally {
    menuLoad.value = false;
  }
};

const fetchTreeData = async () => {
  queryRef.value.loadData();
};

onMounted(async () => {
  initDict();
  await fetchData();
  if (items.value && items.value.length > 0) {
    let data = items.value[0];
    let parentName = items.value[0].name;
    if (items.value[0].children && items.value[0].children.length > 0) {
      data = items.value[0].children[0];
      selectNode1.id = data.parentId;
      selectNode1.name = parentName;
    }
    activeNode.value = [data.id];
    selectNode.parentId = data.parentId;
    selectNode.id = data.id;
    selectNode.name = data.name;
    params.value.classify = data.id;
    await fetchTreeData();
  }
});
const columns = [
  {
    width: 200,
    colKey: 'id',
    title: '编号',
  },
  {
    width: 200,
    colKey: 'name',
    title: '名称',
  },
  {
    width: 200,
    colKey: 'outType',
    title: '输出类型',
    align: 'center',
  },
  {
    width: 140,
    colKey: 'isPublic',
    title: '是否关联权限',
    align: 'center',
  },
  {
    colKey: 'updateTime',
    title: '修改时间',
    width: 200,
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 170,
    cell: 'operation',
    fixed: 'right',
    align: 'center',
  },
] as Columns;

const firstFetch = async () => {
  if (selectNode.id) {
    queryRef.value.loadData(true);
  }
};
</script>

<style lang="less" scoped>
@import '@/style/variables.less';

.table-tree-container {
  background-color: var(--tdvns-bg-color-container);
  border-radius: var(--tdvns-border-radius);

  .t-tree {
    margin-top: 24px;
  }
}

.list-tree-wrapper {
  overflow-y: hidden;
}

.list-tree-operator {
  //width: 200px;
  float: left;
  padding: 10px 10px;
}

.list-tree-content {
  border-left: 1px solid var(--tdvns-border-level-1-color);
  overflow: auto;
}
:deep(.leftCard .t-card__body) {
  padding-top: 0px !important;
}
</style>
<style>
.overlay-options {
  display: inline-block;
  font-size: 20px;
}
.t-select__popup-reference {
  align-items: center;
}
</style>
