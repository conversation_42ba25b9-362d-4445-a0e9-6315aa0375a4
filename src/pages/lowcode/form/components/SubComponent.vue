<template>
  <t-form :label-width="formAttr.config.labelWidth || 100">
    <VueDraggable class="drag-area" :animation="150" target=".content" v-model="list" group="g1">
      <!-- <p>{{ el.text }}</p>
      <sub-component v-if="el.children" v-model="el.children" /> -->
      <t-row :gutter="[10, 10]" class="content" style="min-height: 100px; padding-bottom: 50px">
        <t-col v-if="modelValue.length == 0" :span="12">
          <div style="display: flex; justify-content: center; flex-direction: column; align-items: center; gap: 20px">
            <!-- <Result403Icon style="color: var(--tdvns-brand-color)" /> -->
            <t-empty :description="'请选择左侧组件进行拖拽'" :title="' '" />
          </div>
        </t-col>
        <template v-if="modelValue.length > 0" v-for="el in modelValue" :key="el.id">
          <t-col :span="calcSpan(el)" style="cursor: all-scroll" @click="clickEl(el)">
            <div :class="{ divSelect: selectEl?.id === el.id }">
              <div v-if="selectEl?.id === el.id" class="elBtn">
                <t-space>
                  <t-button shape="circle" theme="primary" variant="outline" size="small" @click.stop="delEl(el)">
                    <template #icon> <FrIcon name="delete" /> </template>
                  </t-button>
                </t-space>
              </div>
              <RenderCom :group="el.group" :type="el.type" :fieldName="el.fieldName" :config="el.config" />
            </div>
          </t-col>
        </template>
      </t-row>
    </VueDraggable>
  </t-form>
</template>
<script setup lang="ts">
import { VueDraggable, DraggableEvent } from 'vue-draggable-plus';
import { computed, ref } from 'vue';
import RenderCom from './RenderCom.vue';
import FrIcon from '@/components/fr-icon';
import Result403Icon from '@/assets/assets-result-403.svg?component';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { IList, FormType } from '@/types/interface';

interface Props {
  modelValue: IList[];
  formAttr: FormType;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'update:modelValue', value: IList[]): void;
  (e: 'clickEl', value: IList): void;
}

const emits = defineEmits<Emits>();
const list = computed({
  get: () => props.modelValue,
  set: (value) => emits('update:modelValue', value),
});

const calcSpan = (el: IList) => {
  if (el.group === 'group') {
    return 12;
  } else if (el.group === 'form') {
    return el.config?.span || props.formAttr.config?.span || 6;
  } else if (el.group === 'data') {
    return el.config?.span || props.formAttr.config?.span || 6;
  } else if (el.group === 'file') {
    return el.config?.span || props.formAttr.config?.span || 6;
  }
};

const selectEl = ref<IList>();

const clickEl = (el: IList) => {
  if (selectEl.value && selectEl.value.id === el.id) {
    selectEl.value = undefined;
  } else {
    selectEl.value = el;
  }
  emits('clickEl', selectEl.value);
};
const delEl = (el: IList) => {
  let diaLog = DialogPlugin.confirm({
    header: '删除提醒',
    body: '是否确定删除组件<' + el.fieldName + '>?',
    confirmBtn: {
      content: '提交',
      theme: 'primary',
      loading: false,
    },
    theme: 'warning',
    onConfirm: () => {
      diaLog.destroy();
      let val = props.modelValue.filter((row) => {
        return row.id !== el.id;
      });
      emits('update:modelValue', val);
      selectEl.value = undefined;
      emits('clickEl', selectEl.value);
    },
  });
};

const clearSelectEl = () => {
  selectEl.value = undefined;
};

defineExpose({
  clearSelectEl,
  clickEl,
});
</script>
<style lang="less" scoped>
.drag-area {
  min-height: 100px;
  //outline: 1px dashed;
  padding: 10px;
}
.divSelect {
  border: 1px solid var(--td-brand-color);
}
.elBtn {
  position: absolute;
  top: -18px;
  right: 8px;
  z-index: 2;
}
</style>
