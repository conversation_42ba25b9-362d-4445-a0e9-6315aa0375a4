<template>
  <div v-if="group === 'group' && type === '17'" class="title">
    <CaretRightIcon size="13px" class="ticon" />
    <span>{{ fieldName }}</span>
  </div>
  <t-form-item
    v-if="group === 'form'"
    :label="fieldName"
    :label-width="fieldName ? props.config.labelWidth : 0"
    :required-mark="props.config.required"
    label-align="left"
  >
    <t-input
      v-if="type === '01'"
      :placeholder="props.config.placeholder || '请输入'"
      :show-limit-number="!!props.config.maxlength"
      :maxlength="props.config.maxlength"
      :disabled="!props.config.isEdit"
      clearable
    >
      <template v-if="props.config.prefixIcon" #prefixIcon>
        <FrIcon :name="props.config.prefixIcon" />
      </template>
      <template v-if="props.config.suffixIcon" #suffixIcon>
        <FrIcon :name="props.config.suffixIcon" />
      </template>
      <template v-if="props.config.suffix" #suffix>
        {{ props.config.suffix }}
      </template>
    </t-input>
    <t-textarea
      v-if="type === '02'"
      :autosize="{ minRows: 3, maxRows: 5 }"
      :placeholder="props.config.placeholder || '请输入'"
      :maxlength="props.config.maxlength"
      :disabled="!props.config.isEdit"
    ></t-textarea>
    <t-input-number
      v-if="type === '03'"
      :placeholder="props.config.placeholder || '请输入'"
      theme="column"
      :max="Number.isNaN(props.config.numberMax) ? null : props.config.numberMax"
      :min="Number.isNaN(props.config.numberMin) ? null : props.config.numberMin"
      :disabled="!props.config.isEdit"
      style="width: 100%"
    >
      <template v-if="props.config.suffix" #suffix>
        {{ props.config.suffix }}
      </template>
    </t-input-number>
    <t-input
      v-if="type === '04'"
      type="password"
      :placeholder="props.config.placeholder || '请输入'"
      clearable
      :disabled="!props.config.isEdit"
    ></t-input>
    <t-date-picker
      v-if="type === '05'"
      :placeholder="props.config.placeholder || '请选择日期'"
      clearable
      :disabled="!props.config.isEdit"
      style="width: 100%"
    ></t-date-picker>
    <t-date-picker
      v-if="type === '06'"
      enable-time-picker
      allow-input
      clearable
      :disabled="!props.config.isEdit"
      :placeholder="props.config.placeholder || '请选择日期'"
      style="width: 100%"
    />
    <t-switch v-if="type === '07'" :disabled="!props.config.isEdit">
      <template #label="slotProps">{{
        slotProps.value ? props.config.switchOpen || '' : props.config.switchClose || ''
      }}</template>
    </t-switch>
    <t-radio-group v-if="type === '08'" :disabled="!props.config.isEdit">
      <t-radio v-for="(tag, index) in props.config.radioList" :value="tag">{{ tag }}</t-radio>
    </t-radio-group>
    <t-checkbox-group
      v-if="type === '09'"
      :options="props.config.radioList"
      :disabled="!props.config.isEdit"
    ></t-checkbox-group>
    <t-select
      v-if="type === '10'"
      :multiple="!!props.config.multiple"
      :placeholder="props.config.placeholder || '请选择'"
      :disabled="!props.config.isEdit"
    >
      <t-option v-for="(item, index) in props.config.dictList" :key="index" :label="item.label" :value="item.value" />
    </t-select>
    <t-select
      v-if="type === '11'"
      :placeholder="props.config.placeholder || '请选择'"
      :disabled="!props.config.isEdit"
      :multiple="!!props.config.multiple"
    >
      <t-option v-for="(item, index) in props.config.radioList" :key="index" :label="item" :value="item" />
    </t-select>
    <FrEditor
      v-if="type === 'form_01'"
      :readOnly="!props.config.isEdit"
      :height="props.config.height + 'px'"
      :placeholder="props.config.placeholder"
    />
  </t-form-item>
  <t-form-item
    v-if="group === 'data'"
    :label="fieldName"
    :label-width="fieldName ? props.config.labelWidth : 0"
    :required-mark="props.config.required"
    label-align="left"
  >
    <FrUser
      v-if="type === '12'"
      :disabled="!props.config.isEdit"
      :placeholder="props.config.placeholder || '请选择'"
      width="100%"
    />
    <FrTenant
      v-if="type === '13'"
      :disabled="!props.config.isEdit"
      :placeholder="props.config.placeholder || '请选择'"
      width="100%"
    />
    <FrData
      v-if="type === '14'"
      :placeholder="props.config.placeholder || '请选择'"
      :searchPlaceholder="props.config.searchPlaceholder || '请选择'"
      v-bind="props.config.dataParam"
      width="100%"
      :disabled="!props.config.isEdit"
    />
    <div v-if="type === 'data_01'" style="width: 100%">
      <t-button v-if="props.config.isEdit" size="small" theme="primary" style="margin-bottom: 5px">添加行</t-button>
      <t-table
        rowKey="id"
        :data="props.config.tableData || []"
        :columns="computedColumnCfg(props.config.tableCol, props.config.isEdit)"
        style="width: 100%"
        :bordered="props.config.tableBordered"
      >
        <template v-for="item in props.config.tableCol" #[item.key]="{ row }">
          {{ row[item.key] }}
        </template>
        <template #options="{ row }">
          <t-button variant="text" theme="primary" size="small"> 修改 </t-button>
          <t-button variant="text" theme="danger" size="small">删除</t-button>
        </template>
      </t-table>
    </div>
  </t-form-item>
  <t-form-item
    v-if="group === 'file'"
    :label="fieldName"
    :label-width="fieldName ? props.config.labelWidth : 0"
    :required-mark="props.config.required"
    label-align="left"
  >
    <FrImg v-if="type === '15'" :size="props.config.fileSize" :disabled="!props.config.isEdit" />
    <FrFile
      v-if="type === '16'"
      :size="props.config.fileSize"
      :accept="props.config.fileAccept"
      :max="props.config.fileMax"
      :disabled="!props.config.isEdit"
    />
  </t-form-item>
</template>

<script lang="ts" setup>
import { CaretRightIcon } from 'tdesign-icons-vue-next';
import { FieldType } from '@/types/interface';
import FrIcon from '@/components/fr-icon';
import FrUser from '@/components/fr-user/index.vue';
import FrTenant from '@/components/fr-tenant/index.vue';
import FrData from '@/components/fr-data/index.vue';
import FrImg from '@/components/fr-img/index.vue';
import FrFile from '@/components/fr-file/index.vue';
import FrEditor from '@/components/fr-editor/index.vue';

const props = defineProps<{
  group: string;
  type: string;
  fieldName: string;
  config: FieldType;
}>();

const computedColumnCfg = (tableCol: Array<any>, isEdit: boolean) => {
  let columns = tableCol.map((row) => {
    row.colKey = row.key;
    row.title = row.label;
    row.width = row.minWidth;
    return row;
  });
  if (isEdit) {
    columns.push({
      colKey: 'options',
      title: '操作',
      width: 100,
    });
  }
  return columns;
};
</script>

<style lang="less" scoped>
.title {
  height: 40px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 2px solid var(--td-brand-color);
  width: 90%;
  span {
    font-size: 14px;
    color: var(--td-brand-color) !important;
  }
  margin-bottom: 10px;
}
</style>
