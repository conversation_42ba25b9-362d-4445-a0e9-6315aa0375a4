<template>
  <div v-loading="pageLoad">
    <t-card v-if="pageResult.success && !pageLoad" :bordered="false">
      <FrQuery
        ref="queryRef"
        v-model:params="params"
        :columns="columns"
        :request="{
          url: queryUrl,
          method: 'post',
        }"
        :is-load="false"
        :is-pagination="queryType"
        :extKey="'modelId'"
      >
        <template #frSimpleQuery>
          <template v-for="(item, index) in queryList">
            <FrUser
              v-if="item.extAttr.type === 'user'"
              :placeholder="item.extAttr.name"
              v-model="params[item.attrNo]"
              :style="{ width: '220px' }"
            />
            <FrOrgan
              v-if="item.extAttr.type === 'organ'"
              :placeholder="item.extAttr.name"
              v-model="params[item.attrNo]"
              :style="{ width: '220px' }"
            />
            <t-input
              v-if="item.extAttr.type === 'input'"
              :placeholder="item.extAttr.name"
              v-model="params[item.attrNo]"
              :style="{ width: '220px' }"
              clearable
            ></t-input>
            <t-date-picker
              v-if="item.extAttr.type === 'date'"
              :placeholder="item.extAttr.name"
              v-model="params[item.attrNo]"
              :style="{ width: '220px' }"
              valueFormat="YYYY-MM-DD"
              clearable
              :editable="false"
            ></t-date-picker>
            <t-date-picker
              v-if="item.extAttr.type === 'datetime'"
              v-model="params[item.attrNo]"
              :placeholder="item.extAttr.name"
              enable-time-picker
              clearable
              valueFormat="YYYY-MM-DD HH:mm:ss"
              :editable="false"
            ></t-date-picker>
            <t-select
              v-if="item.extAttr.type === 'select'"
              :editable="false"
              v-model="params[item.attrNo]"
              :placeholder="item.extAttr.name"
              :options="dict[item.extAttr.dict].list"
            ></t-select>
          </template>
        </template>
        <template #frSimpleBtn>
          <t-space :size="8">
            <template v-for="(item, index) in toolbarList">
              <t-button
                v-if="(item.isAuth === 'true' && userStore.roles?.includes(item.authorize)) || item.isAuth !== 'true'"
                :theme="item.optType === 'add' ? 'primary' : item.optType === 'del' ? 'danger' : 'default'"
                @click="optRow(item)"
                >{{ item.name }}</t-button
              >
            </template>
          </t-space>
        </template>
        <template #operation="{ row }">
          <t-space :size="4">
            <template v-for="(item, index) in optBtnList">
              <t-button
                v-if="
                  ((item.isAuth === 'true' && userStore.roles?.includes(item.authorize)) || item.isAuth === 'false') &&
                  item.optType !== 'del'
                "
                size="small"
                :theme="'primary'"
                variant="text"
                @click="optRow(item, row)"
                >{{ item.name }}</t-button
              >
              <t-popconfirm
                v-if="
                  ((item.isAuth === 'true' && userStore.roles?.includes(item.authorize)) || item.isAuth === 'false') &&
                  item.optType === 'del'
                "
                :content="'是否确认' + item.name + '?'"
                @confirm="delRow(item, row)"
              >
                <t-button size="small" theme="danger" variant="text">{{ item.name }} </t-button>
              </t-popconfirm>
            </template>
          </t-space>
        </template>
      </FrQuery>
    </t-card>
    <Result v-if="!pageLoad && !pageResult.success" type="500" :title="pageResult.text" />

    <t-dialog
      v-model:visible="visibleModal"
      width="70%"
      :header="formTitle"
      top="30px"
      :close-on-overlay-click="false"
      :destroy-on-close="true"
      :lazy="true"
    >
      <div style="height: 65vh; overflow-y: auto; overflow-x: hidden" v-loading="loadPage">
        <DynamicForm v-if="formType === '01' && fieldList.length > 0" ref="dynamicFormRef" :list="fieldList" />
        <MyComponent v-if="formType === '02'" ref="componentRef" :busId="''"></MyComponent>
      </div>
      <template #footer>
        <div
          style="
            width: 100%;
            justify-content: center;
            display: flex;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding-top: 15px;
          "
        >
          <t-button v-if="!isCanSubmit" theme="default" @click="() => (visibleModal = false)">关闭</t-button>
          <t-popconfirm v-if="isCanSubmit" content="是否确认关闭?" @confirm="() => (visibleModal = false)">
            <t-button theme="default">关闭</t-button>
          </t-popconfirm>
          <t-popconfirm v-if="isCanSubmit" content="是否确认提交?" @confirm="submit">
            <t-button :loading="saveBtn.loading" theme="primary">{{ saveBtn.text }}</t-button>
          </t-popconfirm>
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, nextTick, reactive, shallowRef, Component, defineAsyncComponent } from 'vue';
import FrQuery from '@/components/fr-query/index.vue';
import { getModel, getFormModel, publicInterface } from '@/api/common';
import { useRoute } from 'vue-router';
import Result from '@/components/result/index.vue';
import { getDict } from '@/utils/index';
import _ from 'lodash';
import { useUserStore } from '@/store';
import DynamicForm from './form/components/DynamicForm.vue';
import Flow404 from '@/pages/result/Flow404.vue';
import { MessagePlugin as message } from 'tdesign-vue-next';
import FrUser from '@/components/fr-user/index.vue';
import FrOrgan from '@/components/fr-organ/index.vue';

const props = defineProps({
  modelId: {
    type: String,
    default: '',
  },
});
const userStore = useUserStore();
const modules = import.meta.glob('./pages/**');
const MyComponent = shallowRef<Component>();
const dict = ref<DictType>({});
const initDict = async (codes) => {
  dict.value = await getDict(codes);
};
const queryRef = ref(null);
const route = useRoute();
const queryUrl = ref('');
const delUrl = ref('');
const queryType = ref(true);
const queryList = ref([]);
const outList = ref([]);
const toolbarList = ref([]);
const optBtnList = ref([]);
const params = ref<CommonObject>({});
const columns = ref<CommonArray>([]);
const pageLoad = ref(true);
const pageResult = ref({
  success: true,
  text: '',
});

const saveBtn = reactive({
  text: '提交',
  loading: false,
});
const visibleModal = ref(false);
const formType = ref('');
const formId = ref('');
const fieldList = ref([]);
const loadPage = ref(false);
const formTitle = ref('');
const busId = ref('');
const dynamicFormRef = ref(null);
const componentRef = ref(null);
const formUrl = ref('');
const formDataUrl = ref('');
const delRow = async (config, row) => {
  try {
    message.loading({
      duration: 0,
      content: '正在处理中...',
    });
    console.log(row, config);
    let res = await publicInterface({
      url: delUrl.value,
      method: 'post',
      data: {
        formId: dataModelId.value,
        businessKey: row[config.businessKey],
        btnId: config.id,
      },
    });
    message.closeAll();
    if (res.code === 0) {
      message.success('处理成功');
      queryRef.value.loadData();
    } else {
      message.error('处理失败：' + res.msg);
    }
  } catch (er) {
    message.closeAll();
  }
};
const optRow = async (config, row?) => {
  if (config.optType === 'view') {
    isCanSubmit.value = false;
  } else {
    isCanSubmit.value = true;
  }
  loadPage.value = true;
  fieldList.value = [];
  formType.value = config.type;
  formTitle.value = config.name;
  busId.value = row ? row[config.businessKey] : '';
  formId.value = config.formId;
  visibleModal.value = true;
  if (formType.value === '01') {
    let res = await getFormModel({
      formId: formId.value,
    });
    fieldList.value = await _.orderBy(res.data.fieldList, ['sort'], ['asc']);
    formUrl.value = '/' + res.data.server + '/frQuickCode/saveOrEdit';
    formDataUrl.value = '/' + res.data.server + '/frQuickCode/formData';
    if (busId.value) {
      await loadFormData();
    } else {
      nextTick(() => {
        dynamicFormRef.value.reset();
      });
    }
  } else {
    try {
      let sfcCom = modules[`./pages/${formId.value}.vue`];
      MyComponent.value = defineAsyncComponent(sfcCom);
    } catch (e) {
      MyComponent.value = Flow404;
    }
  }
  loadPage.value = false;
};
const loadFormData = async () => {
  let res1 = await publicInterface({
    url: formDataUrl.value,
    method: 'post',
    data: {
      formId: formId.value,
      businessKey: busId.value,
    },
  });
  nextTick(async () => {
    await dynamicFormRef.value.initFormData(res1.data);
  });
};
const submit = async () => {
  if (formType.value === '01') {
    try {
      saveBtn.loading = true;
      saveBtn.text = '提交中...';
      let checkResult = await dynamicFormRef.value.check();
      if (!checkResult) {
        return false;
      }
      let formData = {
        businessKey: busId.value,
        formId: formId.value,
        formData: dynamicFormRef.value.getData(),
      };
      let res = await publicInterface({
        url: formUrl.value,
        method: 'post',
        data: formData,
      });
      if (res.code === 0) {
        message.success('提交成功');
        visibleModal.value = false;
        queryRef.value.loadData();
      } else {
        message.error(res.msg);
      }
    } catch (er) {
      message.error(er.message);
    } finally {
      saveBtn.loading = false;
      saveBtn.text = '提交';
    }
  } else {
    try {
      saveBtn.loading = true;
      saveBtn.text = '提交中...';
      let subRes = await componentRef.value.submit();
      if (subRes) {
        message.success('提交成功');
        visibleModal.value = false;
        queryRef.value.loadData();
      }
    } catch (er) {
      if (er.message.includes('componentRef.value.submit is not a function')) {
        message.error('未定义submit方法');
      } else {
        message.error(er.message);
      }
    } finally {
      saveBtn.loading = false;
      saveBtn.text = '提交';
    }
  }
};

const isCanSubmit = ref(true);
const dataModelId = ref('');
onBeforeMount(async () => {
  try {
    console.log('mo', props.modelId);
    pageLoad.value = true;
    pageResult.value.success = true;
    pageResult.value.text = '';
    dataModelId.value = (route.params['id'] as string) || (route.query['id'] as string) || props.modelId;
    if (!dataModelId.value) {
      throw new Error('缺少模型ID参数');
    }
    params.value['modelId'] = dataModelId.value;
    let res = await getModel({
      modelId: dataModelId.value,
    });
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    //开始解析数据渲染页面
    let modelData = res.data;
    queryType.value = modelData.outType === '03';
    queryUrl.value = '/' + modelData.server + '/frQuick/list';
    delUrl.value = '/' + modelData.server + '/frQuickCode/del';
    queryList.value = modelData.reqData.filter((row) => row.extAttr.isShow);
    outList.value = modelData.outData.filter((row) => row.extAttr.isShow);
    toolbarList.value = modelData.btnData.filter((row) => row.isShow === 'true' && row.position === '2');
    optBtnList.value = modelData.btnData.filter((row) => row.isShow === 'true' && row.position === '1');
    let dictList = [];
    queryList.value.map((row) => {
      params.value[row.attrNo] = null;
      if (row.extAttr.type === 'select') {
        dictList.push(row.extAttr.dict);
      }
    });
    outList.value.map((row) => {
      if (row.extAttr.type === 'select') {
        dictList.push(row.extAttr.dict);
      }
    });
    if (dictList.length > 0) {
      await initDict(_.uniq(dictList).join(','));
    }
    outList.value.map((row) => {
      columns.value.push({
        width: row.extAttr.width,
        key: row.attrNo,
        colKey: row.attrNo,
        dataIndex: row.attrNo,
        title: row.fieldName,
        ellipsis: true,
        align: row.extAttr.align,
      });
    });
    if (optBtnList.value.length > 0) {
      columns.value.push({
        width: optBtnList.value.length * 40,
        key: 'operation',
        dataIndex: 'operation',
        colKey: 'operation',
        title: '操作',
        ellipsis: true,
        align: 'center',
        fixed: 'right',
      });
    }

    //页面初始化完成，开始执行查询
    pageLoad.value = false;
    nextTick(() => {
      queryRef.value.loadData();
    });
  } catch (er) {
    pageResult.value.success = false;
    pageResult.value.text = er.message || '系统未知错误';
  } finally {
    pageLoad.value = false;
  }
});
</script>

<style lang="less" scoped></style>
