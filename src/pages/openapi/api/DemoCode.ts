export const content =
  '@Service("DefaultInterFace")\r\n' +
  '@Slf4j\r\n' +
  'public class DefaultInterFace { \r\n' +
  '        @SentinelResource(value = "DefaultInterFace.demoApi")\r\n' +
  '        public OpenResult demoApi(OpenParam param) {\r\n \r\n' +
  '              OpenResult result = new OpenResult();\r\n' +
  '              log.info("调用接口{}", param.getString("apiNo"));\r\n' +
  '              log.info("当前用户Id{}", AuthUtil.getUserId());\r\n' +
  '              result.put("apiNo", param.getString("apiNo"));\r\n' +
  '              return result;\r\n \r\n' +
  '      }\r\n' +
  '}\r\n';
