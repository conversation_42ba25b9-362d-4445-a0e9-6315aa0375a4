<template>
  <div>
    <t-dialog
      v-model:visible="visibleModal"
      width="600"
      :closeOnOverlayClick="false"
      :header="'修改密钥'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form
          ref="form"
          :label-align="'right'"
          label-width="140px"
          :data="userForm"
          :layout="'vertical'"
          :rules="rules"
        >
          <t-form-item label="AppId">
            <b>{{ userForm.appId }}</b>
          </t-form-item>
          <t-form-item label="当前用户登录密码" name="userPwd">
            <t-input
              v-model="userForm.userPwd"
              type="password"
              :style="{ width: '100%' }"
              placeholder="请输入当前登录用户密码"
            ></t-input>
          </t-form-item>
          <t-form-item label="新AppSecret" name="appSecret">
            <t-input
              v-model="userForm.appSecret"
              type="password"
              :style="{ width: '100%' }"
              placeholder="请输入新AppSecret"
            ></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { editPwd } from '@/api/openapi/user';
import { sm2Encrypt } from '@/utils/encrypt';

const emit = defineEmits(['loadData']);

//新增/修改弹窗start
const visibleModal = ref(false);
const userForm = reactive({
  appId: '',
  userPwd: '',
  appSecret: '',
});
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});

const rules = {
  userPwd: [{ required: true, message: '请输入当前登录用户密码', type: 'error' }],
  appSecret: [{ required: true, message: '请输入新AppSecret', type: 'error' }],
} as Rules;
const onSubmit = async () => {
  saveBtn.content = '修改中...';
  saveBtn.loading = true;
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    let secret = await sm2Encrypt(userForm.appSecret);
    let pwd = await sm2Encrypt(userForm.userPwd);
    let submitForm = {
      appId: userForm.appId,
      userPwd: pwd,
      appSecret: secret,
    };
    try {
      let result1 = await editPwd(submitForm);
      if (result1.code === 0) {
        visibleModal.value = false;
        MessagePlugin.success('修改成功');
      } else {
        MessagePlugin.error('修改失败：' + result1.msg);
      }
    } catch (error) {
      MessagePlugin.error('修改失败');
    } finally {
      saveBtn.content = '保存';
      saveBtn.loading = false;
    }
  } else {
    saveBtn.content = '保存';
    saveBtn.loading = false;
  }
};
//新增/修改弹窗end

const editRow = (row) => {
  visibleModal.value = true;
  nextTick(() => {
    form.value.reset();
    userForm.appId = row.appId;
  });
};
defineExpose({
  editRow,
});
</script>

<style scoped lang="less"></style>
