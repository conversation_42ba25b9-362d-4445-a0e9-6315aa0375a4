<template>
  <div>
    <t-dialog
      v-model:visible="visibleModal"
      width="500"
      :closeOnOverlayClick="false"
      header="接口用户信息"
      mode="modal"
      draggable
      :cancelBtn="null"
      :confirmBtn="null"
    >
      <t-descriptions
        title=""
        :label-style="{ width: '200px', textAlign: 'right' }"
        :content-style="{ textAlign: 'left' }"
        layout="vertical"
      >
        <t-descriptions-item label="AppId">{{ rowData.appId }}</t-descriptions-item>
        <t-descriptions-item label="用户昵称">{{ rowData.nickName }}</t-descriptions-item>
        <t-descriptions-item label="电子邮箱">{{ rowData.email }}</t-descriptions-item>
        <t-descriptions-item label="手机号">{{ rowData.phone }}</t-descriptions-item>
        <t-descriptions-item label="token有效时长(秒)">{{ rowData.tokenExp }}</t-descriptions-item>
        <t-descriptions-item label="refTokenExp有效时长(秒)">{{ rowData.refTokenExp }}</t-descriptions-item>
        <t-descriptions-item label="用户状态">
          <t-tag shape="round" :theme="rowData.status === '0' ? 'primary' : 'warning'" variant="light" size="small">{{
            dictObj.status.obj[rowData.status]
          }}</t-tag>
        </t-descriptions-item>
      </t-descriptions>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps({
  dictObj: {
    type: Object,
  },
});

const rowData = ref({
  appId: '',
  nickName: '',
  email: '',
  phone: '',
  tokenExp: '',
  refTokenExp: '',
  status: '',
});

const visibleModal = ref(false);
const initView = (row) => {
  rowData.value.appId = row.appId;
  rowData.value.nickName = row.nickName;
  rowData.value.email = row.email;
  rowData.value.phone = row.phone;
  rowData.value.tokenExp = row.tokenExp;
  rowData.value.refTokenExp = row.refTokenExp;
  rowData.value.status = row.status;
  visibleModal.value = true;
};

defineExpose({
  initView,
});
</script>

<style scoped lang="less"></style>
