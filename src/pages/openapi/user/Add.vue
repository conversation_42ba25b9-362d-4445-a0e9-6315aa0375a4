<template>
  <div>
    <t-dialog
      v-model:visible="visibleModal"
      width="600"
      :closeOnOverlayClick="false"
      :header="opt === 'add' ? '创建接口用户' : '修改接口用户'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-alert
          v-if="opt === 'add'"
          style="margin: 10px 0px 10px 0px"
          theme="info"
          message="appId在创建成功后自动生成"
        />
        <t-form
          ref="form"
          :label-align="'right'"
          label-width="140px"
          :data="userForm"
          :layout="'vertical'"
          :rules="rules"
        >
          <t-form-item v-if="opt === 'edit'" label="appId">
            <t-input v-model="userForm.appId" :disabled="true" :style="{ width: '100%' }"></t-input>
          </t-form-item>
          <t-form-item v-if="opt === 'add'" label="appSecret" name="appSecret">
            <t-input
              v-model="userForm.appSecret"
              type="password"
              :style="{ width: '100%' }"
              placeholder="请输入appSecret"
            ></t-input>
          </t-form-item>
          <t-form-item label="用户昵称" name="nickName">
            <t-input v-model="userForm.nickName" :style="{ width: '100%' }" placeholder="请输入用户昵称"></t-input>
          </t-form-item>
          <t-form-item label="电子邮箱" name="email">
            <t-input v-model="userForm.email" :style="{ width: '100%' }" placeholder="请输入电子邮箱"></t-input>
          </t-form-item>
          <t-form-item label="手机号" name="phone">
            <t-input v-model="userForm.phone" :style="{ width: '100%' }" placeholder="请输入手机号"></t-input>
          </t-form-item>
          <t-form-item label="token有效时长" name="tokenExp">
            <t-input
              type="number"
              v-model="userForm.tokenExp"
              :style="{ width: '100%' }"
              placeholder="请输入token有效时长"
              suffix="秒"
            ></t-input>
          </t-form-item>
          <t-form-item label="refToken有效时长" name="refTokenExp">
            <t-input
              type="number"
              v-model="userForm.refTokenExp"
              :style="{ width: '100%' }"
              placeholder="请输入refToken有效时长"
              suffix="秒"
            ></t-input>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { add, edit } from '@/api/openapi/user';
import { sm2Encrypt } from '@/utils/encrypt';

const emit = defineEmits(['loadData', '']);
const props = defineProps({
  dictObj: {
    type: Object,
  },
});

const opt = ref('add');

//新增/修改弹窗start
const visibleModal = ref(false);
const userForm = reactive({
  appId: '',
  appSecret: '',
  nickName: '',
  email: '',
  phone: '',
  tokenExp: '',
  refTokenExp: '',
});
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});

const npwValidator = async (val) => {
  if (val) {
    let rex = /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/;
    if (!rex.test(val)) {
      return { result: false, message: '至少六位，需包含大小写字，数字和特殊字符', type: 'error' };
    }
  }
  return { result: true, type: 'success' };
};

const rules = {
  appSecret: [{ required: true, message: '请输入appSecret', type: 'error' }, { validator: npwValidator }],
  nickName: [{ required: true, message: '请输入用户昵称', type: 'error' }],
  email: [
    { required: true, message: '请输入电子邮箱', type: 'error' },
    { email: { ignore_max_length: true }, message: '请输入正确的邮箱地址' },
  ],
  phone: [
    { required: true, message: '请输入手机号', type: 'error' },
    { telnumber: true, message: '请输入正确的手机号码' },
  ],
  tokenExp: [{ required: true, message: '请输入token有效时长', type: 'error' }],
  refTokenExp: [{ required: true, message: '请输入refToken有效时长', type: 'error' }],
} as Rules;
const onSubmit = async () => {
  saveBtn.content = '保存中...';
  saveBtn.loading = true;
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    let secret = await sm2Encrypt(userForm.appSecret);
    let submitForm = {
      appSecret: secret,
      nickName: userForm.nickName,
      email: userForm.email,
      phone: userForm.phone,
      tokenExp: userForm.tokenExp,
      refTokenExp: userForm.refTokenExp,
      appId: null,
    };
    if (opt.value === 'add') {
      try {
        let result1 = await add(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          emit('loadData');
          let diaMsg = DialogPlugin.alert({
            theme: 'success',
            header: '接口用户创建成功',
            body: 'AppId： ' + result1.data,
            confirmBtn: {
              content: '确认',
              variant: 'base',
              theme: 'primary',
            },
            onConfirm: ({ e }) => {
              diaMsg.hide();
            },
            onClose: ({ e, trigger }) => {
              diaMsg.hide();
            },
          });
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      try {
        submitForm.appId = userForm.appId;
        let result1 = await edit(submitForm);
        if (result1.code === 0) {
          visibleModal.value = false;
          emit('loadData');
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败');
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  } else {
    saveBtn.content = '保存';
    saveBtn.loading = false;
  }
};
//新增/修改弹窗end

const addRow = () => {
  opt.value = 'add';
  visibleModal.value = true;
  nextTick(() => {
    form.value.reset();
    userForm.appId = '';
  });
};
const editRow = (row) => {
  opt.value = 'edit';
  visibleModal.value = true;
  nextTick(() => {
    form.value.reset();
    userForm.appId = row.appId;
    userForm.email = row.email;
    userForm.nickName = row.nickName;
    userForm.phone = row.phone;
    userForm.refTokenExp = row.refTokenExp;
    userForm.tokenExp = row.tokenExp;
  });
};
defineExpose({
  addRow,
  editRow,
});
</script>

<style scoped lang="less"></style>
