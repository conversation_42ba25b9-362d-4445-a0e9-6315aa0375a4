<template>
  <div>
    <div>
      <t-card :bordered="false">
        <FrQuery
          ref="queryRef"
          v-model:params="params"
          :columns="columns"
          :orders="[
            {
              asc: false,
              column: 'create_time',
            },
          ]"
          :request="{
            url: '/openapi/apiLog/list',
            method: 'post',
          }"
        >
          <template #frSimpleQuery>
            <t-input
              placeholder="AppId"
              clearable
              v-model="params.appId"
              @enter="firstFetch"
              :style="{ width: '220px' }"
            ></t-input>
            <t-input
              placeholder="接口编号"
              clearable
              v-model="params.apiNo"
              @enter="firstFetch"
              :style="{ width: '220px' }"
            ></t-input>
          </template>
          <template #status="{ row }">
            <t-tag v-if="row.status == '0'" shape="round" theme="success" variant="light-outline">
              <check-circle-filled-icon />成功
            </t-tag>
            <t-tooltip v-if="row.status == '1'" :content="row.error">
              <t-tag shape="round" theme="danger" variant="light-outline"> <error-circle-filled-icon />失败 </t-tag>
            </t-tooltip>
          </template>
          <template #method="{ row }">
            <t-tooltip :content="row.param"
              ><t-tag shape="round" theme="primary" size="small" variant="light">param</t-tag></t-tooltip
            >
          </template>
          <template #outParam="{ row }">
            <t-tooltip :content="row.outParam"
              ><t-tag shape="round" theme="primary" size="small" variant="light">result</t-tag></t-tooltip
            >
          </template>
        </FrQuery>
      </t-card>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListTenant',
};
</script>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import FrQuery from '@/components/fr-query/index.vue';
import { CheckCircleFilledIcon, ErrorCircleFilledIcon } from 'tdesign-icons-vue-next';

const queryRef = ref(null);
const firstFetch = async () => {
  queryRef.value.loadData(true);
};
//左侧角色菜单列表数据start
const columns = [
  {
    width: 80,
    colKey: 'status',
    title: '状态',
  },
  {
    width: 120,
    colKey: 'apiNo',
    title: '接口编号',
  },
  {
    width: 120,
    colKey: 'version',
    title: '版本号',
  },
  {
    width: 120,
    colKey: 'appId',
    title: 'AppId',
  },
  {
    width: 100,
    colKey: 'ip',
    title: '请求IP',
  },
  {
    width: 80,
    colKey: 'method',
    title: '请求参数',
    align: 'center',
  },
  {
    width: 80,
    colKey: 'outParam',
    title: '返回内容',
    align: 'center',
  },
  {
    width: 120,
    colKey: 'createTime',
    title: '请求时间',
    align: 'center',
  },
  {
    width: 100,
    colKey: 'time',
    title: '耗时(ms)',
    align: 'center',
  },
] as Columns;
const params = ref({
  apiNo: undefined,
  appId: undefined,
});
//左侧角色菜单列表数据end

//vue的api
onMounted(() => {});
</script>

<style lang="less" scoped>
@import '@/style/variables';
.menu-active {
  color: var(--td-brand-color) !important;
}
.menu-unactive {
  color: var(--tdvns-text-color-primary) !important;
}
.menu-text {
  vertical-align: middle;
}
.sp-role-left {
  border-radius: 8px;
  .sp-role-left-header {
    padding-bottom: 10px;
  }
}
</style>
