<template>
  <div>
    <t-card class="list-card-container">
      <div class="table-container">
        <div class="table-header">
          <div class="table-header-inner">
            <div class="title">任务类型管理</div>
            <div class="operation-container">
              <t-button @click="handleAdd" theme="primary">
                <template #icon><fr-icon name="add" /></template>
                新增任务类型
              </t-button>
              <t-button @click="handleRefresh" variant="outline" theme="default">
                <template #icon><fr-icon name="refresh" /></template>
                刷新
              </t-button>
            </div>
          </div>
        </div>

        <!-- 查询条件 -->
        <div class="table-header">
          <t-form ref="form" :data="searchForm" :label-width="80" layout="inline">
            <t-form-item label="类型编码" name="typeCode">
              <t-input v-model="searchForm.typeCode" placeholder="请输入类型编码" clearable />
            </t-form-item>
            <t-form-item label="类型名称" name="typeName">
              <t-input v-model="searchForm.typeName" placeholder="请输入类型名称" clearable />
            </t-form-item>
            <t-form-item label="状态" name="status">
              <t-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <t-option value="0" label="启用" />
                <t-option value="1" label="禁用" />
              </t-select>
            </t-form-item>
            <t-form-item>
              <t-button theme="primary" @click="handleSearch">搜索</t-button>
              <t-button variant="outline" theme="default" @click="handleReset">重置</t-button>
            </t-form-item>
          </t-form>
        </div>

        <!-- 数据表格 -->
        <t-table
          :data="data"
          :columns="COLUMNS"
          :row-key="rowKey"
          vertical-align="top"
          :hover="true"
          :pagination="pagination"
          :loading="dataLoading"
          @page-change="rehandlePageChange"
          @change="rehandleChange"
        >
          <template #status="{ row }">
            <t-tag v-if="row.status === '0'" theme="success" variant="light">启用</t-tag>
            <t-tag v-else theme="warning" variant="light">禁用</t-tag>
          </template>
          <template #isSystem="{ row }">
            <t-tag v-if="row.isSystem === '1'" theme="primary" variant="light">系统内置</t-tag>
            <t-tag v-else theme="default" variant="light">自定义</t-tag>
          </template>
          <template #op="slotProps">
            <a class="t-button-link" @click="handleClickView(slotProps)">查看</a>
            <a class="t-button-link" @click="handleClickEdit(slotProps)">编辑</a>
            <a v-if="slotProps.row.status === '0'" class="t-button-link" @click="handleToggleStatus(slotProps, '1')">禁用</a>
            <a v-else class="t-button-link" @click="handleToggleStatus(slotProps, '0')">启用</a>
            <a v-if="slotProps.row.isSystem !== '1'" class="t-button-link" @click="handleClickDelete(slotProps)">删除</a>
          </template>
        </t-table>
      </div>
    </t-card>

    <!-- 新增/编辑对话框 -->
    <t-dialog
      :visible="dialogVisible"
      :header="dialogTitle"
      width="600px"
      :on-cancel="handleCancel"
      :on-confirm="handleConfirm"
      :confirm-loading="confirmLoading"
    >
      <template #body>
        <t-form ref="dialogForm" :data="formData" :rules="rules" :label-width="100">
          <t-form-item label="类型编码" name="typeCode">
            <t-input v-model="formData.typeCode" placeholder="请输入类型编码" :disabled="isEdit" />
          </t-form-item>
          <t-form-item label="类型名称" name="typeName">
            <t-input v-model="formData.typeName" placeholder="请输入类型名称" />
          </t-form-item>
          <t-form-item label="描述" name="description">
            <t-textarea v-model="formData.description" placeholder="请输入描述" :rows="3" />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 查看对话框 -->
    <t-dialog
      :visible="viewDialogVisible"
      header="任务类型详情"
      width="600px"
      :footer="false"
      :on-cancel="() => { viewDialogVisible = false }"
    >
      <template #body>
        <t-descriptions :data="viewData" />
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { 
  getTaskTypeList, 
  createTaskType, 
  updateTaskType, 
  deleteTaskType, 
  toggleTaskTypeStatus,
  getTaskTypeById 
} from '@/api/equipment/task';

const COLUMNS = [
  { colKey: 'typeCode', title: '类型编码', width: 150 },
  { colKey: 'typeName', title: '类型名称', width: 150 },
  { colKey: 'description', title: '描述', ellipsis: true },
  { colKey: 'isSystem', title: '类型', width: 100 },
  { colKey: 'status', title: '状态', width: 100 },
  { colKey: 'createDate', title: '创建时间', width: 160 },
  { colKey: 'op', title: '操作', width: 200, fixed: 'right' },
];

const data = ref([]);
const pagination = ref({
  defaultPageSize: 10,
  total: 0,
  defaultCurrent: 1,
});

const searchForm = reactive({
  typeCode: '',
  typeName: '',
  status: '',
});

const dataLoading = ref(false);
const dialogVisible = ref(false);
const viewDialogVisible = ref(false);
const confirmLoading = ref(false);
const isEdit = ref(false);

const dialogTitle = computed(() => (isEdit.value ? '编辑任务类型' : '新增任务类型'));

const formData = reactive({
  id: '',
  typeCode: '',
  typeName: '',
  description: '',
});

const viewData = ref([]);

const rules = {
  typeCode: [{ required: true, message: '请输入类型编码', trigger: 'blur' }],
  typeName: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
};

const rowKey = 'id';
const dialogForm = ref();
const form = ref();

const getList = async () => {
  dataLoading.value = true;
  try {
    const params = {
      page: pagination.value.defaultCurrent,
      size: pagination.value.defaultPageSize,
      ...searchForm,
    };
    const response = await getTaskTypeList(params);
    data.value = response.data.records;
    pagination.value.total = response.data.total;
  } catch (error) {
    console.error('获取任务类型列表失败:', error);
    MessagePlugin.error('获取任务类型列表失败');
  } finally {
    dataLoading.value = false;
  }
};

const handleAdd = () => {
  isEdit.value = false;
  dialogVisible.value = true;
  resetForm();
};

const handleClickEdit = async (slotProps: any) => {
  isEdit.value = true;
  dialogVisible.value = true;
  try {
    const response = await getTaskTypeById(slotProps.row.id);
    Object.assign(formData, response.data);
  } catch (error) {
    console.error('获取任务类型详情失败:', error);
    MessagePlugin.error('获取任务类型详情失败');
  }
};

const handleClickView = async (slotProps: any) => {
  viewDialogVisible.value = true;
  try {
    const response = await getTaskTypeById(slotProps.row.id);
    const taskType = response.data;
    viewData.value = [
      { label: '类型编码', value: taskType.typeCode },
      { label: '类型名称', value: taskType.typeName },
      { label: '描述', value: taskType.description || '-' },
      { label: '类型', value: taskType.isSystem === '1' ? '系统内置' : '自定义' },
      { label: '状态', value: taskType.status === '0' ? '启用' : '禁用' },
      { label: '创建时间', value: taskType.createDate },
    ];
  } catch (error) {
    console.error('获取任务类型详情失败:', error);
    MessagePlugin.error('获取任务类型详情失败');
  }
};

const handleClickDelete = (slotProps: any) => {
  const { row } = slotProps;
  const confirmDia = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定删除任务类型"${row.typeName}"吗？`,
    onConfirm: async () => {
      try {
        await deleteTaskType(row.id);
        MessagePlugin.success('删除成功');
        getList();
        confirmDia.destroy();
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败');
      }
    },
  });
};

const handleToggleStatus = async (slotProps: any, status: string) => {
  const { row } = slotProps;
  try {
    await toggleTaskTypeStatus(row.id, status);
    MessagePlugin.success('状态更新成功');
    getList();
  } catch (error) {
    console.error('状态更新失败:', error);
    MessagePlugin.error('状态更新失败');
  }
};

const handleConfirm = async () => {
  const form = dialogForm.value;
  const valid = await form.validate();
  if (!valid) return false;

  confirmLoading.value = true;
  try {
    if (isEdit.value) {
      await updateTaskType(formData);
      MessagePlugin.success('更新成功');
    } else {
      await createTaskType(formData);
      MessagePlugin.success('创建成功');
    }
    dialogVisible.value = false;
    getList();
  } catch (error) {
    console.error('操作失败:', error);
    MessagePlugin.error('操作失败');
  } finally {
    confirmLoading.value = false;
  }
};

const handleCancel = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    typeCode: '',
    typeName: '',
    description: '',
  });
};

const handleSearch = () => {
  pagination.value.defaultCurrent = 1;
  getList();
};

const handleReset = () => {
  Object.assign(searchForm, {
    typeCode: '',
    typeName: '',
    status: '',
  });
  handleSearch();
};

const handleRefresh = () => {
  getList();
};

const rehandlePageChange = (curr: any) => {
  pagination.value.defaultCurrent = curr.current;
  pagination.value.defaultPageSize = curr.pageSize;
  getList();
};

const rehandleChange = () => {
  getList();
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.list-card-container {
  padding: 0;
}

.table-container {
  padding: 0;
}

.table-header {
  padding: 20px;
  background: #fff;
}

.table-header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
}

.operation-container {
  display: flex;
  gap: 12px;
}

.t-button-link {
  color: #0052d9;
  text-decoration: none;
  margin-right: 16px;
  cursor: pointer;
}

.t-button-link:hover {
  color: #266fe8;
}

.t-button-link:last-child {
  margin-right: 0;
}
</style>