<template>
  <div>
    <t-row :gutter="[16, 16]">
      <!-- 左侧：标签分类 -->
      <t-col :span="3">
        <t-card class="category-card">
          <div class="category-header">
            <span class="category-title">标签分类</span>
            <t-button size="small" theme="primary" variant="text" @click="handleAddCategory">
              <template #icon><fr-icon name="add" /></template>
            </t-button>
          </div>
          <t-tree
            :data="categoryTree"
            :keys="{ value: 'id', label: 'categoryName' }"
            :activable="true"
            :expandAll="true"
            @active="handleCategorySelect"
          >
            <template #operations="{ node }">
              <t-dropdown :options="getCategoryOptions(node)" @click="handleCategoryOperation">
                <t-button size="small" variant="text" shape="square">
                  <fr-icon name="more" />
                </t-button>
              </t-dropdown>
            </template>
          </t-tree>
        </t-card>
      </t-col>
      
      <!-- 右侧：标签管理 -->
      <t-col :span="9">
        <t-card class="list-card-container">
          <div class="table-container">
            <div class="table-header">
              <div class="table-header-inner">
                <div class="title">标签管理</div>
                <div class="operation-container">
                  <t-button @click="handleAdd" theme="primary">
                    <template #icon><fr-icon name="add" /></template>
                    新增标签
                  </t-button>
                  <t-button @click="handleBatchOperation" variant="outline" theme="primary">
                    <template #icon><fr-icon name="edit" /></template>
                    批量操作
                  </t-button>
                  <t-button @click="handleImport" variant="outline" theme="primary">
                    <template #icon><fr-icon name="upload" /></template>
                    导入
                  </t-button>
                  <t-button @click="handleExport" variant="outline" theme="default">
                    <template #icon><fr-icon name="download" /></template>
                    导出
                  </t-button>
                  <t-button @click="handleRefresh" variant="outline" theme="default">
                    <template #icon><fr-icon name="refresh" /></template>
                    刷新
                  </t-button>
                </div>
              </div>
            </div>

            <!-- 查询条件 -->
            <div class="table-header">
              <t-form ref="form" :data="searchForm" :label-width="80" layout="inline">
                <t-form-item label="标签名称" name="tagName">
                  <t-input v-model="searchForm.tagName" placeholder="请输入标签名称" clearable />
                </t-form-item>
                <t-form-item label="标签颜色" name="tagColor">
                  <t-color-picker v-model="searchForm.tagColor" clearable />
                </t-form-item>
                <t-form-item label="状态" name="status">
                  <t-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                    <t-option value="0" label="启用" />
                    <t-option value="1" label="禁用" />
                  </t-select>
                </t-form-item>
                <t-form-item>
                  <t-button theme="primary" @click="handleSearch">搜索</t-button>
                  <t-button variant="outline" theme="default" @click="handleReset">重置</t-button>
                </t-form-item>
              </t-form>
            </div>

            <!-- 标签云展示 -->
            <div class="tag-cloud-container" v-if="popularTags.length > 0">
              <div class="tag-cloud-header">
                <span>热门标签</span>
                <t-link theme="primary" @click="handleViewStatistics">查看统计</t-link>
              </div>
              <div class="tag-cloud">
                <t-tag
                  v-for="tag in popularTags"
                  :key="tag.id"
                  :theme="getTagTheme(tag.tagColor)"
                  variant="light"
                  class="tag-item"
                  @click="handleTagClick(tag)"
                >
                  {{ tag.tagName }} ({{ tag.usageCount }})
                </t-tag>
              </div>
            </div>

            <!-- 数据表格 -->
            <t-table
              :data="data"
              :columns="COLUMNS"
              :row-key="rowKey"
              vertical-align="top"
              :hover="true"
              :pagination="pagination"
              :loading="dataLoading"
              :selected-row-keys="selectedRowKeys"
              @select-change="handleSelectChange"
              @page-change="rehandlePageChange"
              @change="rehandleChange"
            >
              <template #tagColor="{ row }">
                <div class="tag-color-display">
                  <div class="color-block" :style="{ backgroundColor: row.tagColor }"></div>
                  <span>{{ row.tagColor }}</span>
                </div>
              </template>
              <template #status="{ row }">
                <t-tag v-if="row.status === '0'" theme="success" variant="light">启用</t-tag>
                <t-tag v-else theme="warning" variant="light">禁用</t-tag>
              </template>
              <template #usageCount="{ row }">
                <t-link theme="primary" @click="handleViewUsage(row)">{{ row.usageCount || 0 }}个设备</t-link>
              </template>
              <template #op="slotProps">
                <a class="t-button-link" @click="handleClickView(slotProps)">查看</a>
                <a class="t-button-link" @click="handleClickEdit(slotProps)">编辑</a>
                <a class="t-button-link" @click="handleViewUsage(slotProps.row)">使用情况</a>
                <a v-if="slotProps.row.usageCount === 0" class="t-button-link" @click="handleClickDelete(slotProps)">删除</a>
              </template>
            </t-table>
          </div>
        </t-card>
      </t-col>
    </t-row>

    <!-- 新增/编辑标签分类对话框 -->
    <t-dialog
      :visible="categoryDialogVisible"
      :header="categoryDialogTitle"
      width="500px"
      :on-cancel="handleCategoryCancel"
      :on-confirm="handleCategoryConfirm"
      :confirm-loading="categoryConfirmLoading"
    >
      <template #body>
        <t-form ref="categoryForm" :data="categoryFormData" :rules="categoryRules" :label-width="100">
          <t-form-item label="分类名称" name="categoryName">
            <t-input v-model="categoryFormData.categoryName" placeholder="请输入分类名称" />
          </t-form-item>
          <t-form-item label="分类描述" name="description">
            <t-textarea v-model="categoryFormData.description" placeholder="请输入分类描述" :rows="3" />
          </t-form-item>
          <t-form-item label="排序" name="sortOrder">
            <t-input-number v-model="categoryFormData.sortOrder" :min="0" />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 新增/编辑标签对话框 -->
    <t-dialog
      :visible="dialogVisible"
      :header="dialogTitle"
      width="600px"
      :on-cancel="handleCancel"
      :on-confirm="handleConfirm"
      :confirm-loading="confirmLoading"
    >
      <template #body>
        <t-form ref="dialogForm" :data="formData" :rules="rules" :label-width="100">
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="标签名称" name="tagName">
                <t-input v-model="formData.tagName" placeholder="请输入标签名称" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="标签颜色" name="tagColor">
                <t-color-picker v-model="formData.tagColor" />
              </t-form-item>
            </t-col>
          </t-row>
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="所属分类" name="categoryId">
                <t-select v-model="formData.categoryId" placeholder="请选择分类">
                  <t-option v-for="category in categories" :key="category.id" :value="category.id" :label="category.categoryName" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="排序" name="sortOrder">
                <t-input-number v-model="formData.sortOrder" :min="0" />
              </t-form-item>
            </t-col>
          </t-row>
          <t-form-item label="标签描述" name="description">
            <t-textarea v-model="formData.description" placeholder="请输入标签描述" :rows="3" />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 查看对话框 -->
    <t-dialog
      :visible="viewDialogVisible"
      header="标签详情"
      width="600px"
      :footer="false"
      :on-cancel="() => { viewDialogVisible = false }"
    >
      <template #body>
        <t-descriptions :data="viewData" />
      </template>
    </t-dialog>

    <!-- 使用情况对话框 -->
    <t-dialog
      :visible="usageDialogVisible"
      header="标签使用情况"
      width="800px"
      :footer="false"
      :on-cancel="() => { usageDialogVisible = false }"
    >
      <template #body>
        <t-table :data="usageData" :columns="USAGE_COLUMNS" />
      </template>
    </t-dialog>

    <!-- 统计信息对话框 -->
    <t-dialog
      :visible="statisticsDialogVisible"
      header="标签统计"
      width="800px"
      :footer="false"
      :on-cancel="() => { statisticsDialogVisible = false }"
    >
      <template #body>
        <div class="statistics-container">
          <!-- TODO: 添加图表组件显示统计信息 -->
          <t-descriptions :data="statisticsData" />
        </div>
      </template>
    </t-dialog>

    <!-- 批量操作对话框 -->
    <t-dialog
      :visible="batchDialogVisible"
      header="批量操作"
      width="500px"
      :on-cancel="() => { batchDialogVisible = false }"
      :on-confirm="handleBatchConfirm"
      :confirm-loading="batchLoading"
    >
      <template #body>
        <t-form :data="batchForm" :label-width="100">
          <t-form-item label="操作类型" name="operation">
            <t-radio-group v-model="batchForm.operation">
              <t-radio value="updateCategory">修改分类</t-radio>
              <t-radio value="updateStatus">修改状态</t-radio>
              <t-radio value="delete">删除标签</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item v-if="batchForm.operation === 'updateCategory'" label="目标分类" name="targetCategoryId">
            <t-select v-model="batchForm.targetCategoryId" placeholder="请选择分类">
              <t-option v-for="category in categories" :key="category.id" :value="category.id" :label="category.categoryName" />
            </t-select>
          </t-form-item>
          <t-form-item v-if="batchForm.operation === 'updateStatus'" label="目标状态" name="targetStatus">
            <t-select v-model="batchForm.targetStatus" placeholder="请选择状态">
              <t-option value="0" label="启用" />
              <t-option value="1" label="禁用" />
            </t-select>
          </t-form-item>
        </t-form>
        <t-alert theme="info" message={`已选择 ${selectedRowKeys.length} 个标签`} />
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { 
  getCategoryList,
  createCategory,
  updateCategory,
  deleteCategory,
  getEnabledCategories,
  getTagList, 
  createTag, 
  updateTag, 
  deleteTag, 
  getTagById,
  getTagsByCategory,
  getPopularTags,
  getTagStatistics,
  getAssetsByTag,
  exportTags
} from '@/api/equipment/tag';

const COLUMNS = [
  { colKey: 'serial-number', title: '序号', width: 80 },
  { colKey: 'tagName', title: '标签名称', width: 150 },
  { colKey: 'tagColor', title: '标签颜色', width: 120 },
  { colKey: 'categoryName', title: '所属分类', width: 120 },
  { colKey: 'description', title: '描述', ellipsis: true },
  { colKey: 'usageCount', title: '使用数量', width: 100 },
  { colKey: 'status', title: '状态', width: 80 },
  { colKey: 'sortOrder', title: '排序', width: 80 },
  { colKey: 'createDate', title: '创建时间', width: 160 },
  { colKey: 'op', title: '操作', width: 200, fixed: 'right' },
];

const USAGE_COLUMNS = [
  { colKey: 'assetCode', title: '设备编码', width: 150 },
  { colKey: 'assetName', title: '设备名称', width: 200 },
  { colKey: 'departmentName', title: '所属科室', width: 150 },
  { colKey: 'tagDate', title: '标记时间', width: 160 },
];

const data = ref([]);
const categoryTree = ref([]);
const categories = ref([]);
const popularTags = ref([]);
const selectedCategoryId = ref('');
const selectedRowKeys = ref([]);

const pagination = ref({
  defaultPageSize: 10,
  total: 0,
  defaultCurrent: 1,
});

const searchForm = reactive({
  tagName: '',
  tagColor: '',
  status: '',
  categoryId: '',
});

const dataLoading = ref(false);
const dialogVisible = ref(false);
const viewDialogVisible = ref(false);
const categoryDialogVisible = ref(false);
const usageDialogVisible = ref(false);
const statisticsDialogVisible = ref(false);
const batchDialogVisible = ref(false);
const confirmLoading = ref(false);
const categoryConfirmLoading = ref(false);
const batchLoading = ref(false);
const isEdit = ref(false);
const isCategoryEdit = ref(false);

const dialogTitle = computed(() => (isEdit.value ? '编辑标签' : '新增标签'));
const categoryDialogTitle = computed(() => (isCategoryEdit.value ? '编辑分类' : '新增分类'));

const formData = reactive({
  id: '',
  tagName: '',
  tagColor: '#1890ff',
  categoryId: '',
  description: '',
  sortOrder: 0,
});

const categoryFormData = reactive({
  id: '',
  categoryName: '',
  description: '',
  sortOrder: 0,
});

const batchForm = reactive({
  operation: 'updateCategory',
  targetCategoryId: '',
  targetStatus: '',
});

const viewData = ref([]);
const usageData = ref([]);
const statisticsData = ref([]);

const rules = {
  tagName: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
  tagColor: [{ required: true, message: '请选择标签颜色', trigger: 'change' }],
  categoryId: [{ required: true, message: '请选择所属分类', trigger: 'change' }],
};

const categoryRules = {
  categoryName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
};

const rowKey = 'id';
const dialogForm = ref();
const categoryForm = ref();
const form = ref();

const getCategoryList = async () => {
  try {
    const response = await getCategoryList({ page: 1, size: 1000 });
    categoryTree.value = response.data.records;
    categories.value = response.data.records;
  } catch (error) {
    console.error('获取分类列表失败:', error);
  }
};

const getList = async () => {
  dataLoading.value = true;
  try {
    const params = {
      page: pagination.value.defaultCurrent,
      size: pagination.value.defaultPageSize,
      categoryId: selectedCategoryId.value,
      ...searchForm,
    };
    const response = await getTagList(params);
    data.value = response.data.records;
    pagination.value.total = response.data.total;
  } catch (error) {
    console.error('获取标签列表失败:', error);
    MessagePlugin.error('获取标签列表失败');
  } finally {
    dataLoading.value = false;
  }
};

const getPopularTagsList = async () => {
  try {
    const response = await getPopularTags(20);
    popularTags.value = response.data;
  } catch (error) {
    console.error('获取热门标签失败:', error);
  }
};

const handleCategorySelect = (value: any) => {
  selectedCategoryId.value = value[0] || '';
  searchForm.categoryId = selectedCategoryId.value;
  handleSearch();
};

const handleAddCategory = () => {
  isCategoryEdit.value = false;
  categoryDialogVisible.value = true;
  resetCategoryForm();
};

const getCategoryOptions = (node: any) => {
  return [
    { content: '编辑', value: 'edit', data: node },
    { content: '删除', value: 'delete', data: node },
  ];
};

const handleCategoryOperation = ({ value, data }: any) => {
  if (value === 'edit') {
    isCategoryEdit.value = true;
    categoryDialogVisible.value = true;
    Object.assign(categoryFormData, data.data);
  } else if (value === 'delete') {
    const confirmDia = DialogPlugin.confirm({
      header: '确认删除',
      body: `确定删除分类"${data.data.categoryName}"吗？`,
      onConfirm: async () => {
        try {
          await deleteCategory(data.data.id);
          MessagePlugin.success('删除成功');
          getCategoryList();
          confirmDia.destroy();
        } catch (error) {
          console.error('删除失败:', error);
          MessagePlugin.error('删除失败');
        }
      },
    });
  }
};

const handleCategoryConfirm = async () => {
  const form = categoryForm.value;
  const valid = await form.validate();
  if (!valid) return false;

  categoryConfirmLoading.value = true;
  try {
    if (isCategoryEdit.value) {
      await updateCategory(categoryFormData);
      MessagePlugin.success('更新成功');
    } else {
      await createCategory(categoryFormData);
      MessagePlugin.success('创建成功');
    }
    categoryDialogVisible.value = false;
    getCategoryList();
  } catch (error) {
    console.error('操作失败:', error);
    MessagePlugin.error('操作失败');
  } finally {
    categoryConfirmLoading.value = false;
  }
};

const handleCategoryCancel = () => {
  categoryDialogVisible.value = false;
  resetCategoryForm();
};

const resetCategoryForm = () => {
  Object.assign(categoryFormData, {
    id: '',
    categoryName: '',
    description: '',
    sortOrder: 0,
  });
};

const handleAdd = () => {
  isEdit.value = false;
  dialogVisible.value = true;
  resetForm();
  if (selectedCategoryId.value) {
    formData.categoryId = selectedCategoryId.value;
  }
};

const handleClickEdit = async (slotProps: any) => {
  isEdit.value = true;
  dialogVisible.value = true;
  try {
    const response = await getTagById(slotProps.row.id);
    Object.assign(formData, response.data);
  } catch (error) {
    console.error('获取标签详情失败:', error);
    MessagePlugin.error('获取标签详情失败');
  }
};

const handleClickView = async (slotProps: any) => {
  viewDialogVisible.value = true;
  try {
    const response = await getTagById(slotProps.row.id);
    const tag = response.data;
    viewData.value = [
      { label: '标签名称', value: tag.tagName },
      { label: '标签颜色', value: tag.tagColor },
      { label: '所属分类', value: tag.categoryName },
      { label: '描述', value: tag.description || '-' },
      { label: '使用数量', value: tag.usageCount || 0 },
      { label: '状态', value: tag.status === '0' ? '启用' : '禁用' },
      { label: '排序', value: tag.sortOrder },
      { label: '创建时间', value: tag.createDate },
    ];
  } catch (error) {
    console.error('获取标签详情失败:', error);
    MessagePlugin.error('获取标签详情失败');
  }
};

const handleClickDelete = (slotProps: any) => {
  const { row } = slotProps;
  const confirmDia = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定删除标签"${row.tagName}"吗？`,
    onConfirm: async () => {
      try {
        await deleteTag(row.id);
        MessagePlugin.success('删除成功');
        getList();
        confirmDia.destroy();
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败');
      }
    },
  });
};

const handleViewUsage = async (row: any) => {
  usageDialogVisible.value = true;
  try {
    const response = await getAssetsByTag(row.id, { page: 1, size: 100 });
    usageData.value = response.data.records;
  } catch (error) {
    console.error('获取使用情况失败:', error);
    MessagePlugin.error('获取使用情况失败');
  }
};

const handleViewStatistics = async () => {
  statisticsDialogVisible.value = true;
  try {
    const response = await getTagStatistics();
    const stats = response.data;
    statisticsData.value = [
      { label: '总标签数', value: stats.totalTags },
      { label: '已使用标签数', value: stats.usedTags },
      { label: '未使用标签数', value: stats.unusedTags },
      { label: '最受欢迎标签', value: stats.mostPopularTag },
      { label: '平均使用率', value: `${stats.averageUsage}%` },
    ];
  } catch (error) {
    console.error('获取统计信息失败:', error);
    MessagePlugin.error('获取统计信息失败');
  }
};

const handleTagClick = (tag: any) => {
  searchForm.tagName = tag.tagName;
  handleSearch();
};

const handleSelectChange = (value: any) => {
  selectedRowKeys.value = value;
};

const handleBatchOperation = () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.error('请选择要操作的标签');
    return;
  }
  batchDialogVisible.value = true;
};

const handleBatchConfirm = async () => {
  // TODO: 实现批量操作
  MessagePlugin.success('批量操作成功');
  batchDialogVisible.value = false;
  selectedRowKeys.value = [];
  getList();
};

const handleImport = () => {
  // TODO: 实现导入功能
  MessagePlugin.info('导入功能开发中');
};

const handleExport = async () => {
  try {
    const response = await exportTags();
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'tags.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    MessagePlugin.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    MessagePlugin.error('导出失败');
  }
};

const getTagTheme = (color: string) => {
  // 根据颜色返回合适的主题
  return 'primary';
};

const handleConfirm = async () => {
  const form = dialogForm.value;
  const valid = await form.validate();
  if (!valid) return false;

  confirmLoading.value = true;
  try {
    if (isEdit.value) {
      await updateTag(formData);
      MessagePlugin.success('更新成功');
    } else {
      await createTag(formData);
      MessagePlugin.success('创建成功');
    }
    dialogVisible.value = false;
    getList();
    getPopularTagsList();
  } catch (error) {
    console.error('操作失败:', error);
    MessagePlugin.error('操作失败');
  } finally {
    confirmLoading.value = false;
  }
};

const handleCancel = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    tagName: '',
    tagColor: '#1890ff',
    categoryId: '',
    description: '',
    sortOrder: 0,
  });
};

const handleSearch = () => {
  pagination.value.defaultCurrent = 1;
  getList();
};

const handleReset = () => {
  Object.assign(searchForm, {
    tagName: '',
    tagColor: '',
    status: '',
    categoryId: '',
  });
  selectedCategoryId.value = '';
  handleSearch();
};

const handleRefresh = () => {
  getList();
  getPopularTagsList();
};

const rehandlePageChange = (curr: any) => {
  pagination.value.defaultCurrent = curr.current;
  pagination.value.defaultPageSize = curr.pageSize;
  getList();
};

const rehandleChange = () => {
  getList();
};

onMounted(() => {
  getCategoryList();
  getList();
  getPopularTagsList();
});
</script>

<style scoped>
.category-card {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.category-title {
  font-weight: 500;
  font-size: 16px;
}

.list-card-container {
  padding: 0;
}

.table-container {
  padding: 0;
}

.table-header {
  padding: 20px;
  background: #fff;
}

.table-header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
}

.operation-container {
  display: flex;
  gap: 12px;
}

.tag-cloud-container {
  padding: 16px 20px;
  background: #f8f9fa;
  margin: 0 20px;
  border-radius: 4px;
}

.tag-cloud-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
  transition: all 0.2s;
}

.tag-item:hover {
  transform: scale(1.05);
}

.tag-color-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-block {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: 1px solid #ddd;
}

.statistics-container {
  padding: 16px;
}

.t-button-link {
  color: #0052d9;
  text-decoration: none;
  margin-right: 16px;
  cursor: pointer;
}

.t-button-link:hover {
  color: #266fe8;
}

.t-button-link:last-child {
  margin-right: 0;
}
</style>