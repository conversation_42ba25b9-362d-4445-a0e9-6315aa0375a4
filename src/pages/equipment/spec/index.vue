<template>
  <div>
    <t-row :gutter="[16, 16]">
      <!-- 左侧：规格分组 -->
      <t-col :span="3">
        <t-card class="group-card">
          <div class="group-header">
            <span class="group-title">规格分组</span>
            <t-button size="small" theme="primary" variant="text" @click="handleAddGroup">
              <template #icon><fr-icon name="add" /></template>
            </t-button>
          </div>
          <t-tree
            :data="groupTree"
            :keys="{ value: 'id', label: 'groupName' }"
            :activable="true"
            :expandAll="true"
            @active="handleGroupSelect"
          >
            <template #operations="{ node }">
              <t-dropdown :options="getGroupOptions(node)" @click="handleGroupOperation">
                <t-button size="small" variant="text" shape="square">
                  <fr-icon name="more" />
                </t-button>
              </t-dropdown>
            </template>
          </t-tree>
        </t-card>
      </t-col>
      
      <!-- 右侧：参数管理 -->
      <t-col :span="9">
        <t-tabs v-model="activeTab" theme="normal">
          <!-- 参数配置标签页 -->
          <t-tab-panel value="params" label="参数配置">
            <t-card class="list-card-container">
              <div class="table-container">
                <div class="table-header">
                  <div class="table-header-inner">
                    <div class="title">规格参数管理</div>
                    <div class="operation-container">
                      <t-button @click="handleAddParam" theme="primary">
                        <template #icon><fr-icon name="add" /></template>
                        新增参数
                      </t-button>
                      <t-button @click="handleRefresh" variant="outline" theme="default">
                        <template #icon><fr-icon name="refresh" /></template>
                        刷新
                      </t-button>
                    </div>
                  </div>
                </div>

                <!-- 查询条件 -->
                <div class="table-header">
                  <t-form ref="form" :data="searchForm" :label-width="80" layout="inline">
                    <t-form-item label="参数名称" name="paramName">
                      <t-input v-model="searchForm.paramName" placeholder="请输入参数名称" clearable />
                    </t-form-item>
                    <t-form-item label="参数类型" name="paramType">
                      <t-select v-model="searchForm.paramType" placeholder="请选择参数类型" clearable>
                        <t-option value="string" label="文本" />
                        <t-option value="number" label="数字" />
                        <t-option value="boolean" label="布尔" />
                        <t-option value="date" label="日期" />
                        <t-option value="enum" label="枚举" />
                      </t-select>
                    </t-form-item>
                    <t-form-item>
                      <t-button theme="primary" @click="handleSearch">搜索</t-button>
                      <t-button variant="outline" theme="default" @click="handleReset">重置</t-button>
                    </t-form-item>
                  </t-form>
                </div>

                <!-- 数据表格 -->
                <t-table
                  :data="paramData"
                  :columns="PARAM_COLUMNS"
                  :row-key="rowKey"
                  vertical-align="top"
                  :hover="true"
                  :pagination="paramPagination"
                  :loading="paramLoading"
                  @page-change="rehandleParamPageChange"
                  @change="rehandleParamChange"
                >
                  <template #paramType="{ row }">
                    <t-tag v-if="row.paramType === 'string'" theme="primary" variant="light">文本</t-tag>
                    <t-tag v-else-if="row.paramType === 'number'" theme="success" variant="light">数字</t-tag>
                    <t-tag v-else-if="row.paramType === 'boolean'" theme="warning" variant="light">布尔</t-tag>
                    <t-tag v-else-if="row.paramType === 'date'" theme="danger" variant="light">日期</t-tag>
                    <t-tag v-else-if="row.paramType === 'enum'" theme="default" variant="light">枚举</t-tag>
                  </template>
                  <template #isRequired="{ row }">
                    <t-tag v-if="row.isRequired" theme="success" variant="light">必填</t-tag>
                    <t-tag v-else theme="default" variant="light">选填</t-tag>
                  </template>
                  <template #op="slotProps">
                    <a class="t-button-link" @click="handleClickViewParam(slotProps)">查看</a>
                    <a class="t-button-link" @click="handleClickEditParam(slotProps)">编辑</a>
                    <a class="t-button-link" @click="handleClickDeleteParam(slotProps)">删除</a>
                  </template>
                </t-table>
              </div>
            </t-card>
          </t-tab-panel>

          <!-- 设备规格标签页 -->
          <t-tab-panel value="specs" label="设备规格">
            <t-card class="list-card-container">
              <div class="table-container">
                <div class="table-header">
                  <div class="table-header-inner">
                    <div class="title">设备规格管理</div>
                    <div class="operation-container">
                      <t-button @click="handleAddSpec" theme="primary">
                        <template #icon><fr-icon name="add" /></template>
                        新增规格
                      </t-button>
                      <t-button @click="handleCompareSpecs" variant="outline" theme="primary">
                        <template #icon><fr-icon name="swap" /></template>
                        规格对比
                      </t-button>
                      <t-button @click="handleImportSpecs" variant="outline" theme="primary">
                        <template #icon><fr-icon name="upload" /></template>
                        导入
                      </t-button>
                      <t-button @click="handleExportSpecs" variant="outline" theme="default">
                        <template #icon><fr-icon name="download" /></template>
                        导出
                      </t-button>
                    </div>
                  </div>
                </div>

                <!-- 查询条件 -->
                <div class="table-header">
                  <t-form ref="specForm" :data="specSearchForm" :label-width="80" layout="inline">
                    <t-form-item label="设备编码" name="assetCode">
                      <t-input v-model="specSearchForm.assetCode" placeholder="请输入设备编码" clearable />
                    </t-form-item>
                    <t-form-item label="设备名称" name="assetName">
                      <t-input v-model="specSearchForm.assetName" placeholder="请输入设备名称" clearable />
                    </t-form-item>
                    <t-form-item>
                      <t-button theme="primary" @click="handleSpecSearch">搜索</t-button>
                      <t-button variant="outline" theme="default" @click="handleSpecReset">重置</t-button>
                    </t-form-item>
                  </t-form>
                </div>

                <!-- 数据表格 -->
                <t-table
                  :data="specData"
                  :columns="SPEC_COLUMNS"
                  :row-key="rowKey"
                  vertical-align="top"
                  :hover="true"
                  :pagination="specPagination"
                  :loading="specLoading"
                  :selected-row-keys="selectedSpecKeys"
                  @select-change="handleSpecSelectChange"
                  @page-change="rehandleSpecPageChange"
                  @change="rehandleSpecChange"
                >
                  <template #op="slotProps">
                    <a class="t-button-link" @click="handleClickViewSpec(slotProps)">查看</a>
                    <a class="t-button-link" @click="handleClickEditSpec(slotProps)">编辑</a>
                    <a class="t-button-link" @click="handleViewSpecHistory(slotProps)">历史</a>
                    <a class="t-button-link" @click="handleClickDeleteSpec(slotProps)">删除</a>
                  </template>
                </t-table>
              </div>
            </t-card>
          </t-tab-panel>

          <!-- 规格模板标签页 -->
          <t-tab-panel value="templates" label="规格模板">
            <t-card class="list-card-container">
              <div class="table-container">
                <div class="table-header">
                  <div class="table-header-inner">
                    <div class="title">规格模板管理</div>
                    <div class="operation-container">
                      <t-button @click="handleAddTemplate" theme="primary">
                        <template #icon><fr-icon name="add" /></template>
                        新增模板
                      </t-button>
                      <t-button @click="handleExportTemplate" variant="outline" theme="default">
                        <template #icon><fr-icon name="download" /></template>
                        导出模板
                      </t-button>
                    </div>
                  </div>
                </div>

                <!-- 数据表格 -->
                <t-table
                  :data="templateData"
                  :columns="TEMPLATE_COLUMNS"
                  :row-key="rowKey"
                  vertical-align="top"
                  :hover="true"
                  :pagination="templatePagination"
                  :loading="templateLoading"
                  @page-change="rehandleTemplatePageChange"
                  @change="rehandleTemplateChange"
                >
                  <template #op="slotProps">
                    <a class="t-button-link" @click="handleClickViewTemplate(slotProps)">查看</a>
                    <a class="t-button-link" @click="handleClickEditTemplate(slotProps)">编辑</a>
                    <a class="t-button-link" @click="handleApplyTemplate(slotProps)">应用</a>
                    <a class="t-button-link" @click="handleClickDeleteTemplate(slotProps)">删除</a>
                  </template>
                </t-table>
              </div>
            </t-card>
          </t-tab-panel>
        </t-tabs>
      </t-col>
    </t-row>

    <!-- 新增/编辑分组对话框 -->
    <t-dialog
      :visible="groupDialogVisible"
      :header="groupDialogTitle"
      width="500px"
      :on-cancel="handleGroupCancel"
      :on-confirm="handleGroupConfirm"
      :confirm-loading="groupConfirmLoading"
    >
      <template #body>
        <t-form ref="groupForm" :data="groupFormData" :rules="groupRules" :label-width="100">
          <t-form-item label="分组名称" name="groupName">
            <t-input v-model="groupFormData.groupName" placeholder="请输入分组名称" />
          </t-form-item>
          <t-form-item label="上级分组" name="parentId">
            <t-tree-select
              v-model="groupFormData.parentId"
              :data="groupTree"
              :keys="{ value: 'id', label: 'groupName' }"
              placeholder="请选择上级分组"
              clearable
            />
          </t-form-item>
          <t-form-item label="分组描述" name="description">
            <t-textarea v-model="groupFormData.description" placeholder="请输入分组描述" :rows="3" />
          </t-form-item>
          <t-form-item label="排序" name="sortOrder">
            <t-input-number v-model="groupFormData.sortOrder" :min="0" />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 新增/编辑参数对话框 -->
    <t-dialog
      :visible="paramDialogVisible"
      :header="paramDialogTitle"
      width="600px"
      :on-cancel="handleParamCancel"
      :on-confirm="handleParamConfirm"
      :confirm-loading="paramConfirmLoading"
    >
      <template #body>
        <t-form ref="paramDialogForm" :data="paramFormData" :rules="paramRules" :label-width="120">
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="参数名称" name="paramName">
                <t-input v-model="paramFormData.paramName" placeholder="请输入参数名称" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="参数单位" name="paramUnit">
                <t-input v-model="paramFormData.paramUnit" placeholder="请输入参数单位" />
              </t-form-item>
            </t-col>
          </t-row>
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="参数类型" name="paramType">
                <t-select v-model="paramFormData.paramType" placeholder="请选择参数类型">
                  <t-option value="string" label="文本" />
                  <t-option value="number" label="数字" />
                  <t-option value="boolean" label="布尔" />
                  <t-option value="date" label="日期" />
                  <t-option value="enum" label="枚举" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="是否必填" name="isRequired">
                <t-switch v-model="paramFormData.isRequired" />
              </t-form-item>
            </t-col>
          </t-row>
          <t-form-item label="参数描述" name="description">
            <t-textarea v-model="paramFormData.description" placeholder="请输入参数描述" :rows="2" />
          </t-form-item>
          <t-form-item v-if="paramFormData.paramType === 'enum'" label="枚举选项" name="enumOptions">
            <t-textarea v-model="paramFormData.enumOptions" placeholder="请输入枚举选项，每行一个" :rows="3" />
          </t-form-item>
          <t-form-item label="验证规则" name="validationRules">
            <t-textarea v-model="paramFormData.validationRules" placeholder="请输入验证规则(JSON格式)" :rows="2" />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 新增/编辑设备规格对话框 -->
    <t-dialog
      :visible="specDialogVisible"
      :header="specDialogTitle"
      width="800px"
      :on-cancel="handleSpecCancel"
      :on-confirm="handleSpecConfirm"
      :confirm-loading="specConfirmLoading"
    >
      <template #body>
        <t-form ref="specDialogForm" :data="specFormData" :rules="specRules" :label-width="120">
          <t-form-item label="设备" name="assetId">
            <!-- TODO: 添加设备选择组件 -->
            <t-input v-model="specFormData.assetId" placeholder="请选择设备" />
          </t-form-item>
          
          <t-divider>规格参数</t-divider>
          <div class="spec-params-container">
            <t-table
              :data="specFormData.params"
              :columns="SPEC_PARAM_COLUMNS"
              row-key="paramId"
              size="small"
              max-height="400"
            >
              <template #paramValue="{ row, rowIndex }">
                <t-input v-if="row.paramType === 'string'" v-model="row.paramValue" size="small" />
                <t-input-number v-else-if="row.paramType === 'number'" v-model="row.paramValue" size="small" />
                <t-switch v-else-if="row.paramType === 'boolean'" v-model="row.paramValue" size="small" />
                <t-date-picker v-else-if="row.paramType === 'date'" v-model="row.paramValue" size="small" />
                <t-select v-else-if="row.paramType === 'enum'" v-model="row.paramValue" size="small">
                  <t-option v-for="option in getEnumOptions(row)" :key="option" :value="option" :label="option" />
                </t-select>
              </template>
            </t-table>
          </div>
        </t-form>
      </template>
    </t-dialog>

    <!-- 规格对比对话框 -->
    <t-dialog
      :visible="compareDialogVisible"
      header="规格对比"
      width="1000px"
      :footer="false"
      :on-cancel="() => { compareDialogVisible = false }"
    >
      <template #body>
        <div class="compare-container">
          <t-table :data="compareData" :columns="compareColumns" />
        </div>
      </template>
    </t-dialog>

    <!-- 查看对话框 -->
    <t-dialog
      :visible="viewDialogVisible"
      :header="viewDialogTitle"
      width="800px"
      :footer="false"
      :on-cancel="() => { viewDialogVisible = false }"
    >
      <template #body>
        <t-descriptions :data="viewData" />
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { 
  getSpecGroupTree,
  createSpecGroup,
  updateSpecGroup,
  deleteSpecGroup,
  getSpecParamList,
  createSpecParam,
  updateSpecParam,
  deleteSpecParam,
  getParamsByGroup,
  getAssetSpecList,
  createAssetSpec,
  updateAssetSpec,
  deleteAssetSpec,
  compareAssetSpecs,
  getSpecTemplateList,
  createSpecTemplate,
  updateSpecTemplate,
  deleteSpecTemplate,
  exportSpecTemplate,
  exportSpecs
} from '@/api/equipment/spec';

const PARAM_COLUMNS = [
  { colKey: 'paramName', title: '参数名称', width: 150 },
  { colKey: 'paramUnit', title: '单位', width: 80 },
  { colKey: 'paramType', title: '类型', width: 100 },
  { colKey: 'isRequired', title: '必填', width: 80 },
  { colKey: 'description', title: '描述', ellipsis: true },
  { colKey: 'sortOrder', title: '排序', width: 80 },
  { colKey: 'createDate', title: '创建时间', width: 160 },
  { colKey: 'op', title: '操作', width: 150, fixed: 'right' },
];

const SPEC_COLUMNS = [
  { colKey: 'serial-number', title: '序号', width: 80 },
  { colKey: 'assetCode', title: '设备编码', width: 150 },
  { colKey: 'assetName', title: '设备名称', width: 200 },
  { colKey: 'paramCount', title: '参数数量', width: 100 },
  { colKey: 'lastUpdateDate', title: '最后更新', width: 160 },
  { colKey: 'op', title: '操作', width: 200, fixed: 'right' },
];

const TEMPLATE_COLUMNS = [
  { colKey: 'templateName', title: '模板名称', width: 200 },
  { colKey: 'description', title: '描述', ellipsis: true },
  { colKey: 'paramCount', title: '参数数量', width: 100 },
  { colKey: 'usageCount', title: '使用数量', width: 100 },
  { colKey: 'createDate', title: '创建时间', width: 160 },
  { colKey: 'op', title: '操作', width: 200, fixed: 'right' },
];

const SPEC_PARAM_COLUMNS = [
  { colKey: 'paramName', title: '参数名称', width: 150 },
  { colKey: 'paramUnit', title: '单位', width: 80 },
  { colKey: 'paramType', title: '类型', width: 100 },
  { colKey: 'paramValue', title: '参数值', width: 200 },
  { colKey: 'isRequired', title: '必填', width: 80 },
];

const activeTab = ref('params');
const groupTree = ref([]);
const selectedGroupId = ref('');
const selectedSpecKeys = ref([]);

// 参数管理相关状态
const paramData = ref([]);
const paramPagination = ref({ defaultPageSize: 10, total: 0, defaultCurrent: 1 });
const paramLoading = ref(false);

// 设备规格相关状态
const specData = ref([]);
const specPagination = ref({ defaultPageSize: 10, total: 0, defaultCurrent: 1 });
const specLoading = ref(false);

// 模板管理相关状态
const templateData = ref([]);
const templatePagination = ref({ defaultPageSize: 10, total: 0, defaultCurrent: 1 });
const templateLoading = ref(false);

const searchForm = reactive({
  paramName: '',
  paramType: '',
  groupId: '',
});

const specSearchForm = reactive({
  assetCode: '',
  assetName: '',
});

// 对话框状态
const groupDialogVisible = ref(false);
const paramDialogVisible = ref(false);
const specDialogVisible = ref(false);
const compareDialogVisible = ref(false);
const viewDialogVisible = ref(false);
const groupConfirmLoading = ref(false);
const paramConfirmLoading = ref(false);
const specConfirmLoading = ref(false);
const isGroupEdit = ref(false);
const isParamEdit = ref(false);
const isSpecEdit = ref(false);

const groupDialogTitle = computed(() => (isGroupEdit.value ? '编辑分组' : '新增分组'));
const paramDialogTitle = computed(() => (isParamEdit.value ? '编辑参数' : '新增参数'));
const specDialogTitle = computed(() => (isSpecEdit.value ? '编辑规格' : '新增规格'));
const viewDialogTitle = ref('');

const groupFormData = reactive({
  id: '',
  groupName: '',
  parentId: '',
  description: '',
  sortOrder: 0,
});

const paramFormData = reactive({
  id: '',
  paramName: '',
  paramUnit: '',
  paramType: 'string',
  groupId: '',
  isRequired: false,
  description: '',
  enumOptions: '',
  validationRules: '',
  sortOrder: 0,
});

const specFormData = reactive({
  id: '',
  assetId: '',
  params: [] as any[],
});

const viewData = ref([]);
const compareData = ref([]);
const compareColumns = ref([]);

const groupRules = {
  groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
};

const paramRules = {
  paramName: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
  paramType: [{ required: true, message: '请选择参数类型', trigger: 'change' }],
};

const specRules = {
  assetId: [{ required: true, message: '请选择设备', trigger: 'change' }],
};

const rowKey = 'id';
const groupForm = ref();
const paramDialogForm = ref();
const specDialogForm = ref();
const form = ref();
const specForm = ref();

// 获取分组树
const getGroupTree = async () => {
  try {
    const response = await getSpecGroupTree();
    groupTree.value = response.data;
  } catch (error) {
    console.error('获取分组树失败:', error);
  }
};

// 获取参数列表
const getParamList = async () => {
  paramLoading.value = true;
  try {
    const params = {
      page: paramPagination.value.defaultCurrent,
      size: paramPagination.value.defaultPageSize,
      groupId: selectedGroupId.value,
      ...searchForm,
    };
    const response = await getSpecParamList(params);
    paramData.value = response.data.records;
    paramPagination.value.total = response.data.total;
  } catch (error) {
    console.error('获取参数列表失败:', error);
    MessagePlugin.error('获取参数列表失败');
  } finally {
    paramLoading.value = false;
  }
};

// 获取设备规格列表
const getSpecList = async () => {
  specLoading.value = true;
  try {
    const params = {
      page: specPagination.value.defaultCurrent,
      size: specPagination.value.defaultPageSize,
      ...specSearchForm,
    };
    const response = await getAssetSpecList(params);
    specData.value = response.data.records;
    specPagination.value.total = response.data.total;
  } catch (error) {
    console.error('获取设备规格列表失败:', error);
    MessagePlugin.error('获取设备规格列表失败');
  } finally {
    specLoading.value = false;
  }
};

// 获取模板列表
const getTemplateList = async () => {
  templateLoading.value = true;
  try {
    const params = {
      page: templatePagination.value.defaultCurrent,
      size: templatePagination.value.defaultPageSize,
    };
    const response = await getSpecTemplateList(params);
    templateData.value = response.data.records;
    templatePagination.value.total = response.data.total;
  } catch (error) {
    console.error('获取模板列表失败:', error);
    MessagePlugin.error('获取模板列表失败');
  } finally {
    templateLoading.value = false;
  }
};

// 分组相关操作
const handleGroupSelect = (value: any) => {
  selectedGroupId.value = value[0] || '';
  searchForm.groupId = selectedGroupId.value;
  if (activeTab.value === 'params') {
    handleSearch();
  }
};

const handleAddGroup = () => {
  isGroupEdit.value = false;
  groupDialogVisible.value = true;
  resetGroupForm();
};

const getGroupOptions = (node: any) => {
  return [
    { content: '编辑', value: 'edit', data: node },
    { content: '删除', value: 'delete', data: node },
  ];
};

const handleGroupOperation = ({ value, data }: any) => {
  if (value === 'edit') {
    isGroupEdit.value = true;
    groupDialogVisible.value = true;
    Object.assign(groupFormData, data.data);
  } else if (value === 'delete') {
    const confirmDia = DialogPlugin.confirm({
      header: '确认删除',
      body: `确定删除分组"${data.data.groupName}"吗？`,
      onConfirm: async () => {
        try {
          await deleteSpecGroup(data.data.id);
          MessagePlugin.success('删除成功');
          getGroupTree();
          confirmDia.destroy();
        } catch (error) {
          console.error('删除失败:', error);
          MessagePlugin.error('删除失败');
        }
      },
    });
  }
};

const handleGroupConfirm = async () => {
  const form = groupForm.value;
  const valid = await form.validate();
  if (!valid) return false;

  groupConfirmLoading.value = true;
  try {
    if (isGroupEdit.value) {
      await updateSpecGroup(groupFormData);
      MessagePlugin.success('更新成功');
    } else {
      await createSpecGroup(groupFormData);
      MessagePlugin.success('创建成功');
    }
    groupDialogVisible.value = false;
    getGroupTree();
  } catch (error) {
    console.error('操作失败:', error);
    MessagePlugin.error('操作失败');
  } finally {
    groupConfirmLoading.value = false;
  }
};

const handleGroupCancel = () => {
  groupDialogVisible.value = false;
  resetGroupForm();
};

const resetGroupForm = () => {
  Object.assign(groupFormData, {
    id: '',
    groupName: '',
    parentId: '',
    description: '',
    sortOrder: 0,
  });
};

// 参数相关操作
const handleAddParam = () => {
  if (!selectedGroupId.value) {
    MessagePlugin.error('请先选择分组');
    return;
  }
  isParamEdit.value = false;
  paramDialogVisible.value = true;
  resetParamForm();
  paramFormData.groupId = selectedGroupId.value;
};

const handleClickEditParam = async (slotProps: any) => {
  isParamEdit.value = true;
  paramDialogVisible.value = true;
  Object.assign(paramFormData, slotProps.row);
};

const handleClickViewParam = async (slotProps: any) => {
  viewDialogVisible.value = true;
  viewDialogTitle.value = '参数详情';
  const param = slotProps.row;
  viewData.value = [
    { label: '参数名称', value: param.paramName },
    { label: '参数单位', value: param.paramUnit || '-' },
    { label: '参数类型', value: param.paramType },
    { label: '是否必填', value: param.isRequired ? '是' : '否' },
    { label: '描述', value: param.description || '-' },
    { label: '排序', value: param.sortOrder },
    { label: '创建时间', value: param.createDate },
  ];
};

const handleClickDeleteParam = (slotProps: any) => {
  const { row } = slotProps;
  const confirmDia = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定删除参数"${row.paramName}"吗？`,
    onConfirm: async () => {
      try {
        await deleteSpecParam(row.id);
        MessagePlugin.success('删除成功');
        getParamList();
        confirmDia.destroy();
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败');
      }
    },
  });
};

const handleParamConfirm = async () => {
  const form = paramDialogForm.value;
  const valid = await form.validate();
  if (!valid) return false;

  paramConfirmLoading.value = true;
  try {
    if (isParamEdit.value) {
      await updateSpecParam(paramFormData);
      MessagePlugin.success('更新成功');
    } else {
      await createSpecParam(paramFormData);
      MessagePlugin.success('创建成功');
    }
    paramDialogVisible.value = false;
    getParamList();
  } catch (error) {
    console.error('操作失败:', error);
    MessagePlugin.error('操作失败');
  } finally {
    paramConfirmLoading.value = false;
  }
};

const handleParamCancel = () => {
  paramDialogVisible.value = false;
  resetParamForm();
};

const resetParamForm = () => {
  Object.assign(paramFormData, {
    id: '',
    paramName: '',
    paramUnit: '',
    paramType: 'string',
    groupId: '',
    isRequired: false,
    description: '',
    enumOptions: '',
    validationRules: '',
    sortOrder: 0,
  });
};

// 设备规格相关操作
const handleAddSpec = () => {
  isSpecEdit.value = false;
  specDialogVisible.value = true;
  resetSpecForm();
  loadSpecParams();
};

const handleClickEditSpec = async (slotProps: any) => {
  isSpecEdit.value = true;
  specDialogVisible.value = true;
  Object.assign(specFormData, slotProps.row);
  loadSpecParams();
};

const handleClickViewSpec = async (slotProps: any) => {
  viewDialogVisible.value = true;
  viewDialogTitle.value = '设备规格详情';
  // TODO: 加载设备规格详情
};

const handleClickDeleteSpec = (slotProps: any) => {
  const { row } = slotProps;
  const confirmDia = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定删除设备"${row.assetName}"的规格信息吗？`,
    onConfirm: async () => {
      try {
        await deleteAssetSpec(row.id);
        MessagePlugin.success('删除成功');
        getSpecList();
        confirmDia.destroy();
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败');
      }
    },
  });
};

const handleViewSpecHistory = (slotProps: any) => {
  // TODO: 实现规格历史查看
  MessagePlugin.info('规格历史功能开发中');
};

const handleSpecSelectChange = (value: any) => {
  selectedSpecKeys.value = value;
};

const handleCompareSpecs = async () => {
  if (selectedSpecKeys.value.length < 2) {
    MessagePlugin.error('请选择至少2个设备进行对比');
    return;
  }
  
  try {
    const response = await compareAssetSpecs(selectedSpecKeys.value);
    compareData.value = response.data.compareData;
    compareColumns.value = response.data.columns;
    compareDialogVisible.value = true;
  } catch (error) {
    console.error('规格对比失败:', error);
    MessagePlugin.error('规格对比失败');
  }
};

const handleImportSpecs = () => {
  // TODO: 实现导入功能
  MessagePlugin.info('导入功能开发中');
};

const handleExportSpecs = async () => {
  try {
    const assetIds = selectedSpecKeys.value.length > 0 ? selectedSpecKeys.value : undefined;
    const response = await exportSpecs(assetIds);
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'specs.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    MessagePlugin.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    MessagePlugin.error('导出失败');
  }
};

const loadSpecParams = async () => {
  if (selectedGroupId.value) {
    try {
      const response = await getParamsByGroup(selectedGroupId.value);
      specFormData.params = response.data.map((param: any) => ({
        ...param,
        paramValue: '',
      }));
    } catch (error) {
      console.error('加载参数失败:', error);
    }
  }
};

const getEnumOptions = (row: any) => {
  if (row.enumOptions) {
    return row.enumOptions.split('\n').filter((opt: string) => opt.trim());
  }
  return [];
};

const handleSpecConfirm = async () => {
  const form = specDialogForm.value;
  const valid = await form.validate();
  if (!valid) return false;

  specConfirmLoading.value = true;
  try {
    if (isSpecEdit.value) {
      await updateAssetSpec(specFormData);
      MessagePlugin.success('更新成功');
    } else {
      await createAssetSpec(specFormData);
      MessagePlugin.success('创建成功');
    }
    specDialogVisible.value = false;
    getSpecList();
  } catch (error) {
    console.error('操作失败:', error);
    MessagePlugin.error('操作失败');
  } finally {
    specConfirmLoading.value = false;
  }
};

const handleSpecCancel = () => {
  specDialogVisible.value = false;
  resetSpecForm();
};

const resetSpecForm = () => {
  Object.assign(specFormData, {
    id: '',
    assetId: '',
    params: [],
  });
};

// 模板相关操作
const handleAddTemplate = () => {
  // TODO: 实现模板新增
  MessagePlugin.info('模板管理功能开发中');
};

const handleClickEditTemplate = (slotProps: any) => {
  // TODO: 实现模板编辑
  MessagePlugin.info('模板管理功能开发中');
};

const handleClickViewTemplate = (slotProps: any) => {
  // TODO: 实现模板查看
  MessagePlugin.info('模板管理功能开发中');
};

const handleClickDeleteTemplate = (slotProps: any) => {
  // TODO: 实现模板删除
  MessagePlugin.info('模板管理功能开发中');
};

const handleApplyTemplate = (slotProps: any) => {
  // TODO: 实现模板应用
  MessagePlugin.info('模板管理功能开发中');
};

const handleExportTemplate = async () => {
  try {
    const response = await exportSpecTemplate();
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'spec_template.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    MessagePlugin.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    MessagePlugin.error('导出失败');
  }
};

// 搜索相关
const handleSearch = () => {
  paramPagination.value.defaultCurrent = 1;
  getParamList();
};

const handleReset = () => {
  Object.assign(searchForm, {
    paramName: '',
    paramType: '',
    groupId: '',
  });
  handleSearch();
};

const handleSpecSearch = () => {
  specPagination.value.defaultCurrent = 1;
  getSpecList();
};

const handleSpecReset = () => {
  Object.assign(specSearchForm, {
    assetCode: '',
    assetName: '',
  });
  handleSpecSearch();
};

const handleRefresh = () => {
  if (activeTab.value === 'params') {
    getParamList();
  } else if (activeTab.value === 'specs') {
    getSpecList();
  } else if (activeTab.value === 'templates') {
    getTemplateList();
  }
};

// 分页相关
const rehandleParamPageChange = (curr: any) => {
  paramPagination.value.defaultCurrent = curr.current;
  paramPagination.value.defaultPageSize = curr.pageSize;
  getParamList();
};

const rehandleParamChange = () => {
  getParamList();
};

const rehandleSpecPageChange = (curr: any) => {
  specPagination.value.defaultCurrent = curr.current;
  specPagination.value.defaultPageSize = curr.pageSize;
  getSpecList();
};

const rehandleSpecChange = () => {
  getSpecList();
};

const rehandleTemplatePageChange = (curr: any) => {
  templatePagination.value.defaultCurrent = curr.current;
  templatePagination.value.defaultPageSize = curr.pageSize;
  getTemplateList();
};

const rehandleTemplateChange = () => {
  getTemplateList();
};

// 监听标签页变化
watch(activeTab, (newTab) => {
  if (newTab === 'params') {
    getParamList();
  } else if (newTab === 'specs') {
    getSpecList();
  } else if (newTab === 'templates') {
    getTemplateList();
  }
});

onMounted(() => {
  getGroupTree();
  getParamList();
});
</script>

<style scoped>
.group-card {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.group-title {
  font-weight: 500;
  font-size: 16px;
}

.list-card-container {
  padding: 0;
}

.table-container {
  padding: 0;
}

.table-header {
  padding: 20px;
  background: #fff;
}

.table-header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
}

.operation-container {
  display: flex;
  gap: 12px;
}

.spec-params-container {
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  padding: 16px;
}

.compare-container {
  max-height: 500px;
  overflow-y: auto;
}

.t-button-link {
  color: #0052d9;
  text-decoration: none;
  margin-right: 16px;
  cursor: pointer;
}

.t-button-link:hover {
  color: #266fe8;
}

.t-button-link:last-child {
  margin-right: 0;
}
</style>