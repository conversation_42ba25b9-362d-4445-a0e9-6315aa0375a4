<template>
  <div>
    <t-card class="list-card-container">
      <div class="table-container">
        <div class="table-header">
          <div class="table-header-inner">
            <div class="title">工单管理</div>
            <div class="operation-container">
              <t-button @click="handleAdd" theme="primary">
                <template #icon><fr-icon name="add" /></template>
                新增工单
              </t-button>
              <t-button @click="handleViewTimeout" variant="outline" theme="warning">
                <template #icon><fr-icon name="time" /></template>
                超时工单
              </t-button>
              <t-button @click="handleRefresh" variant="outline" theme="default">
                <template #icon><fr-icon name="refresh" /></template>
                刷新
              </t-button>
            </div>
          </div>
        </div>

        <!-- 查询条件 -->
        <div class="table-header">
          <t-form ref="form" :data="searchForm" :label-width="80" layout="inline">
            <t-form-item label="工单编码" name="orderCode">
              <t-input v-model="searchForm.orderCode" placeholder="请输入工单编码" clearable />
            </t-form-item>
            <t-form-item label="工单标题" name="orderTitle">
              <t-input v-model="searchForm.orderTitle" placeholder="请输入工单标题" clearable />
            </t-form-item>
            <t-form-item label="工单类型" name="orderType">
              <t-select v-model="searchForm.orderType" placeholder="请选择工单类型" clearable>
                <t-option value="REPAIR" label="维修" />
                <t-option value="MAINTENANCE" label="保养" />
                <t-option value="INSPECTION" label="点检" />
                <t-option value="PATROL" label="巡检" />
                <t-option value="OTHER" label="其他" />
              </t-select>
            </t-form-item>
            <t-form-item label="工单状态" name="orderStatus">
              <t-select v-model="searchForm.orderStatus" placeholder="请选择工单状态" clearable>
                <t-option value="PENDING" label="待处理" />
                <t-option value="PROCESSING" label="处理中" />
                <t-option value="COMPLETED" label="已完成" />
                <t-option value="CANCELLED" label="已取消" />
              </t-select>
            </t-form-item>
            <t-form-item label="优先级" name="priority">
              <t-select v-model="searchForm.priority" placeholder="请选择优先级" clearable>
                <t-option value="LOW" label="低" />
                <t-option value="NORMAL" label="普通" />
                <t-option value="HIGH" label="高" />
                <t-option value="URGENT" label="紧急" />
              </t-select>
            </t-form-item>
            <t-form-item>
              <t-button theme="primary" @click="handleSearch">搜索</t-button>
              <t-button variant="outline" theme="default" @click="handleReset">重置</t-button>
            </t-form-item>
          </t-form>
        </div>

        <!-- 数据表格 -->
        <t-table
          :data="data"
          :columns="COLUMNS"
          :row-key="rowKey"
          vertical-align="top"
          :hover="true"
          :pagination="pagination"
          :loading="dataLoading"
          @page-change="rehandlePageChange"
          @change="rehandleChange"
        >
          <template #orderType="{ row }">
            <t-tag v-if="row.orderType === 'REPAIR'" theme="danger" variant="light">维修</t-tag>
            <t-tag v-else-if="row.orderType === 'MAINTENANCE'" theme="warning" variant="light">保养</t-tag>
            <t-tag v-else-if="row.orderType === 'INSPECTION'" theme="primary" variant="light">点检</t-tag>
            <t-tag v-else-if="row.orderType === 'PATROL'" theme="success" variant="light">巡检</t-tag>
            <t-tag v-else theme="default" variant="light">其他</t-tag>
          </template>
          <template #orderStatus="{ row }">
            <t-tag v-if="row.orderStatus === 'PENDING'" theme="default" variant="light">待处理</t-tag>
            <t-tag v-else-if="row.orderStatus === 'PROCESSING'" theme="primary" variant="light">处理中</t-tag>
            <t-tag v-else-if="row.orderStatus === 'COMPLETED'" theme="success" variant="light">已完成</t-tag>
            <t-tag v-else-if="row.orderStatus === 'CANCELLED'" theme="warning" variant="light">已取消</t-tag>
          </template>
          <template #priority="{ row }">
            <t-tag v-if="row.priority === 'LOW'" theme="default" variant="light">低</t-tag>
            <t-tag v-else-if="row.priority === 'NORMAL'" theme="primary" variant="light">普通</t-tag>
            <t-tag v-else-if="row.priority === 'HIGH'" theme="warning" variant="light">高</t-tag>
            <t-tag v-else-if="row.priority === 'URGENT'" theme="danger" variant="light">紧急</t-tag>
          </template>
          <template #op="slotProps">
            <a class="t-button-link" @click="handleClickView(slotProps)">查看</a>
            <a v-if="slotProps.row.orderStatus === 'PENDING'" class="t-button-link" @click="handleClickEdit(slotProps)">编辑</a>
            <a v-if="slotProps.row.orderStatus === 'PENDING'" class="t-button-link" @click="handleAssign(slotProps)">分配</a>
            <a v-if="slotProps.row.orderStatus === 'PENDING'" class="t-button-link" @click="handleStart(slotProps)">开始</a>
            <a v-if="slotProps.row.orderStatus === 'PROCESSING'" class="t-button-link" @click="handleComplete(slotProps)">完成</a>
            <a v-if="['PENDING', 'PROCESSING'].includes(slotProps.row.orderStatus)" class="t-button-link" @click="handleCancel(slotProps)">取消</a>
            <a v-if="['PENDING', 'PROCESSING'].includes(slotProps.row.orderStatus)" class="t-button-link" @click="handleUrge(slotProps)">催办</a>
            <a v-if="['PENDING', 'CANCELLED'].includes(slotProps.row.orderStatus)" class="t-button-link" @click="handleClickDelete(slotProps)">删除</a>
          </template>
        </t-table>
      </div>
    </t-card>

    <!-- 新增/编辑对话框 -->
    <t-dialog
      :visible="dialogVisible"
      :header="dialogTitle"
      width="800px"
      :on-cancel="handleCancel"
      :on-confirm="handleConfirm"
      :confirm-loading="confirmLoading"
    >
      <template #body>
        <t-form ref="dialogForm" :data="formData" :rules="rules" :label-width="120">
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="工单编码" name="orderCode">
                <t-input v-model="formData.orderCode" placeholder="留空自动生成" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="工单标题" name="orderTitle">
                <t-input v-model="formData.orderTitle" placeholder="请输入工单标题" />
              </t-form-item>
            </t-col>
          </t-row>
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="工单类型" name="orderType">
                <t-select v-model="formData.orderType" placeholder="请选择工单类型">
                  <t-option value="REPAIR" label="维修" />
                  <t-option value="MAINTENANCE" label="保养" />
                  <t-option value="INSPECTION" label="点检" />
                  <t-option value="PATROL" label="巡检" />
                  <t-option value="OTHER" label="其他" />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="优先级" name="priority">
                <t-select v-model="formData.priority" placeholder="请选择优先级">
                  <t-option value="LOW" label="低" />
                  <t-option value="NORMAL" label="普通" />
                  <t-option value="HIGH" label="高" />
                  <t-option value="URGENT" label="紧急" />
                </t-select>
              </t-form-item>
            </t-col>
          </t-row>
          <t-row :gutter="[16, 16]">
            <t-col :span="6">
              <t-form-item label="关联设备" name="equipmentId">
                <t-input v-model="formData.equipmentId" placeholder="请选择设备" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="申请科室" name="departmentId">
                <fr-organ v-model="formData.departmentId" />
              </t-form-item>
            </t-col>
          </t-row>
          <t-form-item label="问题描述" name="description">
            <t-textarea v-model="formData.description" placeholder="请输入问题描述" :rows="4" />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 查看对话框 -->
    <t-dialog
      :visible="viewDialogVisible"
      header="工单详情"
      width="800px"
      :footer="false"
      :on-cancel="() => { viewDialogVisible = false }"
    >
      <template #body>
        <t-descriptions :data="viewData" />
      </template>
    </t-dialog>

    <!-- 分配工单对话框 -->
    <t-dialog
      :visible="assignDialogVisible"
      header="分配工单"
      width="400px"
      :on-cancel="() => { assignDialogVisible = false }"
      :on-confirm="handleAssignConfirm"
      :confirm-loading="assignLoading"
    >
      <template #body>
        <t-form :data="assignForm" :label-width="100">
          <t-form-item label="处理人员" name="assignedUserId">
            <fr-user v-model="assignForm.assignedUserId" @select="handleUserSelect" />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 完成工单对话框 -->
    <t-dialog
      :visible="completeDialogVisible"
      header="完成工单"
      width="500px"
      :on-cancel="() => { completeDialogVisible = false }"
      :on-confirm="handleCompleteConfirm"
      :confirm-loading="completeLoading"
    >
      <template #body>
        <t-form :data="completeForm" :label-width="100">
          <t-form-item label="解决方案" name="solution">
            <t-textarea v-model="completeForm.solution" placeholder="请输入解决方案" :rows="4" />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <!-- 取消工单对话框 -->
    <t-dialog
      :visible="cancelDialogVisible"
      header="取消工单"
      width="500px"
      :on-cancel="() => { cancelDialogVisible = false }"
      :on-confirm="handleCancelConfirm"
      :confirm-loading="cancelLoading"
    >
      <template #body>
        <t-form :data="cancelForm" :label-width="100">
          <t-form-item label="取消原因" name="reason">
            <t-textarea v-model="cancelForm.reason" placeholder="请输入取消原因" :rows="4" />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { 
  getWorkOrderList, 
  createWorkOrder, 
  updateWorkOrder, 
  deleteWorkOrder, 
  getWorkOrderById,
  assignWorkOrder,
  startWorkOrder,
  completeWorkOrder,
  cancelWorkOrder,
  urgeWorkOrder,
  getTimeoutWorkOrders
} from '@/api/equipment/workOrder';

const COLUMNS = [
  { colKey: 'orderCode', title: '工单编码', width: 150 },
  { colKey: 'orderTitle', title: '工单标题', width: 200 },
  { colKey: 'orderType', title: '工单类型', width: 100 },
  { colKey: 'priority', title: '优先级', width: 80 },
  { colKey: 'orderStatus', title: '状态', width: 100 },
  { colKey: 'equipmentName', title: '设备名称', width: 150 },
  { colKey: 'applicantName', title: '申请人', width: 100 },
  { colKey: 'assignedUserName', title: '处理人', width: 100 },
  { colKey: 'applyTime', title: '申请时间', width: 160 },
  { colKey: 'op', title: '操作', width: 320, fixed: 'right' },
];

const data = ref([]);
const pagination = ref({
  defaultPageSize: 10,
  total: 0,
  defaultCurrent: 1,
});

const searchForm = reactive({
  orderCode: '',
  orderTitle: '',
  orderType: '',
  orderStatus: '',
  priority: '',
});

const dataLoading = ref(false);
const dialogVisible = ref(false);
const viewDialogVisible = ref(false);
const assignDialogVisible = ref(false);
const completeDialogVisible = ref(false);
const cancelDialogVisible = ref(false);
const confirmLoading = ref(false);
const assignLoading = ref(false);
const completeLoading = ref(false);
const cancelLoading = ref(false);
const isEdit = ref(false);

const dialogTitle = computed(() => (isEdit.value ? '编辑工单' : '新增工单'));

const formData = reactive({
  id: '',
  orderCode: '',
  orderTitle: '',
  orderType: '',
  equipmentId: '',
  departmentId: '',
  priority: 'NORMAL',
  description: '',
});

const assignForm = reactive({
  orderId: '',
  assignedUserId: '',
  assignedUserName: '',
});

const completeForm = reactive({
  orderId: '',
  solution: '',
});

const cancelForm = reactive({
  orderId: '',
  reason: '',
});

const viewData = ref([]);

const rules = {
  orderTitle: [{ required: true, message: '请输入工单标题', trigger: 'blur' }],
  orderType: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  description: [{ required: true, message: '请输入问题描述', trigger: 'blur' }],
};

const rowKey = 'id';
const dialogForm = ref();
const form = ref();

const getList = async () => {
  dataLoading.value = true;
  try {
    const params = {
      page: pagination.value.defaultCurrent,
      size: pagination.value.defaultPageSize,
      ...searchForm,
    };
    const response = await getWorkOrderList(params);
    data.value = response.data.records;
    pagination.value.total = response.data.total;
  } catch (error) {
    console.error('获取工单列表失败:', error);
    MessagePlugin.error('获取工单列表失败');
  } finally {
    dataLoading.value = false;
  }
};

const handleAdd = () => {
  isEdit.value = false;
  dialogVisible.value = true;
  resetForm();
};

const handleClickEdit = async (slotProps: any) => {
  isEdit.value = true;
  dialogVisible.value = true;
  try {
    const response = await getWorkOrderById(slotProps.row.id);
    Object.assign(formData, response.data);
  } catch (error) {
    console.error('获取工单详情失败:', error);
    MessagePlugin.error('获取工单详情失败');
  }
};

const handleClickView = async (slotProps: any) => {
  viewDialogVisible.value = true;
  try {
    const response = await getWorkOrderById(slotProps.row.id);
    const workOrder = response.data;
    viewData.value = [
      { label: '工单编码', value: workOrder.orderCode },
      { label: '工单标题', value: workOrder.orderTitle },
      { label: '工单类型', value: workOrder.orderType },
      { label: '优先级', value: workOrder.priority },
      { label: '工单状态', value: workOrder.orderStatus },
      { label: '设备名称', value: workOrder.equipmentName || '-' },
      { label: '申请人', value: workOrder.applicantName },
      { label: '处理人', value: workOrder.assignedUserName || '-' },
      { label: '申请时间', value: workOrder.applyTime },
      { label: '分配时间', value: workOrder.assignTime || '-' },
      { label: '开始时间', value: workOrder.startTime || '-' },
      { label: '完成时间', value: workOrder.completeTime || '-' },
      { label: '问题描述', value: workOrder.description || '-' },
      { label: '解决方案', value: workOrder.solution || '-' },
    ];
  } catch (error) {
    console.error('获取工单详情失败:', error);
    MessagePlugin.error('获取工单详情失败');
  }
};

const handleClickDelete = (slotProps: any) => {
  const { row } = slotProps;
  const confirmDia = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定删除工单"${row.orderTitle}"吗？`,
    onConfirm: async () => {
      try {
        await deleteWorkOrder(row.id);
        MessagePlugin.success('删除成功');
        getList();
        confirmDia.destroy();
      } catch (error) {
        console.error('删除失败:', error);
        MessagePlugin.error('删除失败');
      }
    },
  });
};

const handleAssign = (slotProps: any) => {
  assignForm.orderId = slotProps.row.id;
  assignForm.assignedUserId = '';
  assignForm.assignedUserName = '';
  assignDialogVisible.value = true;
};

const handleUserSelect = (user: any) => {
  assignForm.assignedUserName = user.name;
};

const handleAssignConfirm = async () => {
  if (!assignForm.assignedUserId) {
    MessagePlugin.error('请选择处理人员');
    return false;
  }
  
  assignLoading.value = true;
  try {
    await assignWorkOrder(assignForm.orderId, assignForm.assignedUserId, assignForm.assignedUserName);
    MessagePlugin.success('工单分配成功');
    assignDialogVisible.value = false;
    getList();
  } catch (error) {
    console.error('工单分配失败:', error);
    MessagePlugin.error('工单分配失败');
  } finally {
    assignLoading.value = false;
  }
};

const handleStart = async (slotProps: any) => {
  try {
    await startWorkOrder(slotProps.row.id);
    MessagePlugin.success('工单已开始');
    getList();
  } catch (error) {
    console.error('开始工单失败:', error);
    MessagePlugin.error('开始工单失败');
  }
};

const handleComplete = (slotProps: any) => {
  completeForm.orderId = slotProps.row.id;
  completeForm.solution = '';
  completeDialogVisible.value = true;
};

const handleCompleteConfirm = async () => {
  if (!completeForm.solution.trim()) {
    MessagePlugin.error('请输入解决方案');
    return false;
  }
  
  completeLoading.value = true;
  try {
    await completeWorkOrder(completeForm.orderId, completeForm.solution);
    MessagePlugin.success('工单已完成');
    completeDialogVisible.value = false;
    getList();
  } catch (error) {
    console.error('完成工单失败:', error);
    MessagePlugin.error('完成工单失败');
  } finally {
    completeLoading.value = false;
  }
};

const handleCancel = (slotProps: any) => {
  cancelForm.orderId = slotProps.row.id;
  cancelForm.reason = '';
  cancelDialogVisible.value = true;
};

const handleCancelConfirm = async () => {
  if (!cancelForm.reason.trim()) {
    MessagePlugin.error('请输入取消原因');
    return false;
  }
  
  cancelLoading.value = true;
  try {
    await cancelWorkOrder(cancelForm.orderId, cancelForm.reason);
    MessagePlugin.success('工单已取消');
    cancelDialogVisible.value = false;
    getList();
  } catch (error) {
    console.error('取消工单失败:', error);
    MessagePlugin.error('取消工单失败');
  } finally {
    cancelLoading.value = false;
  }
};

const handleUrge = async (slotProps: any) => {
  try {
    await urgeWorkOrder(slotProps.row.id);
    MessagePlugin.success('催办通知已发送');
  } catch (error) {
    console.error('发送催办失败:', error);
    MessagePlugin.error('发送催办失败');
  }
};

const handleViewTimeout = async () => {
  try {
    const response = await getTimeoutWorkOrders(24);
    MessagePlugin.info(`发现${response.data.length}个超时工单`);
    // TODO: 显示超时工单列表
  } catch (error) {
    console.error('获取超时工单失败:', error);
    MessagePlugin.error('获取超时工单失败');
  }
};

const handleConfirm = async () => {
  const form = dialogForm.value;
  const valid = await form.validate();
  if (!valid) return false;

  confirmLoading.value = true;
  try {
    if (isEdit.value) {
      await updateWorkOrder(formData);
      MessagePlugin.success('更新成功');
    } else {
      await createWorkOrder(formData);
      MessagePlugin.success('创建成功');
    }
    dialogVisible.value = false;
    getList();
  } catch (error) {
    console.error('操作失败:', error);
    MessagePlugin.error('操作失败');
  } finally {
    confirmLoading.value = false;
  }
};

const handleCancel = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    orderCode: '',
    orderTitle: '',
    orderType: '',
    equipmentId: '',
    departmentId: '',
    priority: 'NORMAL',
    description: '',
  });
};

const handleSearch = () => {
  pagination.value.defaultCurrent = 1;
  getList();
};

const handleReset = () => {
  Object.assign(searchForm, {
    orderCode: '',
    orderTitle: '',
    orderType: '',
    orderStatus: '',
    priority: '',
  });
  handleSearch();
};

const handleRefresh = () => {
  getList();
};

const rehandlePageChange = (curr: any) => {
  pagination.value.defaultCurrent = curr.current;
  pagination.value.defaultPageSize = curr.pageSize;
  getList();
};

const rehandleChange = () => {
  getList();
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.list-card-container {
  padding: 0;
}

.table-container {
  padding: 0;
}

.table-header {
  padding: 20px;
  background: #fff;
}

.table-header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
}

.operation-container {
  display: flex;
  gap: 12px;
}

.t-button-link {
  color: #0052d9;
  text-decoration: none;
  margin-right: 16px;
  cursor: pointer;
}

.t-button-link:hover {
  color: #266fe8;
}

.t-button-link:last-child {
  margin-right: 0;
}
</style>