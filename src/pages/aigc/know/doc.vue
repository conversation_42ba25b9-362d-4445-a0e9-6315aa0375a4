<template>
  <div>
    <FrQuery
      ref="queryRef"
      v-model:params="params"
      :columns="columns"
      :request="{
        url: '/aigc/vectorFile/list',
        method: 'post',
      }"
      :orders="[
        {
          asc: false,
          column: 'update_time',
        },
      ]"
    >
      <!-- 查询条件 -->
      <template #frSimpleQuery>
        <t-input placeholder="请输入文档名称" v-model="params.title" :style="{ width: '220px' }" />
      </template>
      <!-- 操作按钮 -->
      <template #frSimpleBtn>
        <t-space size="5px">
          <t-button v-if="authAdd" @click="addRow('02')" theme="primary" :variant="'outline'"
            >自定义内容
            <template #icon>
              <AddIcon />
            </template>
          </t-button>
          <t-button v-if="authAdd" @click="addRow('01')"
            >上传附件
            <template #icon>
              <UploadIcon />
            </template>
          </t-button>
        </t-space>
      </template>
      <template #status="{ row }">
        <t-tag
          v-if="row.status !== '-1'"
          :theme="
            row.status === '01'
              ? 'success'
              : row.status === '03'
                ? 'warning'
                : row.status === '-1'
                  ? 'danger'
                  : 'default'
          "
          :variant="'light'"
        >
          {{ dict['SP_AIGC_VECTOR_STATUS']?.map.get(row.status) }}
        </t-tag>
        <t-tooltip v-else :content="row.remark">
          <t-tag style="cursor: pointer" :theme="'danger'" :variant="'light'">
            {{ dict['SP_AIGC_VECTOR_STATUS']?.map.get(row.status) }}
          </t-tag>
        </t-tooltip>
      </template>
      <template #dataSource="{ row }">
        <t-tag :theme="'primary'" :variant="'outline'">
          {{ dict['SP_AIGC_VECTOR_SOURCE']?.map.get(row.dataSource) }}
        </t-tag>
      </template>
      <!-- 操作列 -->
      <template #operation="{ row, rowIndex }">
        <t-space size="4px">
          <t-button v-if="row.status === '01'" size="small" variant="text" theme="primary" @click="viewSlice(row)">
            查看切片
          </t-button>
          <t-popconfirm content="是否确定开始训练?" @confirm="trainDoc(row, rowIndex)">
            <t-button size="small" variant="text" theme="primary" :loading="row.status === '03'">
              {{ row.status === '03' ? '训练中' : row.status === '01' ? '重新训练' : '训练' }}
            </t-button>
          </t-popconfirm>
          <t-button
            v-if="(row.status === '02' || row.status === '-1') && row.dataSource === '02'"
            size="small"
            variant="text"
            theme="primary"
            @click="editRow(row)"
          >
            修改
          </t-button>
          <t-button
            v-if="row.dataSource === '01'"
            size="small"
            variant="text"
            theme="primary"
            @click="downKnowFile(row)"
          >
            下载
          </t-button>
          <t-popconfirm v-if="authDel" content="是否确认删除?" @confirm="delRow(row)">
            <t-button size="small" variant="text" theme="danger"> 删除 </t-button>
          </t-popconfirm>
        </t-space>
      </template>
    </FrQuery>
    <!-- 新增/修改 -->
    <t-dialog
      v-model:visible="visibleModal"
      width="400"
      :closeOnOverlayClick="false"
      :header="'上传附件'"
      mode="modal"
      :destroy-on-close="true"
      :lazy="true"
      draggable
      :confirm-btn="null"
    >
      <template #body>
        <UploadFile
          :size="8"
          :accept="'.docx,.pdf,.txt,.md,.xlsx,.xls'"
          :max="1"
          @close="closeDia"
          :knowId="props.knowId"
        />
      </template>
    </t-dialog>

    <!-- 新增/修改文档 -->
    <t-dialog
      v-model:visible="visibleModal1"
      width="800"
      :closeOnOverlayClick="false"
      :header="opt === 'add' ? '新增' : '修改'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form ref="form" :label-align="'right'" :data="formData" :rules="rules">
          <t-form-item label="标题" name="title">
            <t-input v-model="formData.title" placeholder="请输入标题"></t-input>
          </t-form-item>
          <t-form-item label="内容" name="content">
            <t-textarea
              v-model="formData.content"
              placeholder="请输入内容"
              :maxcharacter="4000"
              :autosize="{ minRows: 15, maxRows: 50 }"
            ></t-textarea>
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>

    <t-dialog
      v-model:visible="visibleModal2"
      width="800"
      :closeOnOverlayClick="false"
      :header="'切片列表'"
      mode="modal"
      draggable
      :confirm-btn="null"
    >
      <template #body>
        <t-loading :loading="sliceLoad">
          <div style="height: 400px; overflow-x: hidden; overflow-y: scroll">
            <t-row :gutter="[10, 10]">
              <t-col :span="4" v-for="(item, index) in sliceList" :key="index">
                <t-textarea
                  v-model="item.content"
                  :maxcharacter="4000"
                  :autosize="{ minRows: 15, maxRows: 15 }"
                  :readonly="true"
                ></t-textarea>
              </t-col>
            </t-row>
          </div>
        </t-loading>
      </template>
    </t-dialog>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListvectorFile',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { publicInterface } from '@/api/common';
import { useUserStore } from '@/store';
import FrQuery from '@/components/fr-query/index.vue';
import { UploadIcon, AddIcon } from 'tdesign-icons-vue-next';
import { getDict } from '@/utils/index';
import UploadFile from './Upload.vue';
import { train, reTrain, listSlice } from '@/api/aigc/know';
import { downLoadKnowFile } from '@/utils/index';

const props = defineProps({
  knowId: {
    type: String,
  },
});
//权限控制
const userStore = useUserStore();
const authAdd = computed(() => userStore.roles.includes('aigc:knowLib:add'));
const authDel = computed(() => userStore.roles.includes('aigc:knowLib:del'));

const queryRef = ref(null);

const dict = ref<DictType>({});
const initDict = async () => {
  dict.value = await getDict('SP_AIGC_VECTOR_SOURCE,SP_AIGC_VECTOR_STATUS');
};

const visibleModal2 = ref(false);
const sliceList = ref([]);
const sliceLoad = ref(false);
const viewSlice = async (row) => {
  sliceLoad.value = true;
  visibleModal2.value = true;
  let res = await listSlice({
    knowId: row.knowId,
    knowFileId: row.id,
  });
  sliceList.value = res.data;
  sliceLoad.value = false;
};

const visibleModal1 = ref(false);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});
const form = ref(null);
const rules = {
  title: [{ required: true, message: '标题必填' }],
  content: [{ required: true, message: '内容必填' }],
} as Rules;

//提交方法
const onSubmit = async () => {
  saveBtn.content = '保存中...';
  saveBtn.loading = true;
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    let submitForm = {
      title: formData.value.title,
      content: formData.value.content,
      id: null,
      dataSource: formData.value.dataSource,
      knowId: props.knowId,
      status: '02',
    };
    if (opt.value === 'add') {
      try {
        let result1 = await publicInterface({
          url: '/aigc/vectorFile/add',
          method: 'post',
          data: submitForm,
        });
        if (result1.code === 0) {
          visibleModal1.value = false;
          queryRef.value.loadData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败:' + error.message);
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      submitForm.id = formData.value.id;
      try {
        let result1 = await publicInterface({
          url: '/aigc/vectorFile/edit',
          method: 'post',
          data: submitForm,
        });
        if (result1.code === 0) {
          visibleModal1.value = false;
          queryRef.value.loadData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败:' + error.message);
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  } else {
    saveBtn.content = '保存';
    saveBtn.loading = false;
  }
};

const closeDia = () => {
  queryRef.value.loadData();
  visibleModal.value = false;
};

const downKnowFile = (row) => {
  downLoadKnowFile(row.id, row.fileName);
};
const trainDoc = async (row, index: number) => {
  let msg = MessagePlugin.loading('开启训练中...');
  try {
    let res;
    if (row.status === '01') {
      res = await reTrain({
        id: row.id,
      });
    } else {
      res = await train({
        id: row.id,
      });
    }
    MessagePlugin.close(msg);
    if (res.code === 0) {
      let data = queryRef.value.getData();
      data[index].status = '03';
      queryRef.value.setData(data);
      MessagePlugin.success('已经开始训练，请等待训练结束');
    } else {
      MessagePlugin.error(res.msg);
    }
  } catch (e) {
    MessagePlugin.close(msg);
    MessagePlugin.error(e.message || '系统异常错误');
  }
};
//新增/修改弹窗start
const visibleModal = ref(false);
const formData = ref({
  id: null,
  title: null,
  sliceNum: null,
  status: null,
  content: null,
  dataSource: null,
});
//新增/修改弹窗end

const columns = [
  {
    width: 150,
    key: 'title',
    colKey: 'title',
    title: '文档名称',
    align: 'center',
    ellipsis: true,
  },
  {
    width: 100,
    key: 'dataSource',
    colKey: 'dataSource',
    title: '文档来源',
    align: 'center',
  },
  {
    width: 100,
    key: 'status',
    colKey: 'status',
    title: '状态',
    align: 'center',
  },
  {
    width: 100,
    key: 'sliceNum',
    colKey: 'sliceNum',
    title: '切片数量',
    align: 'center',
  },
  {
    width: 100,
    key: 'fileSize',
    colKey: 'fileSize',
    title: '文件大小(B)',
    align: 'center',
  },
  {
    width: 120,
    key: 'updateTime',
    colKey: 'updateTime',
    title: '修改时间',
    align: 'center',
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 170,
    cell: 'operation',
    align: 'center',
    fixed: 'right',
  },
] as Columns;
const params = ref({
  knowId: props.knowId,
  title: '',
});
const opt = ref('add');
const addRow = async (type) => {
  formData.value.dataSource = type;
  opt.value = 'add';
  formData.value.id = '';
  if (type === '01') {
    visibleModal.value = true;
  } else {
    visibleModal1.value = true;
  }
};
const editRow = async (row) => {
  opt.value = 'edit';
  formData.value.id = row.id;
  formData.value.title = row.title;
  formData.value.content = row.content;
  formData.value.dataSource = row.dataSource;
  visibleModal1.value = true;
};
const delRow = async (row) => {
  let msg = MessagePlugin.loading('删除中...');
  try {
    let res = await publicInterface({
      url: '/aigc/vectorFile/del',
      urlParam: row.id,
      method: 'delete',
    });
    MessagePlugin.close(msg);
    if (res.code === 0) {
      MessagePlugin.success('删除成功');
      queryRef.value.loadData();
    } else {
      MessagePlugin.error('删除失败：' + res.msg);
    }
  } catch (error) {
    MessagePlugin.close(msg);
    MessagePlugin.error('删除失败:' + error.message);
  }
};

//vue的api
onMounted(() => {
  initDict();
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
</style>
