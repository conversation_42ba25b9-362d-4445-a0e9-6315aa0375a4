<template>
  <div>
    <t-card :bordered="false">
      <FrQuery
        ref="queryRef"
        v-model:params="params"
        :columns="columns"
        :request="{
          url: '/aigc/aiApp/list',
          method: 'get',
        }"
      >
        <!-- 查询条件 -->
        <template #frSimpleQuery>
          <t-input v-model="params.name" :style="{ width: '300px' }" placeholder="请输入应用名称"></t-input>
        </template>
        <!-- 操作按钮 -->
        <template #frSimpleBtn>
          <t-button v-if="authAdd" @click="addRow">添加应用</t-button>
        </template>
        <template #knowNames="{ row }">
          <t-tag v-if="!row.knowNames" variant="light" theme="warning">未关联</t-tag>
          <t-space v-else>
            <t-tag v-for="(item, index) in row.knowNames.split(',')" :key="index" variant="light" theme="primary">
              {{ item }}
            </t-tag>
          </t-space>
        </template>
        <!-- 操作列 -->
        <template #operation="{ row }">
          <t-space size="4px">
            <t-button v-if="authEdit" size="small" variant="text" theme="primary" @click="openChat(row)">
              对话
            </t-button>
            <t-button v-if="authEdit" size="small" variant="text" theme="primary" @click="editRow(row)">
              修改
            </t-button>
            <t-popconfirm v-if="authDel" content="是否确认删除?" @confirm="delRow(row)">
              <t-button size="small" variant="text" theme="danger"> 删除 </t-button>
            </t-popconfirm>
          </t-space>
        </template>
      </FrQuery>
    </t-card>
    <!-- 新增/修改 -->
    <t-dialog
      v-model:visible="visibleModal"
      width="500"
      :closeOnOverlayClick="false"
      :header="opt === 'add' ? '新增' : '修改'"
      mode="modal"
      draggable
      :confirm-btn="saveBtn"
      :on-confirm="onSubmit"
    >
      <template #body>
        <t-form ref="form" :label-align="'right'" :data="formData" :rules="rules">
          <t-form-item label="应用名称" name="name">
            <t-input v-model="formData.name" placeholder="请输入应用名称"></t-input>
          </t-form-item>
          <t-form-item label="AI 昵称" name="nickName">
            <t-input v-model="formData.nickName" placeholder="请输入AI 昵称"></t-input>
          </t-form-item>
          <t-form-item label="关联模型" name="modelId">
            <t-select v-model="formData.modelId" :options="modelList" placeholder="请输入关联模型"></t-select>
          </t-form-item>
          <t-form-item label="关联知识库">
            <FrData
              ref="knowRef"
              v-model:model-value="formData.knowId"
              url="/aigc/knowLib/listAll"
              method="post"
              query-key="name"
              value-key="id"
              label-key="name"
              searchPlaceholder="请输入知识库名称进行检索"
              placeholder="请选择知识库"
            />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
    <FrChat ref="chatRef" :app-id="selectApp?.id" />
  </div>
</template>

<script lang="ts">
export default {
  name: 'ListaiApp',
};
</script>

<script setup lang="ts">
import { ref, onMounted, computed, reactive, nextTick } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { publicInterface } from '@/api/common';
import { useUserStore } from '@/store';
import FrQuery from '@/components/fr-query/index.vue';
import { listByType } from '@/api/aigc/model';
import FrChat from '@/components/fr-chat/index.vue';
import FrData from '@/components/fr-data/index.vue';

//权限控制
const userStore = useUserStore();
const authAdd = computed(() => userStore.roles.includes('aigc:aiApp:add'));
const authEdit = computed(() => userStore.roles.includes('aigc:aiApp:edit'));
const authDel = computed(() => userStore.roles.includes('aigc:aiApp:del'));

const queryRef = ref(null);
const firstFetch = async () => {
  queryRef.value.loadData(true);
};

const selectApp = ref<CommonObject>();
const chatRef = ref(null);
const openChat = (row) => {
  selectApp.value = row;
  chatRef.value.open();
};

const modelList = ref([]);
const initModel = async () => {
  let res = await listByType('01');
  res.data.map((row) => {
    modelList.value.push({
      value: row.id,
      label: row.title,
    });
  });
};

//新增/修改弹窗start
const visibleModal = ref(false);
const formData = ref({
  id: null,
  name: null,
  modelId: null,
  nickName: null,
  knowId: null,
});
const form = ref(null);
const saveBtn = reactive({
  content: '保存',
  loading: false,
});

const rules = {
  name: [{ required: true, message: '应用名称必填' }],
  nickName: [{ required: true, message: 'AI 昵称必填' }],
  modelId: [{ required: true, message: '关联模型必填' }],
} as Rules;

//提交方法
const onSubmit = async () => {
  saveBtn.content = '保存中...';
  saveBtn.loading = true;
  let result = await form.value.validate();
  if (typeof result !== 'object' && result) {
    let submitForm = {
      name: formData.value.name,
      modelId: formData.value.modelId,
      id: null,
      nickName: formData.value.nickName,
      knowId: formData.value.knowId,
    };
    if (opt.value === 'add') {
      try {
        let result1 = await publicInterface({
          url: '/aigc/aiApp/add',
          method: 'post',
          data: submitForm,
        });
        if (result1.code === 0) {
          visibleModal.value = false;
          queryRef.value.loadData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败:' + error.message);
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    } else {
      submitForm.id = formData.value.id;
      try {
        let result1 = await publicInterface({
          url: '/aigc/aiApp/edit',
          method: 'post',
          data: submitForm,
        });
        if (result1.code === 0) {
          visibleModal.value = false;
          queryRef.value.loadData();
          MessagePlugin.success('保存成功');
        } else {
          MessagePlugin.error('保存失败：' + result1.msg);
        }
      } catch (error) {
        MessagePlugin.error('保存失败:' + error.message);
      } finally {
        saveBtn.content = '保存';
        saveBtn.loading = false;
      }
    }
  } else {
    saveBtn.content = '保存';
    saveBtn.loading = false;
  }
};
//新增/修改弹窗end

const columns = [
  {
    width: 150,
    key: 'name',
    colKey: 'name',
    title: '应用名称',
    align: 'center',
  },
  {
    width: 150,
    key: 'nickName',
    colKey: 'nickName',
    title: 'AI 昵称',
    align: 'center',
  },
  {
    width: 150,
    key: 'modelTitle',
    colKey: 'modelTitle',
    title: '关联模型',
    align: 'center',
  },
  {
    width: 220,
    key: 'knowNames',
    colKey: 'knowNames',
    title: '关联知识库',
    align: 'center',
  },
  //   {
  //     width: 100,
  //     key: 'updateTime',
  //     colKey: 'updateTime',
  //     title: '最后修改时间',
  //     align: 'center',
  //   },
  {
    colKey: 'operation',
    title: '操作',
    width: 120,
    cell: 'operation',
    align: 'center',
    fixed: 'right',
  },
] as Columns;
const params = ref({
  name: null,
});
const opt = ref('add');
const knowRef = ref(null);
const addRow = async () => {
  opt.value = 'add';
  form.value.reset();
  formData.value.id = '';
  formData.value.knowId = '';
  visibleModal.value = true;
  nextTick(() => {
    knowRef.value.clearData();
  });
};
const editRow = async (row) => {
  opt.value = 'edit';
  form.value.reset();
  formData.value.id = row.id;
  formData.value.name = row.name;
  formData.value.modelId = row.modelId;
  formData.value.nickName = row.nickName;
  formData.value.knowId = row.knowId || null;
  visibleModal.value = true;
  nextTick(() => {
    if (formData.value.knowId) {
      knowRef.value.initData('id', formData.value.knowId);
    }
  });
};
const delRow = async (row) => {
  let msg = MessagePlugin.loading('删除中...');
  try {
    let res = await publicInterface({
      url: '/aigc/aiApp/del',
      urlParam: row.id,
      method: 'delete',
    });
    MessagePlugin.close(msg);
    if (res.code === 0) {
      queryRef.value.loadData();
      MessagePlugin.success('删除成功');
    } else {
      MessagePlugin.error('删除失败：' + res.msg);
    }
  } catch (error) {
    MessagePlugin.close(msg);
    MessagePlugin.error('删除失败:' + error.message);
  }
};

//vue的api
onMounted(() => {
  initModel();
});
</script>

<style lang="less" scoped>
@import '@/style/variables';
</style>
