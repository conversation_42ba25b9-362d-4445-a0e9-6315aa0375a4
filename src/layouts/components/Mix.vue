<template>
  <div>
    <div :class="sideNavCls">
      <t-menu
        :class="menuCls"
        :value="active"
        :expanded="openMenu"
        :width="['210px', '0px']"
        :collapsed="collapsed"
        @expand="expanded"
        style="border-top: 1px solid var(--td-component-stroke)"
      >
        <MenuContent :navData="menu" />
      </t-menu>
      <div :class="`${prefix}-side-nav-placeholder${collapsed ? '-hidden' : ''}`"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { prefix } from '@/config/global';
import MenuContent from './MenuContent';
import tLogo from '@/assets/assets-t-logo.svg?component';
import tLogoFull from '@/assets/assets-logo-full.svg?component';
import { useSettingStore } from '@/store';
import { getActive } from '@/router';

const MIN_POINT = 992 - 1;

const props = defineProps({
  menu: {
    type: Array<any>,
    default: () => [],
  },
  showLogo: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  isFixed: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  layout: {
    type: String as PropType<string>,
    default: '',
  },
  headerHeight: {
    type: String as PropType<string>,
    default: '64px',
  },
  theme: {
    type: String as PropType<string>,
    default: 'light',
  },
  isCompact: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
});

const collapsed = computed(() => useSettingStore().isSidebarCompact);

//计算打开的菜单
const openMenu1 = computed(() => {
  return active.value.substring(0, active.value.lastIndexOf('/'));
});

const sideNavCls = computed(() => {
  const { isCompact } = props;
  return [`${prefix}-sidebar-layout`];
});

const menuCls = computed(() => {
  const { showLogo, isFixed, layout } = props;
  return [
    'sp-left-menu',
    `${prefix}-side-nav`,
    {
      [`${prefix}-side-nav-no-logo`]: !showLogo,
      [`${prefix}-side-nav-no-fixed`]: !isFixed,
      [`${prefix}-side-nav-mix-fixed`]: layout === 'mix' && isFixed,
    },
  ];
});

const layoutCls = computed(() => {
  const { layout } = props;
  return [`${prefix}-side-nav-${layout}`, `${prefix}-sidebar-layout`];
});

const router = useRouter();
const settingStore = useSettingStore();
const active = computed(() => getActive());
const changeCollapsed = () => {
  settingStore.updateConfig({
    isSidebarCompact: !settingStore.isSidebarCompact,
  });
};

const autoCollapsed = () => {
  const isCompact = window.innerWidth <= MIN_POINT;
  if (isCompact) {
    settingStore.updateConfig({
      isSidebarCompact: isCompact,
    });
  } else {
    if (props.menu.length === 1 && props.menu[0].path === active.value) {
      settingStore.updateConfig({
        isSidebarCompact: true,
      });
    } else {
      settingStore.updateConfig({
        isSidebarCompact: false,
      });
    }
  }
};

onMounted(() => {
  //autoCollapsed();
  // window.onresize = () => {
  //   autoCollapsed();
  // };
});

const goHome = () => {
  router.push('/');
};
console.log(active.value);
const expandedMenu = ref([active.value.substring(0, active.value.lastIndexOf('/'))]);
const expanded = (val) => {
  expandedMenu.value = val;
};
const openMenu = computed(() => {
  return expandedMenu.value[0] === '' ? [openMenu1.value] : expandedMenu.value;
});
</script>

<style lang="less" scoped></style>
