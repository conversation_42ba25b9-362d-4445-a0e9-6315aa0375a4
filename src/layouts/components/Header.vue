<template>
  <div :class="layoutCls" :style="{ height: '48px' }">
    <t-head-menu :class="menuCls" :theme="theme" expand-type="popup" :value="active">
      <template #logo>
        <span v-if="showLogo && layout != 'sub'" class="header-logo-container">
          <div style="display: flex; flex-direction: row; align-items: center">
            <tLogo :class="prefix + '-side-nav-logo-t-logo3'" />
            <h2 :class="prefix + '-side-nav-logo-tdesign-logo3'">{{ config.title }}</h2>
          </div>
        </span>
        <div v-else class="header-operate-left">
          <t-button
            v-if="(layout === 'sub' && isShowColled) || layout !== 'sub'"
            theme="default"
            shape="square"
            variant="text"
            @click="changeCollapsed"
          >
            <FrIcon class="collapsed-icon" size="16px" v-if="isSidebarCompact" name="menu-fold" />
            <FrIcon class="collapsed-icon" size="16px" v-if="!isSidebarCompact" name="menu-unfold" />
          </t-button>
          <LayoutBreadcrumb v-if="showBreadcrumb" />
          <!-- <search :layout="layout" /> -->
        </div>
      </template>
      <HeadMenu v-if="layout === 'mix'" class="header-menu" :nav-data="menu" />
      <MenuContent v-if="layout === 'top'" class="header-menu" :nav-data="menu"></MenuContent>
      <template #operations>
        <div class="operations-container">
          <t-button @click="goAuth" :variant="'outline'" theme="primary">获取授权</t-button>
          <!-- 搜索框 -->
          <!-- <search v-if="layout !== 'side' && layout !== 'top'" :layout="layout" /> -->
          <Chat @click="showChat" />
          <!-- 全局通知 -->
          <t-tooltip placement="bottom" content="系统设置">
            <t-button theme="default" shape="square" variant="text">
              <FrIcon class="step-1" name="setting" @click="toggleSettingPanel" />
            </t-button>
          </t-tooltip>
          <notice class="step-2" />
          <t-button theme="default" shape="square" variant="text" @click="clickRight">
            <FrIcon class="step-1" name="copyright" />
          </t-button>
          <t-dropdown :min-column-width="135" trigger="click">
            <template #dropdown>
              <t-dropdown-menu>
                <t-dropdown-item class="operations-dropdown-container-item" @click="handleNav('/user/center')">
                  <FrIcon name="setting"></FrIcon>设置
                </t-dropdown-item>
                <t-dropdown-item class="operations-dropdown-container-item" @click="handleLogout">
                  <FrIcon name="poweroff"></FrIcon>退出登录
                </t-dropdown-item>
              </t-dropdown-menu>
            </template>
            <t-button class="header-user-btn" theme="default" variant="text">
              <div class="header-user-account">
                {{ curUser && curUser.nickName ? curUser.nickName : '游客' }}
                <FrIcon name="chevron-down" />
              </div>
            </t-button>
          </t-dropdown>
          <t-avatar v-if="!curUser || !curUser.avatar" style="margin-left: 5px">
            <template #icon>
              <FrIcon name="user-circle" />
            </template>
          </t-avatar>
          <t-avatar v-else style="margin-left: 5px" :image="curUser.avatar" />
        </div>
      </template>
    </t-head-menu>
    <!-- <t-guide
      v-model="current"
      :steps="steps"
      @change="handleChange"
      @prev-step-click="handlePrevStepClick"
      @next-step-click="handleNextStepClick"
      @finish="handleFinish"
      @skip="handleSkip"
    /> -->
    <FrRight ref="rightRef" />
    <FrChat ref="chatRef" :app-id="'1897896683065425921'" />
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useSettingStore, useUserStore, getPermissionStore, getUserStore } from '@/store';
import { getActive } from '@/router';
import { prefix } from '@/config/global';
import config from '@/config/style';
import tLogo from '@/assets/assets-t-logo.svg?component';
import { MenuRoute } from '@/types/interface';
import { DialogPlugin, TdGuideStepProps, NotifyPlugin, MessagePlugin } from 'tdesign-vue-next';
import LayoutBreadcrumb from './Breadcrumb.vue';
import { storeToRefs } from 'pinia';
import FrRight from '@/components/fr-right/index.vue';
import Notice from './Notice.vue';
import Search from './Search.vue';
import HeadMenu from './HeaderMenu.vue';
import MenuContent from './MenuContent';
import FrIcon from '@/components/fr-icon';
import Chat from '../Chat.vue';
import FrChat from '@/components/fr-chat/index.vue';

const route = useRoute();

const chatRef = ref(null);
const showChat = () => {
  chatRef.value.open();
};

const rightRef = ref(null);
const clickRight = () => {
  rightRef.value.open();
};

const permissionStore1 = getPermissionStore();
const userStore1 = getUserStore();

const userStore = useUserStore();
const curUser = computed(() => {
  return userStore.curUser;
});

const goQQ = () => {
  window.open('http://wpa.qq.com/msgrd?v=3&uin=2827916671&site=qq&menu=yes');
};

const props = defineProps({
  theme: {
    type: String as any,
    default: '',
  },
  layout: {
    type: String,
    default: 'top',
  },
  showLogo: {
    type: Boolean,
    default: true,
  },
  menu: {
    type: Array as PropType<MenuRoute[]>,
    default: () => [],
  },
  isFixed: {
    type: Boolean,
    default: false,
  },
  isCompact: {
    type: Boolean,
    default: false,
  },
  maxLevel: {
    type: Number,
    default: 3,
  },
});

const router = useRouter();
const settingStore = useSettingStore();
const showBreadcrumb = computed(() => {
  return settingStore.showBreadcrumb;
});
const isSidebarCompact = computed(() => {
  return settingStore.isSidebarCompact;
});

const toggleSettingPanel = () => {
  settingStore.updateConfig({
    showSettingPanel: true,
  });
};

const active = computed(() => {
  let activeMenu = '';
  if (props.layout === 'mix') {
    props.menu.map((row) => {
      if (route.path.startsWith(row.path)) {
        activeMenu = row.path;
      }
    });
  } else {
    return getActive();
  }
  return activeMenu;
});
const isShowColled = computed(() => {
  let isShow = false;
  const { routers: menuRouters } = storeToRefs(permissionStore1);
  let newMenuRouters = menuRouters.value;
  newMenuRouters.forEach((menu) => {
    if (active.value.startsWith(menu.path) && menu.isTop) {
      isShow = true;
    }
  });
  return isShow;
});

const layoutCls = computed(() => [`${prefix}-header-layout`]);

const menuCls = computed(() => {
  const { isFixed, layout, isCompact } = props;
  return [
    {
      [`${prefix}-header-menu`]: !isFixed,
      [`${prefix}-header-menu-fixed`]: isFixed,
      [`${prefix}-header-menu-fixed-side`]: layout === 'side' && isFixed,
      [`${prefix}-header-menu-fixed-sub`]: layout === 'sub' && isFixed,
      [`${prefix}-header-menu-fixed-side-compact`]: layout === 'side' && isFixed && isCompact,
      [`${prefix}-header-menu-fixed-sub-compact`]: layout === 'sub' && isFixed && isCompact,
    },
  ];
});

const changeCollapsed = () => {
  settingStore.updateConfig({
    isSidebarCompact: !settingStore.isSidebarCompact,
  });
};

const handleNav = (url) => {
  router.push(url);
};

const handleLogout = () => {
  const confirmDia = DialogPlugin({
    header: '提醒',
    body: '是否确认退出系统?',
    confirmBtn: '立即退出',
    //cancelBtn: '暂不',
    onConfirm: async ({ e }) => {
      confirmDia.hide();
      let loadMsg = MessagePlugin.loading({
        content: '正在退出中...',
        duration: 0,
      });
      await userStore1.logout();
      await permissionStore1.restore();
      //router.push(`/login?redirect=${router.currentRoute.value.fullPath}`);
      MessagePlugin.close(loadMsg);
      router.push('/login');
    },
    onClose: ({ e, trigger }) => {
      confirmDia.hide();
    },
  });
};

const goAuth = () => {
  window.open('https://frsimple.cn');
};
onMounted(() => {
  NotifyPlugin.info({
    title: 'frSimple Saas 系统发布',
    content: '演示环境可以通过官网frsimple.cn进行查看',
    duration: 5000,
    closeBtn: true,
  });
  NotifyPlugin.warning({
    title: '系统提醒',
    content: '由于查看演示环境的用户比较多，系统设置不允许重复登录，可能会存在被踢的情况。',
    duration: 0,
    closeBtn: true,
  });
});
</script>
<style lang="less" scoped>
@import '@/style/variables.less';
.@{prefix}-header {
  &-layout {
    height: 64px;
  }

  &-menu-fixed {
    position: fixed;
    top: 0;
    z-index: 1001;

    &-side {
      left: 201px;
      right: 0;
      z-index: 88;
      width: auto;
      transition: all 0.3s;
      &-compact {
        left: 62px;
      }
    }
    &-sub {
      left: 260px;
      right: 0;
      z-index: 100;
      width: auto;
      transition: all 0.3s;
      &-compact {
        left: 80px;
      }
    }
  }

  &-logo-container {
    cursor: pointer;
    display: inline-flex;
    height: 64px;
  }
}
.header-menu {
  flex: 1 1 1;
  display: inline-flex;

  :deep(.t-menu__item) {
    min-width: unset;
    padding: 0px 16px;
  }
}
.operations-container {
  display: flex;
  align-items: center;
  margin-right: 12px;

  .t-popup__reference {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .t-button {
    margin: 0 8px;
    &.header-user-btn {
      margin: 0;
    }
  }

  .t-icon {
    font-size: 20px;
    &.general {
      margin-right: 16px;
    }
  }
}

.header-operate-left {
  display: flex;
  margin-left: 10px;
  align-items: center;
  line-height: 0;
}

.header-logo-container {
  width: 184px;
  height: 26px;
  display: flex;
  margin-left: 24px;
  color: var(--tdvns-text-color-primary);

  .t-logo {
    width: 100%;
    height: 100%;
    &:hover {
      cursor: pointer;
    }
  }

  &:hover {
    cursor: pointer;
  }
}

.header-user-account {
  display: inline-flex;
  align-items: center;
  color: var(--tdvns-text-color-primary);
  .t-icon {
    margin-left: 4px;
    font-size: 16px;
  }
}
.t-menu--light {
  .header-user-account {
    color: var(--tdvns-text-color-primary);
  }
}
.t-menu--dark {
  .header-user-account {
    color: rgba(255, 255, 255, 0.55);
  }
  .t-button {
    --ripple-color: var(--tdvns-gray-color-10) !important;
    &:hover {
      background: var(--tdvns-gray-color-12) !important;
    }
  }
}

.operations-dropdown-container-item {
  width: 100%;
  display: flex;
  align-items: center;

  .t-icon {
    margin-right: 8px;
  }

  :deep(.t-dropdown__item) {
    .t-dropdown__item__content {
      display: flex;
      justify-content: center;
    }
    .t-dropdown__item__content__text {
      display: flex;
      align-items: center;
      font-size: 14px;
    }
  }

  :deep(.t-dropdown__item) {
    width: 100%;
    margin-bottom: 0px;
  }
  &:last-child {
    :deep(.t-dropdown__item) {
      margin-bottom: 8px;
    }
  }
}
</style>
<style lang="less">
.t-default-menu__inner .t-menu__logo > * {
  margin-left: 0px !important;
}
</style>
