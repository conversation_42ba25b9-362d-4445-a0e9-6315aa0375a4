<template>
  <div>
    <div :class="sideNavCls">
      <t-aside width="80px">
        <div class="sub-left-menu">
          <div class="content">
            <t-menu :class="menuCls1" :theme="theme" :value="clickMenu" width="80px">
              <template #logo>
                <div class="top">
                  <tLogo :class="`${prefix}-side-nav-logo-t-logo`" />
                </div>
              </template>
              <template v-for="(item, index) in leftMenu">
                <template v-if="item.isTop">
                  <template v-if="isHref(item)">
                    <template v-if="item.meta.isFrame === '1'">
                      <t-menu-item :name="item.path" :value="getPath(active, item)" @click="pageJump1(item.path)">
                        <template #icon>
                          <FrIcon
                            v-if="item.meta?.icon"
                            :name="item.meta?.icon"
                            size="20px"
                            style="margin-right: 0px !important"
                          />
                        </template>
                        {{ item.meta?.title }}
                      </t-menu-item>
                    </template>
                    <template v-else>
                      <t-menu-item :name="item.name" :value="getPath(active, item)" @click="openWin(isHref(item)?.[0])">
                        <template #icon>
                          <FrIcon
                            v-if="item.meta?.icon"
                            :name="item.meta?.icon"
                            size="20px"
                            style="margin-right: 0px !important"
                          />
                        </template>
                        {{ item.title }}
                      </t-menu-item>
                    </template>
                  </template>
                  <template v-else>
                    <t-menu-item :name="item.path" :value="getPath(active, item)" @click="pageJump1(item.path)">
                      <template #icon>
                        <FrIcon
                          v-if="item.meta?.icon"
                          :name="item.meta?.icon"
                          size="20px"
                          style="margin-right: 0px !important"
                        />
                      </template>
                      {{ item.meta?.title }}
                    </t-menu-item>
                  </template>
                </template>
                <template v-else>
                  <template v-if="isHref(item)">
                    <template v-if="item.meta.isFrame === '1'">
                      <t-menu-item :name="item.path" :value="getPath(active, item)" :to="item.path">
                        <template #icon>
                          <FrIcon
                            v-if="item.meta?.icon"
                            :name="item.meta?.icon"
                            size="20px"
                            style="margin-right: 0px !important"
                          />
                        </template>
                        {{ item.meta?.title }}
                      </t-menu-item>
                    </template>
                    <template v-else>
                      <t-menu-item :name="item.path" :value="getPath(active, item)" @click="openWin(isHref(item)?.[0])">
                        <template #icon>
                          <FrIcon
                            v-if="item.meta?.icon"
                            :name="item.meta?.icon"
                            size="20px"
                            style="margin-right: 0px !important"
                          />
                        </template>
                        {{ item.meta?.title }}
                      </t-menu-item>
                    </template>
                  </template>
                  <template v-else>
                    <t-menu-item :name="item.path" :value="getPath(active, item)" @click="pageJump1(item.path)">
                      <template #icon>
                        <FrIcon
                          v-if="item.meta?.icon"
                          :name="item.meta?.icon"
                          size="20px"
                          style="margin-right: 0px !important"
                        />
                      </template>
                      {{ item.meta?.title }}
                    </t-menu-item>
                  </template>
                </template>
              </template>
            </t-menu>
          </div>
        </div>
      </t-aside>
      <t-aside :width="sideWidth">
        <t-menu
          :class="menuCls"
          :value="active"
          v-model:expanded="expandedMenu"
          :defaultExpanded="[openMenu]"
          :width="['180px', '0px']"
          :collapsed="collapsed"
          @expand="expanded"
        >
          <template v-if="!collapsed" #logo>
            <span :class="`${prefix}-side-nav-logo-wrapper`" @click="goHome">
              <div :style="{ display: 'flex', flexDirection: 'row', alignItems: 'center' }">
                <h2 :style="{ fontSize: '17px' }">{{ selectSubMenu }}</h2>
              </div>
            </span>
          </template>
          <MenuContent :navData="subMenu" />
        </t-menu>
      </t-aside>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, onMounted, watch, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { prefix } from '@/config/global';
import MenuContent from './MenuContent';
import tLogo from '@/assets/assets-t-logo.svg?component';
import tLogoFull from '@/assets/assets-logo-full.svg?component';
import { useSettingStore, usePermissionStore } from '@/store';
import { getActive } from '@/router';
import { storeToRefs } from 'pinia';
import FrIcon from '@/components/fr-icon';

const route = useRoute();
const permissionStore = usePermissionStore();
const { routers: menuRouters } = storeToRefs(permissionStore);
const router = useRouter();
const settingStore = useSettingStore();
//选中的一级菜单
const clickMenu = ref('');

const props = defineProps({
  menuTitle: {
    type: String as PropType<string>,
    default: '',
  },
  leftMenu: {
    type: Array<any>,
    default: () => [],
  },
  menu: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  showLogo: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  isFixed: {
    type: Boolean as PropType<boolean>,
    default: true,
  },
  layout: {
    type: String as PropType<string>,
    default: '',
  },
  headerHeight: {
    type: String as PropType<string>,
    default: '64px',
  },
  theme: {
    type: String as PropType<any>,
    default: 'light',
  },
  isCompact: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
});

//二级菜单列表
const selectMenu = ref([]);
const subMenuTitle = ref('');
const selectSubMenu = computed(() => subMenuTitle.value || props.menuTitle);
const active = computed(() => getActive());
const isHref = (item) => {
  return item.meta?.href ? item.meta?.href.match(/(http|https):\/\/([\w.]+\/?)\S*/) : false;
};
const getPath = (active, item) => {
  return item.path;
};

const pageJump1 = (key) => {
  clickMenu.value = key;
  menuRouters.value.forEach((row) => {
    if (row.path === key) {
      if (row.children.length === 0 || !row.children[0].path) {
        router.push(row.path);
      } else {
        subMenuTitle.value = row.meta.title;
        settingStore.updateConfig({
          isSidebarCompact: false,
        });
        selectMenu.value = row.children.map((subMenu) => ({
          ...subMenu,
          path: `${row.path}${subMenu.path === '' ? '' : '/' + subMenu.path}`,
        }));
      }
    }
  });
};

const collapsed = computed(() => settingStore.isSidebarCompact);
watch(
  () => route,
  (newVal, oldVal) => {
    props.leftMenu.map((row) => {
      if (newVal.path.startsWith(row.path)) {
        clickMenu.value = row.path;
      }
    });
    menuRouters.value.forEach((row) => {
      if (row.path !== newVal && newVal.path.startsWith(row.path)) {
        if (row.children.length !== 0 && row.children[0].path) {
          subMenuTitle.value = row.meta.title;
          settingStore.updateConfig({
            isSidebarCompact: false,
          });
          selectMenu.value = row.children.map((subMenu) => ({
            ...subMenu,
            path: `${row.path}${subMenu.path === '' ? '' : '/' + subMenu.path}`,
          }));
        }
      }
    });
  },
  { deep: true, immediate: true },
);
//计算打开的菜单
const openMenu = computed(() => {
  return active.value.substring(0, active.value.lastIndexOf('/'));
});

const sideNavCls = computed(() => {
  const { isCompact } = props;
  return [
    `${prefix}-sidebar-layout1`,
    {
      [`${prefix}-sidebar-compact`]: isCompact,
    },
  ];
});

const menuCls = computed(() => {
  const { showLogo, isFixed, layout, isCompact } = props;
  return [
    'sp-left-menu',
    'sp-left-menu1',
    `${prefix}-side-nav`,
    {
      [`sp-left-menu1-hide`]: isCompact,
      [`${prefix}-side-nav-no-logo`]: !showLogo,
      [`${prefix}-side-nav-no-fixed`]: !isFixed,
      [`${prefix}-side-nav-mix-fixed`]: layout === 'mix' && isFixed,
    },
  ];
});

const menuCls1 = computed(() => {
  const { showLogo, isFixed, layout } = props;
  return [
    'sp-left-menu',
    `${prefix}-side-nav`,
    {
      [`${prefix}-side-nav-no-logo`]: !showLogo,
      [`${prefix}-side-nav-no-fixed`]: !isFixed,
      [`${prefix}-side-nav-mix-fixed`]: layout === 'mix' && isFixed,
    },
  ];
});

const subMenu = computed(() => {
  return selectMenu.value.length !== 0 ? selectMenu.value : props.menu;
});
const sideWidth = computed(() => {
  return settingStore.isSidebarCompact ? '0px' : '180px';
});
const expandedMenu = ref([openMenu.value]);
const expanded = (value) => {
  expandedMenu.value = value;
};
const openWin = (href: string) => {
  window.open(href);
};

onMounted(() => {
  //autoCollapsed();
  // window.onresize = () => {
  //   autoCollapsed();
  // };
});

const goHome = () => {
  router.push('/');
};
</script>

<style lang="less" scoped></style>
