<template>
  <div class="flex">
    <button class="TDesign-ai-button" @click="showChat">
      <span>
        <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M8.34644 0.0014782L10.6731 5.32692L15.9985 7.65358L10.6731 9.98025L8.34644 15.3057L6.01977 9.98025L0.694336 7.65358L6.01977 5.32692L8.34644 0.0014782ZM14.55 2.21647L13.5041 2.73707L14.55 3.25768L15.0706 4.30356L15.5912 3.25768L16.637 2.73707L15.5912 2.21647L15.0706 1.17059L14.55 2.21647ZM13.2237 9.83728L14.0899 11.5774L15.8301 12.4436L14.0899 13.3098L13.2237 15.05L12.3575 13.3098L10.6174 12.4436L12.3575 11.5774L13.2237 9.83728Z"
            fill="url(#paint0_linear_465_5938)"
            style=""
          ></path>
          <defs>
            <linearGradient
              id="paint0_linear_465_5938"
              x1="-0.102799"
              y1="0.236928"
              x2="14.5949"
              y2="17.087"
              gradientUnits="userSpaceOnUse"
            >
              <stop
                stop-color="#0062FF"
                style="stop-color: #0062ff; stop-color: color(display-p3 0 0.3843 1); stop-opacity: 1"
              ></stop>
              <stop
                offset="1"
                stop-color="#00C3FF"
                style="stop-color: #00c3ff; stop-color: color(display-p3 0 0.7647 1); stop-opacity: 1"
              ></stop>
            </linearGradient>
          </defs>
        </svg>
        <span class="TDesign-ai-button__text" data-text="AI 助手"> AI 助手 </span>
      </span>
    </button>
  </div>
</template>

<script setup lang="ts">
const emits = defineEmits(['click']);

const showChat = () => {
  emits('click');
};
</script>

<style lang="less" scoped>
.TDesign-ai-button {
  border-radius: 6px;
  border: none;
  box-shadow:
    rgba(0, 255, 240, 0.12) 0px 2px 4px -1px,
    rgba(0, 71, 255, 0.08) 0px 4px 5px,
    rgba(0, 102, 255, 0.05) 0px 1px 10px;
  cursor: pointer;
  background: conic-gradient(rgb(0, 195, 255), rgb(76, 235, 27), rgb(0, 98, 255), rgb(160, 124, 243), rgb(0, 195, 255));
  height: 32px;
  font-size: 14px;
  padding: 2px;
}

.TDesign-ai-button span {
  background-color: #ffffff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  border-radius: 4px;
  padding: 0px 8px;
}

.TDesign-ai-button__text {
  position: relative;
  color: rgb(0, 195, 255);
  font-weight: 600;
  padding: 0px 0px 0px 8px;
}
</style>
