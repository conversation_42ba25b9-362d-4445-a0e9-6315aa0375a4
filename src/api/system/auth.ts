import request from '@/utils/request';
import { sm2Encrypt } from '@/utils/encrypt';

const clientId = 'frsimple';
const clientSecret = 'frsimple';
export async function loginByUserName(data) {
  let paramsData = JSON.parse(JSON.stringify(data));
  paramsData.username = await sm2Encrypt(paramsData.username);
  paramsData.password = await sm2Encrypt(paramsData.password);
  paramsData.grant_type = 'password';
  paramsData.client_id = clientId;
  paramsData.client_secret = clientSecret;
  paramsData.scope = 'all';
  return request({
    url: '/oauth2/token',
    method: 'post',
    data: paramsData,
    headers: {
      isAuth: false,
      //_login_type_: 'username',
      loginType: 'PC',
      sp: paramsData.sp,
      code: paramsData.code,
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function loginByUserPhone(data) {
  let paramsData = JSON.parse(JSON.stringify(data));
  paramsData.phone = await sm2Encrypt(paramsData.phone);
  paramsData.grant_type = 'sms';
  paramsData.client_id = clientId;
  paramsData.client_secret = clientSecret;
  paramsData.scope = 'all';
  return request({
    url: '/oauth2/token',
    method: 'post',
    data: paramsData,
    headers: {
      isAuth: false,
      //_login_type_: 'sms',
      loginType: 'PC',
      sp: paramsData.sp,
      code: paramsData.code,
      'Content-Type': 'multipart/form-data',
    },
  });
}

export function sendSms(params) {
  return request({
    url: '/oauth2/sms',
    method: 'get',
    params: params,
    headers: {
      isAuth: false,
    },
  });
}

export function getCurUserInfo() {
  return request({
    url: '/center/user/info',
    method: 'get',
  });
}

export function getCurUserMenu() {
  return request({
    url: '/center/user/menu',
    method: 'get',
  });
}

export function logout() {
  return request({
    url: '/auth/revoke',
    method: 'get',
    params: {
      client_id: clientId,
      client_secret: clientSecret,
    },
  });
}

export function tokenList(params) {
  return request({
    url: '/auth/online',
    method: 'get',
    params: params,
  });
}

export function userLogout(params) {
  return request({
    url: '/auth/kickOff',
    method: 'get',
    params: params,
  });
}

export function license() {
  return request({
    url: '/center/license/info',
    method: 'get',
  });
}

export async function loginConfig() {
  return request({
    url: '/center/sysConfig/api/login',
    method: 'get',
    headers: {
      isAuth: false,
    },
  });
}
