import { request } from '@/utils/request';

const Api = {
  // 规格分组
  getSpecGroupList: '/api/equipment/spec-group/list',
  getSpecGroupById: '/api/equipment/spec-group',
  createSpecGroup: '/api/equipment/spec-group',
  updateSpecGroup: '/api/equipment/spec-group',
  deleteSpecGroup: '/api/equipment/spec-group',
  getSpecGroupTree: '/api/equipment/spec-group/tree',

  // 规格参数
  getSpecParamList: '/api/equipment/spec-param/list',
  getSpecParamById: '/api/equipment/spec-param',
  createSpecParam: '/api/equipment/spec-param',
  updateSpecParam: '/api/equipment/spec-param',
  deleteSpecParam: '/api/equipment/spec-param',
  getParamsByGroup: '/api/equipment/spec-param/group',

  // 设备规格
  getAssetSpecList: '/api/equipment/asset-spec/list',
  getAssetSpecById: '/api/equipment/asset-spec',
  createAssetSpec: '/api/equipment/asset-spec',
  updateAssetSpec: '/api/equipment/asset-spec',
  deleteAssetSpec: '/api/equipment/asset-spec',
  batchUpdateAssetSpec: '/api/equipment/asset-spec/batch',
  getAssetSpecByAssetId: '/api/equipment/asset-spec/asset',
  compareAssetSpecs: '/api/equipment/asset-spec/compare',
  
  // 规格模板
  getSpecTemplateList: '/api/equipment/spec-template/list',
  getSpecTemplateById: '/api/equipment/spec-template',
  createSpecTemplate: '/api/equipment/spec-template',
  updateSpecTemplate: '/api/equipment/spec-template',
  deleteSpecTemplate: '/api/equipment/spec-template',
  applySpecTemplate: '/api/equipment/spec-template',
  
  // 规格历史
  getSpecHistoryList: '/api/equipment/asset-spec-history/list',
  getSpecHistoryById: '/api/equipment/asset-spec-history',
  
  // 导入导出
  importSpecs: '/api/equipment/asset-spec/import',
  exportSpecs: '/api/equipment/asset-spec/export',
  exportSpecTemplate: '/api/equipment/spec-template/template',
};

// 规格分组相关接口
export const getSpecGroupList = (params: any) => {
  return request.get({
    url: Api.getSpecGroupList,
    params,
  });
};

export const getSpecGroupById = (id: string) => {
  return request.get({
    url: `${Api.getSpecGroupById}/${id}`,
  });
};

export const createSpecGroup = (data: any) => {
  return request.post({
    url: Api.createSpecGroup,
    data,
  });
};

export const updateSpecGroup = (data: any) => {
  return request.put({
    url: Api.updateSpecGroup,
    data,
  });
};

export const deleteSpecGroup = (id: string) => {
  return request.delete({
    url: `${Api.deleteSpecGroup}/${id}`,
  });
};

export const getSpecGroupTree = () => {
  return request.get({
    url: Api.getSpecGroupTree,
  });
};

// 规格参数相关接口
export const getSpecParamList = (params: any) => {
  return request.get({
    url: Api.getSpecParamList,
    params,
  });
};

export const getSpecParamById = (id: string) => {
  return request.get({
    url: `${Api.getSpecParamById}/${id}`,
  });
};

export const createSpecParam = (data: any) => {
  return request.post({
    url: Api.createSpecParam,
    data,
  });
};

export const updateSpecParam = (data: any) => {
  return request.put({
    url: Api.updateSpecParam,
    data,
  });
};

export const deleteSpecParam = (id: string) => {
  return request.delete({
    url: `${Api.deleteSpecParam}/${id}`,
  });
};

export const getParamsByGroup = (groupId: string) => {
  return request.get({
    url: `${Api.getParamsByGroup}/${groupId}`,
  });
};

// 设备规格相关接口
export const getAssetSpecList = (params: any) => {
  return request.get({
    url: Api.getAssetSpecList,
    params,
  });
};

export const getAssetSpecById = (id: string) => {
  return request.get({
    url: `${Api.getAssetSpecById}/${id}`,
  });
};

export const createAssetSpec = (data: any) => {
  return request.post({
    url: Api.createAssetSpec,
    data,
  });
};

export const updateAssetSpec = (data: any) => {
  return request.put({
    url: Api.updateAssetSpec,
    data,
  });
};

export const deleteAssetSpec = (id: string) => {
  return request.delete({
    url: `${Api.deleteAssetSpec}/${id}`,
  });
};

export const batchUpdateAssetSpec = (data: any) => {
  return request.put({
    url: Api.batchUpdateAssetSpec,
    data,
  });
};

export const getAssetSpecByAssetId = (assetId: string) => {
  return request.get({
    url: `${Api.getAssetSpecByAssetId}/${assetId}`,
  });
};

export const compareAssetSpecs = (assetIds: string[]) => {
  return request.post({
    url: Api.compareAssetSpecs,
    data: { assetIds },
  });
};

// 规格模板相关接口
export const getSpecTemplateList = (params: any) => {
  return request.get({
    url: Api.getSpecTemplateList,
    params,
  });
};

export const getSpecTemplateById = (id: string) => {
  return request.get({
    url: `${Api.getSpecTemplateById}/${id}`,
  });
};

export const createSpecTemplate = (data: any) => {
  return request.post({
    url: Api.createSpecTemplate,
    data,
  });
};

export const updateSpecTemplate = (data: any) => {
  return request.put({
    url: Api.updateSpecTemplate,
    data,
  });
};

export const deleteSpecTemplate = (id: string) => {
  return request.delete({
    url: `${Api.deleteSpecTemplate}/${id}`,
  });
};

export const applySpecTemplate = (templateId: string, assetIds: string[]) => {
  return request.post({
    url: `${Api.applySpecTemplate}/${templateId}/apply`,
    data: { assetIds },
  });
};

// 规格历史相关接口
export const getSpecHistoryList = (params: any) => {
  return request.get({
    url: Api.getSpecHistoryList,
    params,
  });
};

export const getSpecHistoryById = (id: string) => {
  return request.get({
    url: `${Api.getSpecHistoryById}/${id}`,
  });
};

// 导入导出相关接口
export const importSpecs = (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  return request.post({
    url: Api.importSpecs,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export const exportSpecs = (assetIds?: string[]) => {
  return request.post({
    url: Api.exportSpecs,
    data: { assetIds },
    responseType: 'blob',
  });
};

export const exportSpecTemplate = () => {
  return request.get({
    url: Api.exportSpecTemplate,
    responseType: 'blob',
  });
};