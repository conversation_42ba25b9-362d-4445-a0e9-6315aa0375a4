import { request } from '@/utils/request';

const Api = {
  // 版本管理
  getVersionList: '/api/equipment/asset-version/list',
  getVersionById: '/api/equipment/asset-version',
  createVersion: '/api/equipment/asset-version',
  updateVersion: '/api/equipment/asset-version',
  deleteVersion: '/api/equipment/asset-version',
  getAssetVersions: '/api/equipment/asset-version/asset',
  compareVersions: '/api/equipment/asset-version/compare',
  rollbackVersion: '/api/equipment/asset-version',
  
  // 版本审批
  getApprovalList: '/api/equipment/asset-approval/list',
  createApproval: '/api/equipment/asset-approval',
  updateApproval: '/api/equipment/asset-approval',
  approveVersion: '/api/equipment/asset-approval',
  rejectVersion: '/api/equipment/asset-approval',
  getPendingApprovals: '/api/equipment/asset-approval/pending',
  getApprovalsByVersion: '/api/equipment/asset-approval/version',
  
  // 变更日志
  getChangeLogList: '/api/equipment/asset-change-log/list',
  getChangeLogById: '/api/equipment/asset-change-log',
  getChangeLogsByAsset: '/api/equipment/asset-change-log/asset',
  getChangeLogsByVersion: '/api/equipment/asset-change-log/version',
  
  // 版本标签
  getVersionTagList: '/api/equipment/asset-version-tag/list',
  createVersionTag: '/api/equipment/asset-version-tag',
  updateVersionTag: '/api/equipment/asset-version-tag',
  deleteVersionTag: '/api/equipment/asset-version-tag',
  getTagsByVersion: '/api/equipment/asset-version-tag/version',
  
  // 统计分析
  getVersionStatistics: '/api/equipment/asset-version/statistics',
  getApprovalStatistics: '/api/equipment/asset-approval/statistics',
  
  // 导出
  exportVersions: '/api/equipment/asset-version/export',
  exportChangeLogs: '/api/equipment/asset-change-log/export',
};

// 版本管理相关接口
export const getVersionList = (params: any) => {
  return request.get({
    url: Api.getVersionList,
    params,
  });
};

export const getVersionById = (id: string) => {
  return request.get({
    url: `${Api.getVersionById}/${id}`,
  });
};

export const createVersion = (data: any) => {
  return request.post({
    url: Api.createVersion,
    data,
  });
};

export const updateVersion = (data: any) => {
  return request.put({
    url: Api.updateVersion,
    data,
  });
};

export const deleteVersion = (id: string) => {
  return request.delete({
    url: `${Api.deleteVersion}/${id}`,
  });
};

export const getAssetVersions = (assetId: string, params: any) => {
  return request.get({
    url: `${Api.getAssetVersions}/${assetId}/versions`,
    params,
  });
};

export const compareVersions = (versionIds: string[]) => {
  return request.post({
    url: Api.compareVersions,
    data: { versionIds },
  });
};

export const rollbackVersion = (versionId: string, reason: string) => {
  return request.post({
    url: `${Api.rollbackVersion}/${versionId}/rollback`,
    data: { reason },
  });
};

// 版本审批相关接口
export const getApprovalList = (params: any) => {
  return request.get({
    url: Api.getApprovalList,
    params,
  });
};

export const createApproval = (data: any) => {
  return request.post({
    url: Api.createApproval,
    data,
  });
};

export const updateApproval = (data: any) => {
  return request.put({
    url: Api.updateApproval,
    data,
  });
};

export const approveVersion = (approvalId: string, comment: string) => {
  return request.post({
    url: `${Api.approveVersion}/${approvalId}/approve`,
    data: { comment },
  });
};

export const rejectVersion = (approvalId: string, comment: string) => {
  return request.post({
    url: `${Api.rejectVersion}/${approvalId}/reject`,
    data: { comment },
  });
};

export const getPendingApprovals = (approverId: string) => {
  return request.get({
    url: Api.getPendingApprovals,
    params: { approverId },
  });
};

export const getApprovalsByVersion = (versionId: string) => {
  return request.get({
    url: `${Api.getApprovalsByVersion}/${versionId}`,
  });
};

// 变更日志相关接口
export const getChangeLogList = (params: any) => {
  return request.get({
    url: Api.getChangeLogList,
    params,
  });
};

export const getChangeLogById = (id: string) => {
  return request.get({
    url: `${Api.getChangeLogById}/${id}`,
  });
};

export const getChangeLogsByAsset = (assetId: string, params: any) => {
  return request.get({
    url: `${Api.getChangeLogsByAsset}/${assetId}`,
    params,
  });
};

export const getChangeLogsByVersion = (versionId: string) => {
  return request.get({
    url: `${Api.getChangeLogsByVersion}/${versionId}`,
  });
};

// 版本标签相关接口
export const getVersionTagList = (params: any) => {
  return request.get({
    url: Api.getVersionTagList,
    params,
  });
};

export const createVersionTag = (data: any) => {
  return request.post({
    url: Api.createVersionTag,
    data,
  });
};

export const updateVersionTag = (data: any) => {
  return request.put({
    url: Api.updateVersionTag,
    data,
  });
};

export const deleteVersionTag = (id: string) => {
  return request.delete({
    url: `${Api.deleteVersionTag}/${id}`,
  });
};

export const getTagsByVersion = (versionId: string) => {
  return request.get({
    url: `${Api.getTagsByVersion}/${versionId}`,
  });
};

// 统计分析相关接口
export const getVersionStatistics = () => {
  return request.get({
    url: Api.getVersionStatistics,
  });
};

export const getApprovalStatistics = () => {
  return request.get({
    url: Api.getApprovalStatistics,
  });
};

// 导出相关接口
export const exportVersions = (assetId?: string) => {
  return request.get({
    url: Api.exportVersions,
    params: { assetId },
    responseType: 'blob',
  });
};

export const exportChangeLogs = (assetId?: string, startDate?: string, endDate?: string) => {
  return request.get({
    url: Api.exportChangeLogs,
    params: { assetId, startDate, endDate },
    responseType: 'blob',
  });
};