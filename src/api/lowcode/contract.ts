import request from '@/utils/request';

//查询
export function list(params) {
  return request({
    url: '/lowcode/codeTemplate/list',
    method: 'get',
    params: params,
  });
}

//新增
export function add(data) {
  return request({
    url: '/lowcode/codeTemplate/add',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

//修改
export function edit(data) {
  return request({
    url: '/lowcode/codeTemplate/edit',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

//删除
export function del(id) {
  return request({
    url: '/lowcode/codeTemplate/del/' + id,
    method: 'delete',
  });
}

//查询
export function get(code) {
  return request({
    url: '/lowcode/codeTemplate/get/' + code,
    method: 'get',
  });
}

//下载
export function downLoad(id) {
  return request({
    url: '/lowcode/codeTemplate/del/' + id,
    method: 'delete',
  });
}
