import request from '@/utils/request';

export function listType() {
  return request({
    url: '/lowcode/modelType/list',
    method: 'post',
  });
}

export function addType(data) {
  return request({
    url: '/lowcode/modelType/add',
    method: 'post',
    data,
  });
}

export function editType(data) {
  return request({
    url: '/lowcode/modelType/edit',
    method: 'post',
    data,
  });
}

export function delType(id) {
  return request({
    url: '/lowcode/modelType/del/' + id,
    method: 'delete',
  });
}

export function list(data) {
  return request({
    url: '/lowcode/model/list',
    method: 'post',
    data,
  });
}

export function add(data) {
  return request({
    url: '/lowcode/model/add',
    method: 'post',
    data,
  });
}

export function edit(data) {
  return request({
    url: '/lowcode/model/edit',
    method: 'post',
    data,
  });
}

export function del(id) {
  return request({
    url: '/lowcode/model/del/' + id,
    method: 'delete',
  });
}

export function refCache() {
  return request({
    url: '/lowcode/model/refCache',
    method: 'post',
  });
}
