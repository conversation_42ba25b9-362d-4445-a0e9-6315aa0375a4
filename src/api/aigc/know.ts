import request from '@/utils/request';

// 查询流程模型信息列表
export function listAll(data) {
  return request({
    url: `/aigc/knowLib/listAll`,
    method: 'post',
    data,
  });
}

//训练
export function train(data) {
  return request({
    url: `/aigc/vectorFile/train`,
    method: 'post',
    data,
  });
}

//重新训练
export function reTrain(data) {
  return request({
    url: `/aigc/vectorFile/reTrain`,
    method: 'post',
    data,
  });
}

//下载文件
export function downLoad(data) {
  return request({
    url: '/aigc/vectorFile/downLoad',
    method: 'post',
    data: data,
    responseType: 'blob',
  });
}

// 查询流程模型信息列表
export function listSlice(data) {
  return request({
    url: `/aigc/vectorFile/listSlice`,
    method: 'post',
    data,
  });
}
