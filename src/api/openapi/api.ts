import request from '@/utils/request';

export function list(data) {
  return request({
    url: `/openapi/apiMain/list`,
    method: 'post',
    data,
  });
}

export function add(data) {
  return request({
    url: `/openapi/apiMain/add`,
    method: 'post',
    data,
  });
}

export function edit(data) {
  return request({
    url: `/openapi/apiMain/edit`,
    method: 'post',
    data,
  });
}

export function del(id) {
  return request({
    url: `/openapi/apiMain/del/${id}`,
    method: 'delete',
  });
}

export function get(id) {
  return request({
    url: `/openapi/apiMain/get/${id}`,
    method: 'get',
  });
}

export function refCache() {
  return request({
    url: `/openapi/apiMain/refCache`,
    method: 'get',
  });
}

export function use(id) {
  return request({
    url: `/openapi/apiMain/use/${id}`,
    method: 'get',
  });
}

export function unUse(id) {
  return request({
    url: `/openapi/apiMain/unUse/${id}`,
    method: 'get',
  });
}
