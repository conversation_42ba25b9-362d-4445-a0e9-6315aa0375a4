import request from '@/utils/request';

export function list(data) {
  return request({
    url: `/openapi/user/list`,
    method: 'post',
    data,
  });
}

export function add(data) {
  return request({
    url: `/openapi/user/add`,
    method: 'post',
    data,
  });
}

export function edit(data) {
  return request({
    url: `/openapi/user/edit`,
    method: 'post',
    data,
  });
}

export function del(id) {
  return request({
    url: `/openapi/user/del/${id}`,
    method: 'delete',
  });
}

export function getSecret(data) {
  return request({
    url: `/openapi/user/getSecret`,
    method: 'post',
    data,
  });
}

export function editPwd(data) {
  return request({
    url: `/openapi/user/updatePwd`,
    method: 'post',
    data,
  });
}

export function getAuth(id) {
  return request({
    url: `/openapi/user/getAuth/${id}`,
    method: 'post',
  });
}

export function apiList() {
  return request({
    url: `/openapi/user/apiList`,
    method: 'post',
  });
}

export function saveAuth(data) {
  return request({
    url: `/openapi/user/saveAuth`,
    method: 'post',
    data,
  });
}

export function ipList(id) {
  return request({
    url: `/openapi/user/ipList/${id}`,
    method: 'post',
  });
}

export function saveIp(data) {
  return request({
    url: `/openapi/user/saveIp`,
    method: 'post',
    data,
  });
}

export function lock(id) {
  return request({
    url: `/openapi/user/lock/${id}`,
    method: 'post',
  });
}

export function unLock(id) {
  return request({
    url: `/openapi/user/unLock/${id}`,
    method: 'post',
  });
}

//附件下载
export function exportExcel(id) {
  return request({
    url: '/openapi/user/exportExcel/' + id,
    method: 'post',
    responseType: 'blob',
  });
}
