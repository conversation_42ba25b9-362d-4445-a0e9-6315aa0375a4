import request from '@/utils/request';

// 查询数据
export function list(params) {
  return request({
    url: `/flow/extension/list`,
    method: 'get',
    params,
  });
}

// 新增数据
export function add(data) {
  return request({
    url: `/flow/extension/add`,
    method: 'post',
    data,
  });
}

// 修改数据
export function edit(data) {
  return request({
    url: `/flow/extension/edit`,
    method: 'post',
    data,
  });
}

// 删除数据
export function del(id) {
  return request({
    url: `/flow/extension/del/${id}`,
    method: 'delete',
  });
}
