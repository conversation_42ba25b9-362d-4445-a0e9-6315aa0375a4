import request from '@/utils/request';

//查询所有流程实例
export function list(params) {
  return request({
    url: `/flow/instance/list`,
    method: 'get',
    params,
  });
}

//查询流程实例统计数据
export function listCount(params) {
  return request({
    url: `/flow/instance/listCount`,
    method: 'get',
    params,
  });
}

//查询流转流水
export function logList(id) {
  return request({
    url: `/flow/task/log/${id}`,
    method: 'get',
  });
}

// 发起流程
export function start(data) {
  return request({
    url: `/flow/instance/start`,
    method: 'post',
    data,
  });
}

// 发起并且提交第一个节点
export function startWithSubmit(data) {
  return request({
    url: `/flow/instance/startWithSubmit`,
    method: 'post',
    data,
  });
}

// 暂停流程
export function stop(id) {
  return request({
    url: `/flow/instance/stop/${id}`,
    method: 'get',
  });
}

// 恢复流程
export function active(id) {
  return request({
    url: `/flow/instance/active/${id}`,
    method: 'get',
  });
}

// 作废流程
export function abrogate(params) {
  return request({
    url: `/flow/instance/abrogate`,
    method: 'get',
    params,
  });
}

// 删除流程
export function deleteFlow(id) {
  return request({
    url: `/flow/instance/delete/${id}`,
    method: 'get',
  });
}

// 提交流程
export function commitFlow(data) {
  return request({
    url: `/flow/task/commitFlow`,
    method: 'post',
    data,
  });
}

// 转办任务
export function transfer(data) {
  return request({
    url: `/flow/task/transfer`,
    method: 'post',
    data,
  });
}

// 委托任务
export function delegate(data) {
  return request({
    url: `/flow/task/delegate`,
    method: 'post',
    data,
  });
}

// 流程实例流程图
export function instanceDesign(id) {
  return request({
    url: `/flow/instance/instanceDesign/${id}`,
    method: 'get',
  });
}

// 查询表单数据
export function formData(data) {
  return request({
    url: `/center/frQuickCode/formData`,
    method: 'post',
    data,
  });
}
