import request from '@/utils/request';

// 查询数据
export function list(params) {
  return request({
    url: `/flow/monitor/list`,
    method: 'get',
    params,
  });
}

// 新增数据
export function add(data) {
  return request({
    url: `/flow/monitor/add`,
    method: 'post',
    data,
  });
}

// 修改数据
export function edit(data) {
  return request({
    url: `/flow/monitor/edit`,
    method: 'post',
    data,
  });
}

// 删除数据
export function del(id) {
  return request({
    url: `/flow/monitor/del/${id}`,
    method: 'delete',
  });
}
