import request from '@/utils/request';

// 查询字典项
export function dicVals(code) {
  return request({
    url: `/center/dict/cache/${code}`,
    method: 'get',
  });
}

// 查询系统参数
export function paramVal(name) {
  return request({
    url: `/center/sysparameter/cache/${name}`,
    method: 'get',
  });
}

//上传图片返回图片路径
export function commonUploadImg(data) {
  return request({
    url: '/center/file/upLoadImg',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 根据用户ids查询用户昵称
export function userNameByIds(params) {
  return request({
    url: `/center/user/userNameByIds`,
    method: 'get',
    params,
  });
}

//公共接口方法
export function publicInterface(httpParam: HttpParam) {
  if (httpParam.urlParam) {
    httpParam.url = httpParam.url + '/' + httpParam.urlParam;
  }
  return request({ ...httpParam });
}

//上传附件
export function upLoadFile(data) {
  return request({
    url: '/center/file/upLoad',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 批量附件上传
export function upLoadFiles(data) {
  return request({
    url: '/center/file/batchUpLoad',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

//附件下载
export function downLoadFile(data) {
  return request({
    url: '/center/file/downLoad',
    method: 'post',
    data: data,
    responseType: 'blob',
  });
}

//列表数据导出excel格式
export function exportExcel(data) {
  return request({
    url: '/center/pub/exportExcel',
    method: 'post',
    data: data,
    responseType: 'blob',
  });
}

// 删除附件
export function removeFile(data) {
  return request({
    url: '/center/file/remove',
    method: 'post',
    data: data,
  });
}

//根据ids查询附件列表
export function listByIds(ids) {
  return request({
    url: `/center/file/listByIds`,
    method: 'post',
    data: {
      id: ids,
    },
  });
}

//生成临时下载链接
export function temporaryFile(data) {
  return request({
    url: '/center/file/temporaryFile',
    method: 'post',
    data: data,
  });
}

//获取模型
export function getModel(data) {
  return request({
    url: '/center/frQuick/model',
    method: 'post',
    data,
  });
}

//获取表单模型
export function getFormModel(data) {
  return request({
    url: '/center/frQuickCode/model',
    method: 'post',
    data,
  });
}
