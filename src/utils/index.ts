import proxy from '../config/proxy';
import { dicVals, publicInterface } from '@/api/common';
import type { Dict } from '@/types/interface';
import { downLoad as downLoadFile } from '@/api/aigc/know';
import { MessagePlugin } from 'tdesign-vue-next';
import _ from 'lodash';

const env = import.meta.env.MODE || 'development';
const host = env === 'development' ? '' : proxy[env].host;

/**
 * @description 格式化时间
 * @param time
 * @param cFormat
 * @returns {string|null}
 */
export function parseTime(time: string | number | Date, cFormat: string): string | null {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
  let date: any;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time, 10);
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time *= 1000;
    }
    date = new Date(time);
  }
  const formatObj: any = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  return format.replace(/{([ymdhisa])+}/g, (result: string | any[], key: string) => {
    let value = formatObj[key];
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    if (result.length > 0 && value < 10) {
      value = `0${value}`;
    }
    return value || 0;
  });
}

// 表格专用格式化
export function tableDateFormat(cellValue: any) {
  const format = '{y}-{m}-{d} {h}:{i}';
  if (!cellValue) return '';
  return parseTime(cellValue, format);
}

export function tableEnumFormatter(enumItemCodes: any, enumItemList: any) {
  let result = '';
  if (enumItemCodes === undefined) return result;
  const dataArr = enumItemCodes
    .replace(/\[/g, '')
    .replace(/\]/g, '')
    .replace(/\{/g, '')
    .replace(/\}/g, '')
    .replace(/"/g, '')
    .split(',');
  if (!enumItemList) {
    return result;
  }
  if (enumItemList === null || enumItemList === undefined || enumItemList.length === 0) {
    return result;
  }
  if (dataArr.length > 0) {
    dataArr.forEach((val: any) => {
      const findItem = enumItemList.filter((val1: { code: any }) => {
        return val1.code === val;
      });
      if (findItem.length === 0) {
        return result;
      }
      result += `${findItem[0].name},`;
      return true;
    });
  } else {
    if (enumItemList == null || enumItemList === undefined || enumItemList.length === 0) {
      return result;
    }
    const findItem = enumItemList.filter((val: { code: any }) => {
      return val.code === enumItemCodes;
    });
    if (findItem.length === 0) {
      return result;
    }
    result = findItem[0].name;
  }
  if (result !== '' && result.lastIndexOf(',') > 0) {
    result = result.substring(0, result.lastIndexOf(','));
  }
  return result;
}

export const getDict = async (keys: string) => {
  let keyArray = keys.split(',');
  let result: DictType = {};
  const promises = keyArray.map(async (str) => {
    let res = await dicVals(str);
    result[str] = {
      list: [],
      map: new Map(),
    };
    res.data.map((row) => {
      result[str].list.push({
        value: row.value,
        label: row.label,
      });
      result[str].map.set(row.value, row.label);
    });
    return str;
  });
  // 使用Promise.all()等待所有请求完成
  await Promise.all(promises);
  return result;
};

export const exportSqlFile = async (type, value, server = 'center') => {
  let load = MessagePlugin.loading({
    content: '生成下载中...',
    duration: 0,
  });
  try {
    let res = await publicInterface({
      url: `/${server}/frQuick/createSql`,
      method: 'post',
      headers: {
        responseType: 'blob',
      },
      data: {
        value: value,
        type: type,
      },
    });
    let blob = new Blob([res.data], { type: 'application/sql' });
    let filename = res.headers['content-disposition'];
    let link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    MessagePlugin.close(load);
  } catch (er) {
    MessagePlugin.close(load);
    MessagePlugin.error('生成失败');
  }
};

export const findAllParentNames = (data: CommonArray, target: string) => {
  const result: string[] = [];
  let isFind = false;
  const backtrack = (item: CommonObject, keys: string[]) => {
    if (item.id === target) {
      isFind = true;
      result.push(...keys);
      return;
    }
    if (item.children?.length) {
      item.children.forEach((el) => {
        backtrack(el, [...keys, el.label as string]);
      });
    }
  };
  data.forEach((el: CommonObject) => {
    if (isFind) return; // Performance optimization
    backtrack(el, [el.label as string]);
  });
  return result.join(' > ');
};

export const downLoadKnowFile = async (id, name) => {
  let msgLoad = MessagePlugin.loading({
    duration: 0,
    content: '正在下载中...',
  });
  let res = await downLoadFile({
    id: id,
  });
  MessagePlugin.close(msgLoad);
  let blob = new Blob([res]);
  let downloadElement = document.createElement('a');
  let href = window.URL.createObjectURL(blob); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = name; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
  window.URL.revokeObjectURL(href); //释放掉blob对象
};

export const imgUrl = host + '/center/file/viewImg/';

export const upLoadImgUrl = host + '/center/file/upLoadImg';
