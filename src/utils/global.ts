import { useSettingStore } from '@/store';
import { computed } from 'vue';

export default {
  tableHeight: computed(() => {
    if (useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 274;
    } else if (useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 237;
    } else if (!useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 233;
    } else if (!useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 197;
    }
    return 274;
  }),
  tableNoPageHeight: computed(() => {
    if (useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 218;
    } else if (useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 182;
    } else if (!useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 176;
    } else if (!useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 142;
    }
    return 218;
  }),
  alertTableHeight: computed(() => {
    if (useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 'calc(100vh - 372px)';
    } else if (useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 'calc(100vh - 332px)';
    } else if (!useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 'calc(100vh - 332px)';
    } else if (!useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 'calc(100vh - 252px)';
    }
    return 'calc(100vh - 372px)';
  }),
  alertTableNoPageHeight: computed(() => {
    if (useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 'calc(100vh - 344px)';
    } else if (useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 'calc(100vh - 304px)';
    } else if (!useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 'calc(100vh - 304px)';
    } else if (!useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 'calc(100vh - 224px)';
    }
    return 'calc(100vh - 344px)';
  }),
  tabHeight: computed(() => {
    if (useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 'calc(100vh - 322px)';
    } else if (useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 'calc(100vh - 286px)';
    } else if (!useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 'calc(100vh - 280px)';
    } else if (!useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 'calc(100vh - 246px)';
    }
    return 'calc(100vh - 322px)';
  }),
  contentHeight: computed(() => {
    if (useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 'calc(100vh - 142px)';
    } else if (useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 'calc(100vh - 104px)';
    } else if (!useSettingStore().isUseTabsRouter && useSettingStore().showFooter) {
      return 'calc(100vh - 101px)';
    } else if (!useSettingStore().isUseTabsRouter && !useSettingStore().showFooter) {
      return 'calc(100vh - 63px)';
    }
    return 'calc(100vh - 142px)';
  }),
};
