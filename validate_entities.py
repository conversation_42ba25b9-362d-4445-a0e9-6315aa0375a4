#!/usr/bin/env python3
import os
import re
import glob

def validate_entity_file(file_path):
    """验证单个entity文件的基本结构"""
    print(f"验证: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues = []
    
    # 检查基本结构
    if 'implements Serializable' not in content:
        issues.append("缺少 implements Serializable")
    
    if 'serialVersionUID' not in content:
        issues.append("缺少 serialVersionUID")
    
    if '@Serial' not in content:
        issues.append("缺少 @Serial 注解")
    
    if 'extends BaseEntity' in content:
        issues.append("仍然继承 BaseEntity")
    
    # 检查导入
    required_imports = [
        'import java.io.Serializable;',
        'import java.io.Serial;'
    ]
    
    for imp in required_imports:
        if imp not in content:
            issues.append(f"缺少导入: {imp}")
    
    # 检查是否有BaseEntity字段
    has_create_time = 'createTime' in content
    has_creator = 'private String creator' in content
    has_update_time = 'updateTime' in content
    
    if not has_create_time:
        issues.append("缺少 createTime 字段")
    if not has_creator:
        issues.append("缺少 creator 字段")
    if not has_update_time:
        issues.append("缺少 updateTime 字段")
    
    # 检查括号匹配
    open_braces = content.count('{')
    close_braces = content.count('}')
    if open_braces != close_braces:
        issues.append(f"括号不匹配: {{ {open_braces} vs }} {close_braces}")
    
    if issues:
        print(f"  ❌ 发现问题:")
        for issue in issues:
            print(f"    - {issue}")
        return False
    else:
        print(f"  ✅ 验证通过")
        return True

def main():
    # 获取所有entity文件
    entity_dir = "equipment/src/main/java/org/simple/equipment/entity"
    java_files = glob.glob(f"{entity_dir}/*.java")
    
    print(f"开始验证 {len(java_files)} 个entity文件...\n")
    
    valid_count = 0
    invalid_count = 0
    
    for file_path in sorted(java_files):
        if validate_entity_file(file_path):
            valid_count += 1
        else:
            invalid_count += 1
        print()
    
    print(f"验证完成:")
    print(f"  ✅ 通过: {valid_count}")
    print(f"  ❌ 失败: {invalid_count}")
    print(f"  📊 总计: {len(java_files)}")
    
    if invalid_count == 0:
        print("\n🎉 所有entity文件都已成功规范化！")
    else:
        print(f"\n⚠️  还有 {invalid_count} 个文件需要修复")

if __name__ == "__main__":
    main()
