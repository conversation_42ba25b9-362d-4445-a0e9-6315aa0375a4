package org.simple.equipment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.Category;
import org.simple.equipment.service.CategoryService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备分类控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/equipment/category")
@Tag(name = "设备分类管理", description = "设备分类管理")
public class CategoryController {

    @Resource
    private CategoryService categoryService;

    /**
     * 查询分类树形结构
     */
    @GetMapping("/tree")
    @SaCheckPermission("equipment:category:query")
    @Operation(summary = "查询分类树形结构")
    public FrResult<List<Tree<String>>> tree() {
        List<Tree<String>> tree = categoryService.getCategoryTree();
        return FrResult.success(tree);
    }

    /**
     * 查询分类列表
     */
    @GetMapping("/list")
    @SaCheckPermission("equipment:category:query")
    @Operation(summary = "查询分类列表")
    public FrResult<List<Category>> list(Category category) {
        List<Category> list = categoryService.getCategoryList(category);
        return FrResult.success(list);
    }

    /**
     * 查询分类详情
     */
    @GetMapping("/detail/{id}")
    @SaCheckPermission("equipment:category:query")
    @Operation(summary = "查询分类详情")
    public FrResult<Category> detail(@PathVariable String id) {
        Category category = categoryService.getById(id);
        return category != null ? FrResult.success(category) : FrResult.failed("分类不存在");
    }

    /**
     * 新增分类
     */
    @PostMapping("/add")
    @SaCheckPermission("equipment:category:add")
    @Operation(summary = "新增分类")
    public FrResult<?> add(@RequestBody @Validated Category category) {
        return categoryService.saveCategory(category);
    }

    /**
     * 修改分类
     */
    @PostMapping("/edit")
    @SaCheckPermission("equipment:category:edit")
    @Operation(summary = "修改分类")
    public FrResult<?> edit(@RequestBody @Validated Category category) {
        if (StrUtil.isBlank(category.getId())) {
            return FrResult.failed("分类ID不能为空");
        }
        return categoryService.updateCategory(category);
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/del/{id}")
    @SaCheckPermission("equipment:category:del")
    @Operation(summary = "删除分类")
    public FrResult<?> delete(@PathVariable String id) {
        return categoryService.deleteCategory(id);
    }

    /**
     * 检查分类编码是否存在
     */
    @GetMapping("/checkCategoryCode")
    @SaCheckPermission("equipment:category:query")
    @Operation(summary = "检查分类编码是否存在")
    public FrResult<Boolean> checkCategoryCode(@RequestParam String categoryCode,
                                             @RequestParam(required = false) String excludeId) {
        boolean exists = categoryService.checkCategoryCodeExists(categoryCode, excludeId);
        return FrResult.success(exists);
    }

    /**
     * 查询分类选项（用于下拉选择）
     */
    @GetMapping("/options")
    @SaCheckPermission("equipment:category:query")
    @Operation(summary = "查询分类选项")
    public FrResult<List<Tree<String>>> options() {
        List<Tree<String>> tree = categoryService.getCategoryTree();
        return FrResult.success(tree);
    }
}