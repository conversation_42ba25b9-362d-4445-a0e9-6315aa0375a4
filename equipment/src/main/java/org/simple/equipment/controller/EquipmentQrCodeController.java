package org.simple.equipment.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.simple.base.vo.FrResult;
import org.simple.equipment.dto.QrCodeGenerateRequest;
import org.simple.equipment.dto.QrCodeScanRequest;
import org.simple.equipment.dto.QrCodeScanResponse;
import org.simple.equipment.entity.EquipmentQrCode;
import org.simple.equipment.service.EquipmentQrCodeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 设备二维码控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "设备二维码管理", description = "设备二维码生成、扫描和管理相关接口")
@RestController
@RequestMapping("/api/equipment/qrcode")
@RequiredArgsConstructor
@Validated
public class EquipmentQrCodeController {

    private final EquipmentQrCodeService qrCodeService;

    @Operation(summary = "生成设备二维码", description = "为指定设备生成专属二维码")
    @PostMapping("/generate")
    public FrResult<EquipmentQrCode> generateQrCode(@Valid @RequestBody QrCodeGenerateRequest request) {
        EquipmentQrCode qrCode = qrCodeService.generateEquipmentQrCode(request);
        return FrResult.success(qrCode);
    }

    @Operation(summary = "扫描二维码", description = "扫描设备二维码并获取设备信息和推荐模板")
    @PostMapping("/scan")
    public FrResult<QrCodeScanResponse> scanQrCode(@Valid @RequestBody QrCodeScanRequest request) {
        QrCodeScanResponse response = qrCodeService.scanQrCode(request);
        return FrResult.success(response);
    }

    @Operation(summary = "获取设备二维码", description = "根据设备ID获取当前有效的二维码")
    @GetMapping("/equipment/{equipmentId}")
    public FrResult<EquipmentQrCode> getQrCodeByEquipmentId(@PathVariable String equipmentId) {
        EquipmentQrCode qrCode = qrCodeService.getQrCodeByEquipmentId(equipmentId);
        return FrResult.success(qrCode);
    }

    @Operation(summary = "重新生成二维码", description = "使原二维码失效并生成新的二维码")
    @PostMapping("/regenerate/{equipmentId}")
    public FrResult<EquipmentQrCode> regenerateQrCode(@PathVariable String equipmentId) {
        EquipmentQrCode qrCode = qrCodeService.regenerateQrCode(equipmentId);
        return FrResult.success(qrCode);
    }

    @Operation(summary = "使二维码失效", description = "使指定设备的二维码失效")
    @PostMapping("/deactivate/{equipmentId}")
    public FrResult<Void> deactivateQrCode(@PathVariable String equipmentId) {
        qrCodeService.deactivateQrCode(equipmentId);
        return FrResult.success();
    }
}