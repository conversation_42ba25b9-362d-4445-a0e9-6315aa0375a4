package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
// import org.simple.base.util.ResultUtil; // 已替换为FrResult
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.TaskType;
import org.simple.equipment.service.TaskTypeService;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 任务类型管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/equipment/task-type")
@RequiredArgsConstructor
@Tag(name = "任务类型管理", description = "任务类型管理相关接口")
public class TaskTypeController {

    private final TaskTypeService taskTypeService;

    @Operation(summary = "获取任务类型列表", description = "获取任务类型列表")
    @GetMapping("/list")
    public FrResult<Page<TaskType>> list(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "类型编码") @RequestParam(required = false) String typeCode,
            @Parameter(description = "类型名称") @RequestParam(required = false) String typeName,
            @Parameter(description = "状态") @RequestParam(required = false) String status) {

        QueryWrapper<TaskType> queryWrapper = new QueryWrapper<>();
        //queryWrapper.eq("tenant_id", AuthUtil.getTenantId());
        
        if (typeCode != null && !typeCode.trim().isEmpty()) {
            queryWrapper.like("type_code", typeCode);
        }
        if (typeName != null && !typeName.trim().isEmpty()) {
            queryWrapper.like("type_name", typeName);
        }
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByDesc("create_date");
        
        Page<TaskType> pageResult = taskTypeService.page(new Page<>(page, size), queryWrapper);
        return FrResult.success(pageResult);
    }

    @Operation(summary = "获取启用的任务类型", description = "获取启用的任务类型")
    @GetMapping("/enabled")
    public FrResult<List<TaskType>> getEnabledTaskTypes() {
        List<TaskType> taskTypes = taskTypeService.getEnabledTaskTypes();
        return FrResult.success(taskTypes);
    }

    @Operation(summary = "获取任务类型详情", description = "根据ID获取任务类型详情")
    @GetMapping("/{id}")
    public FrResult<TaskType> getById(@PathVariable String id) {
        TaskType taskType = taskTypeService.getById(id);
        if (taskType == null) {
            return FrResult.failed("任务类型不存在");
        }
        return FrResult.success(taskType);
    }

    @Operation(summary = "创建任务类型", description = "创建新的任务类型")
    @PostMapping
    public FrResult<Boolean> create(@Valid @RequestBody TaskType taskType) {
        boolean success = taskTypeService.createTaskType(taskType);
        return FrResult.success(success);
    }

    @Operation(summary = "更新任务类型", description = "更新任务类型信息")
    @PutMapping
    public FrResult<Boolean> update(@Valid @RequestBody TaskType taskType) {
        boolean success = taskTypeService.updateTaskType(taskType);
        return FrResult.success(success);
    }

    @Operation(summary = "删除任务类型", description = "删除指定的任务类型")
    @DeleteMapping("/{id}")
    public FrResult<Boolean> delete(@PathVariable String id) {
        boolean success = taskTypeService.deleteTaskType(id);
        return FrResult.success(success);
    }

    @Operation(summary = "切换任务类型状态", description = "启用或禁用任务类型")
    @PutMapping("/{id}/status")
    public FrResult<Boolean> toggleStatus(
            @PathVariable String id,
            @RequestParam String status) {
        boolean success = taskTypeService.toggleStatus(id, status);
        return FrResult.success(success);
    }

    @Operation(summary = "检查类型编码是否存在", description = "检查类型编码是否已存在")
    @GetMapping("/check-code")
    public FrResult<Boolean> checkTypeCode(
            @RequestParam String typeCode,
            @RequestParam(required = false) String excludeId) {
        boolean exists = taskTypeService.checkTypeCodeExists(typeCode, excludeId);
        return FrResult.success(exists);
    }
}