package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.InspectionTemplate;
import org.simple.equipment.service.InspectionTemplateService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备点检模板控制器
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Tag(name = "设备点检模板管理", description = "设备点检模板管理相关接口")
@RestController
@RequestMapping("/api/equipment/inspection-template")
@RequiredArgsConstructor
public class InspectionTemplateController {

    private final InspectionTemplateService inspectionTemplateService;

    @Operation(summary = "分页查询点检模板")
    @GetMapping("/list")
    public FrResult<IPage<InspectionTemplate>> getTemplatePage(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "模板编码") @RequestParam(required = false) String templateCode,
            @Parameter(description = "模板名称") @RequestParam(required = false) String templateName,
            @Parameter(description = "分类ID") @RequestParam(required = false) String categoryId,
            @Parameter(description = "周期类型") @RequestParam(required = false) String cycleType,
            @Parameter(description = "模板状态") @RequestParam(required = false) String templateStatus,
            @Parameter(description = "模板类型") @RequestParam(required = false) String templateType) {

        Page<InspectionTemplate> page = new Page<>(current, size);
        IPage<InspectionTemplate> result = inspectionTemplateService.getTemplatePage(page, templateCode, templateName,
                categoryId, cycleType, templateStatus, templateType);
        return FrResult.success(result);
    }

    @Operation(summary = "创建点检模板")
    @PostMapping
    public FrResult<Boolean> createTemplate(@Valid @RequestBody InspectionTemplate template) {
        boolean success = inspectionTemplateService.createTemplate(template);
        return FrResult.success(success);
    }

    @Operation(summary = "更新点检模板")
    @PutMapping
    public FrResult<Boolean> updateTemplate(@Valid @RequestBody InspectionTemplate template) {
        boolean success = inspectionTemplateService.updateTemplate(template);
        return FrResult.success(success);
    }

    @Operation(summary = "删除点检模板")
    @DeleteMapping("/{templateId}")
    public FrResult<Boolean> deleteTemplate(@PathVariable String templateId) {
        boolean success = inspectionTemplateService.deleteTemplate(templateId);
        return FrResult.success(success);
    }

    @Operation(summary = "根据ID获取模板详情")
    @GetMapping("/{templateId}")
    public FrResult<InspectionTemplate> getTemplateById(@PathVariable String templateId) {
        InspectionTemplate template = inspectionTemplateService.getTemplateById(templateId);
        return FrResult.success(template);
    }

    @Operation(summary = "根据分类查询模板")
    @GetMapping("/category/{categoryId}")
    public FrResult<List<InspectionTemplate>> getTemplatesByCategory(@PathVariable String categoryId) {
        List<InspectionTemplate> templates = inspectionTemplateService.getTemplatesByCategory(categoryId);
        return FrResult.success(templates);
    }

    @Operation(summary = "根据设备型号查询模板")
    @GetMapping("/model/{equipmentModel}")
    public FrResult<List<InspectionTemplate>> getTemplatesByModel(@PathVariable String equipmentModel) {
        List<InspectionTemplate> templates = inspectionTemplateService.getTemplatesByModel(equipmentModel);
        return FrResult.success(templates);
    }

    @Operation(summary = "查询已发布的模板")
    @GetMapping("/published")
    public FrResult<List<InspectionTemplate>> getPublishedTemplates() {
        List<InspectionTemplate> templates = inspectionTemplateService.getPublishedTemplates();
        return FrResult.success(templates);
    }

    @Operation(summary = "根据周期类型查询模板")
    @GetMapping("/cycle/{cycleType}")
    public FrResult<List<InspectionTemplate>> getTemplatesByCycle(@PathVariable String cycleType) {
        List<InspectionTemplate> templates = inspectionTemplateService.getTemplatesByCycle(cycleType);
        return FrResult.success(templates);
    }

    @Operation(summary = "检查模板编码是否存在")
    @GetMapping("/check-code")
    public FrResult<Boolean> checkTemplateCodeExists(
            @Parameter(description = "模板编码") @RequestParam String templateCode,
            @Parameter(description = "排除的模板ID") @RequestParam(required = false) String excludeId) {
        boolean exists = inspectionTemplateService.checkTemplateCodeExists(templateCode, excludeId);
        return FrResult.success(exists);
    }

    @Operation(summary = "生成模板编码")
    @GetMapping("/generate-code")
    public FrResult<String> generateTemplateCode(@Parameter(description = "分类ID") @RequestParam(required = false) String categoryId) {
        String code = inspectionTemplateService.generateTemplateCode(categoryId);
        return FrResult.success(code);
    }

    @Operation(summary = "发布模板")
    @PostMapping("/{templateId}/publish")
    public FrResult<Boolean> publishTemplate(@PathVariable String templateId, @RequestParam(required = false) String reviewComment) {
        boolean success = inspectionTemplateService.publishTemplate(templateId, reviewComment);
        return FrResult.success(success);
    }

    @Operation(summary = "归档模板")
    @PostMapping("/{templateId}/archive")
    public FrResult<Boolean> archiveTemplate(@PathVariable String templateId) {
        boolean success = inspectionTemplateService.archiveTemplate(templateId);
        return FrResult.success(success);
    }

    @Operation(summary = "复制模板")
    @PostMapping("/{sourceTemplateId}/copy")
    public FrResult<Boolean> copyTemplate(@PathVariable String sourceTemplateId, @RequestParam String templateName) {
        boolean success = inspectionTemplateService.copyTemplate(sourceTemplateId, templateName);
        return FrResult.success(success);
    }

    @Operation(summary = "批量更新状态")
    @PostMapping("/batch-update-status")
    public FrResult<Boolean> batchUpdateStatus(@RequestBody List<String> templateIds, @RequestParam String status) {
        boolean success = inspectionTemplateService.batchUpdateStatus(templateIds, status);
        return FrResult.success(success);
    }

    @Operation(summary = "更新使用次数")
    @PostMapping("/{templateId}/update-usage")
    public FrResult<Void> updateUsageCount(@PathVariable String templateId) {
        inspectionTemplateService.updateUsageCount(templateId);
        return FrResult.success();
    }

    @Operation(summary = "获取模板统计信息")
    @GetMapping("/statistics")
    public FrResult<List<InspectionTemplate>> getTemplateStatistics() {
        List<InspectionTemplate> statistics = inspectionTemplateService.getTemplateStatistics();
        return FrResult.success(statistics);
    }

    @Operation(summary = "验证模板有效性")
    @GetMapping("/{templateId}/validate")
    public FrResult<Boolean> validateTemplate(@PathVariable String templateId) {
        boolean valid = inspectionTemplateService.validateTemplate(templateId);
        return FrResult.success(valid);
    }

    @Operation(summary = "导出模板")
    @GetMapping("/{templateId}/export")
    public FrResult<Void> exportTemplate(@PathVariable String templateId) {
        inspectionTemplateService.exportTemplate(templateId);
        return FrResult.success();
    }

    @Operation(summary = "导入模板")
    @PostMapping("/import")
    public FrResult<Boolean> importTemplate(@RequestParam String templateData) {
        boolean success = inspectionTemplateService.importTemplate(templateData);
        return FrResult.success(success);
    }
}