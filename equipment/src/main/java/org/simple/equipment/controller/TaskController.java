package org.simple.equipment.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.Task;
import org.simple.equipment.service.TaskService;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 任务管理控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/equipment/task")
@RequiredArgsConstructor
@Tag(name = "任务管理", description = "任务管理相关接口")
public class TaskController {

    private final TaskService taskService;

    @Operation(summary = "获取任务列表", description = "获取任务列表")
    @GetMapping("/list")
    public FrResult<Page<Task>> list(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "任务编码") @RequestParam(required = false) String taskCode,
            @Parameter(description = "任务名称") @RequestParam(required = false) String taskName,
            @Parameter(description = "任务类型ID") @RequestParam(required = false) String taskTypeId,
            @Parameter(description = "设备ID") @RequestParam(required = false) String equipmentId,
            @Parameter(description = "分配人员ID") @RequestParam(required = false) String assignedUserId,
            @Parameter(description = "任务状态") @RequestParam(required = false) String taskStatus,
            @Parameter(description = "优先级") @RequestParam(required = false) String priority) {

        QueryWrapper<Task> queryWrapper = new QueryWrapper<>();
        //queryWrapper.eq("tenant_id", AuthUtil.getTenantId());
        
        if (taskCode != null && !taskCode.trim().isEmpty()) {
            queryWrapper.like("task_code", taskCode);
        }
        if (taskName != null && !taskName.trim().isEmpty()) {
            queryWrapper.like("task_name", taskName);
        }
        if (taskTypeId != null && !taskTypeId.trim().isEmpty()) {
            queryWrapper.eq("task_type_id", taskTypeId);
        }
        if (equipmentId != null && !equipmentId.trim().isEmpty()) {
            queryWrapper.eq("equipment_id", equipmentId);
        }
        if (assignedUserId != null && !assignedUserId.trim().isEmpty()) {
            queryWrapper.eq("assigned_user_id", assignedUserId);
        }
        if (taskStatus != null && !taskStatus.trim().isEmpty()) {
            queryWrapper.eq("task_status", taskStatus);
        }
        if (priority != null && !priority.trim().isEmpty()) {
            queryWrapper.eq("priority", priority);
        }
        
        queryWrapper.orderByDesc("create_date");
        
        Page<Task> pageResult = taskService.page(new Page<>(page, size), queryWrapper);
        return FrResult.success(pageResult);
    }

    @Operation(summary = "获取任务详情", description = "根据ID获取任务详情")
    @GetMapping("/{id}")
    public FrResult<Task> getById(@PathVariable String id) {
        Task task = taskService.getById(id);
        if (task == null) {
            return FrResult.failed("任务不存在");
        }
        return FrResult.success(task);
    }

    @Operation(summary = "创建任务", description = "创建新的任务")
    @PostMapping
    public FrResult<Boolean> create(@Valid @RequestBody Task task) {
        boolean success = taskService.createTask(task);
        return FrResult.success(success);
    }

    @Operation(summary = "更新任务", description = "更新任务信息")
    @PutMapping
    public FrResult<Boolean> update(@Valid @RequestBody Task task) {
        boolean success = taskService.updateTask(task);
        return FrResult.success(success);
    }

    @Operation(summary = "删除任务", description = "删除指定的任务")
    @DeleteMapping("/{id}")
    public FrResult<Boolean> delete(@PathVariable String id) {
        boolean success = taskService.deleteTask(id);
        return FrResult.success(success);
    }

    @Operation(summary = "分配任务", description = "将任务分配给指定用户")
    @PutMapping("/{id}/assign")
    public FrResult<Boolean> assign(
            @PathVariable String id,
            @RequestParam String userId,
            @RequestParam String userName) {
        boolean success = taskService.assignTask(id, userId, userName);
        return FrResult.success(success);
    }

    @Operation(summary = "开始任务", description = "开始执行任务")
    @PutMapping("/{id}/start")
    public FrResult<Boolean> start(@PathVariable String id) {
        boolean success = taskService.startTask(id);
        return FrResult.success(success);
    }

    @Operation(summary = "完成任务", description = "完成任务")
    @PutMapping("/{id}/complete")
    public FrResult<Boolean> complete(
            @PathVariable String id,
            @RequestParam String result) {
        boolean success = taskService.completeTask(id, result);
        return FrResult.success(success);
    }

    @Operation(summary = "取消任务", description = "取消任务")
    @PutMapping("/{id}/cancel")
    public FrResult<Boolean> cancel(
            @PathVariable String id,
            @RequestParam String reason) {
        boolean success = taskService.cancelTask(id, reason);
        return FrResult.success(success);
    }

    @Operation(summary = "获取任务统计", description = "获取任务统计信息")
    @GetMapping("/statistics")
    public FrResult<Map<String, Object>> getStatistics() {
        Map<String, Object> statistics = taskService.getTaskStatistics();
        return FrResult.success(statistics);
    }

    @Operation(summary = "获取逾期任务", description = "获取逾期任务列表")
    @GetMapping("/overdue")
    public FrResult<List<Task>> getOverdueTasks() {
        List<Task> tasks = taskService.getOverdueTasks();
        return FrResult.success(tasks);
    }

    @Operation(summary = "获取用户任务", description = "获取指定用户的任务列表")
    @GetMapping("/user/{userId}")
    public FrResult<List<Task>> getUserTasks(@PathVariable String userId) {
        List<Task> tasks = taskService.getUserTasks(userId);
        return FrResult.success(tasks);
    }

    @Operation(summary = "获取设备任务", description = "获取指定设备的任务列表")
    @GetMapping("/equipment/{equipmentId}")
    public FrResult<List<Task>> getEquipmentTasks(@PathVariable String equipmentId) {
        List<Task> tasks = taskService.getEquipmentTasks(equipmentId);
        return FrResult.success(tasks);
    }

    @Operation(summary = "检查任务编码是否存在", description = "检查任务编码是否已存在")
    @GetMapping("/check-code")
    public FrResult<Boolean> checkTaskCode(
            @RequestParam String taskCode,
            @RequestParam(required = false) String excludeId) {
        boolean exists = taskService.checkTaskCodeExists(taskCode, excludeId);
        return FrResult.success(exists);
    }

    @Operation(summary = "生成任务编码", description = "生成任务编码")
    @GetMapping("/generate-code")
    public FrResult<String> generateTaskCode(@RequestParam String taskTypeId) {
        String taskCode = taskService.generateTaskCode(taskTypeId);
        return FrResult.success(taskCode);
    }

    @Operation(summary = "检查逾期任务", description = "检查并更新逾期任务")
    @PostMapping("/check-overdue")
    public FrResult<Boolean> checkOverdue() {
        taskService.checkAndUpdateOverdueTasks();
        return FrResult.success(true);
    }

    @Operation(summary = "发送任务提醒", description = "发送任务提醒")
    @PostMapping("/{id}/remind")
    public FrResult<Boolean> remind(@PathVariable String id) {
        taskService.sendTaskReminder(id);
        return FrResult.success(true);
    }
}