package org.simple.equipment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.AttributeTemplate;
import org.simple.equipment.service.AttributeTemplateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 设备属性模板控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/equipment/attributeTemplate")
@Tag(name = "设备属性模板管理", description = "设备属性模板管理")
public class AttributeTemplateController {

    @Resource
    private AttributeTemplateService attributeTemplateService;

    /**
     * 查询属性模板列表
     */
    @GetMapping("/list")
    @SaCheckPermission("equipment:attributeTemplate:query")
    @Operation(summary = "查询属性模板列表")
    public FrResult<List<AttributeTemplate>> list(AttributeTemplate template) {
        List<AttributeTemplate> list = attributeTemplateService.getTemplateList(template);
        return FrResult.success(list);
    }

    /**
     * 查询有效的属性模板列表
     */
    @GetMapping("/activeList")
    @SaCheckPermission("equipment:attributeTemplate:query")
    @Operation(summary = "查询有效的属性模板列表")
    public FrResult<List<AttributeTemplate>> activeList() {
        List<AttributeTemplate> list = attributeTemplateService.getActiveTemplateList();
        return FrResult.success(list);
    }

    /**
     * 根据设备分类查询属性模板
     */
    @GetMapping("/listByCategory")
    @SaCheckPermission("equipment:attributeTemplate:query")
    @Operation(summary = "根据设备分类查询属性模板")
    public FrResult<List<AttributeTemplate>> listByCategory(@RequestParam(required = false) String categoryId,
                                                            @RequestParam(required = false) String typeId) {
        List<AttributeTemplate> list = attributeTemplateService.getTemplatesByCategory(categoryId, typeId);
        return FrResult.success(list);
    }

    /**
     * 查询属性模板详情
     */
    @GetMapping("/detail/{id}")
    @SaCheckPermission("equipment:attributeTemplate:query")
    @Operation(summary = "查询属性模板详情")
    public FrResult<Map<String, Object>> detail(@PathVariable String id) {
        Map<String, Object> detail = attributeTemplateService.getTemplateDetail(id);
        return detail != null ? FrResult.success(detail) : FrResult.failed("模板不存在");
    }

    /**
     * 新增属性模板
     */
    @PostMapping("/add")
    @SaCheckPermission("equipment:attributeTemplate:add")
    @Operation(summary = "新增属性模板")
    public FrResult<?> add(@RequestBody @Validated Map<String, Object> requestData) {
        AttributeTemplate template = convertToTemplate(requestData);
        List<String> fieldIds = (List<String>) requestData.get("fieldIds");
        return attributeTemplateService.saveAttributeTemplate(template, fieldIds);
    }

    /**
     * 修改属性模板
     */
    @PostMapping("/edit")
    @SaCheckPermission("equipment:attributeTemplate:edit")
    @Operation(summary = "修改属性模板")
    public FrResult<?> edit(@RequestBody @Validated Map<String, Object> requestData) {
        AttributeTemplate template = convertToTemplate(requestData);
        if (StrUtil.isBlank(template.getId())) {
            return FrResult.failed("模板ID不能为空");
        }
        List<String> fieldIds = (List<String>) requestData.get("fieldIds");
        return attributeTemplateService.updateAttributeTemplate(template, fieldIds);
    }

    /**
     * 删除属性模板
     */
    @DeleteMapping("/del/{id}")
    @SaCheckPermission("equipment:attributeTemplate:del")
    @Operation(summary = "删除属性模板")
    public FrResult<?> delete(@PathVariable String id) {
        return attributeTemplateService.deleteAttributeTemplate(id);
    }

    /**
     * 检查模板编码是否存在
     */
    @GetMapping("/checkTemplateCode")
    @SaCheckPermission("equipment:attributeTemplate:query")
    @Operation(summary = "检查模板编码是否存在")
    public FrResult<Boolean> checkTemplateCode(@RequestParam String templateCode,
                                               @RequestParam(required = false) String excludeId) {
        boolean exists = attributeTemplateService.checkTemplateCodeExists(templateCode, excludeId);
        return FrResult.success(exists);
    }

    /**
     * 更新模板状态
     */
    @PostMapping("/updateStatus")
    @SaCheckPermission("equipment:attributeTemplate:edit")
    @Operation(summary = "更新模板状态")
    public FrResult<?> updateStatus(@RequestParam String id,
                                    @RequestParam String isEnabled) {
        return attributeTemplateService.updateTemplateStatus(id, isEnabled);
    }

    /**
     * 复制模板
     */
    @PostMapping("/copy")
    @SaCheckPermission("equipment:attributeTemplate:add")
    @Operation(summary = "复制模板")
    public FrResult<?> copy(@RequestParam String templateId,
                           @RequestParam String newTemplateName,
                           @RequestParam String newTemplateCode) {
        return attributeTemplateService.copyTemplate(templateId, newTemplateName, newTemplateCode);
    }

    /**
     * 导入模板
     */
    @PostMapping("/import")
    @SaCheckPermission("equipment:attributeTemplate:add")
    @Operation(summary = "导入模板")
    public FrResult<?> importTemplate(@RequestBody String templateData) {
        return attributeTemplateService.importTemplate(templateData);
    }

    /**
     * 导出模板
     */
    @GetMapping("/export/{id}")
    @SaCheckPermission("equipment:attributeTemplate:query")
    @Operation(summary = "导出模板")
    public FrResult<String> export(@PathVariable String id) {
        return attributeTemplateService.exportTemplate(id);
    }

    /**
     * 应用模板到设备
     */
    @PostMapping("/applyToAssets")
    @SaCheckPermission("equipment:attributeTemplate:apply")
    @Operation(summary = "应用模板到设备")
    public FrResult<?> applyToAssets(@RequestParam String templateId,
                                     @RequestParam List<String> assetIds,
                                     @RequestParam(defaultValue = "full") String applyMode) {
        return attributeTemplateService.applyTemplateToAssets(templateId, assetIds, applyMode);
    }

    /**
     * 从设备创建模板
     */
    @PostMapping("/createFromAsset")
    @SaCheckPermission("equipment:attributeTemplate:add")
    @Operation(summary = "从设备创建模板")
    public FrResult<?> createFromAsset(@RequestParam String assetId,
                                       @RequestParam String templateName,
                                       @RequestParam String templateCode) {
        return attributeTemplateService.createTemplateFromAsset(assetId, templateName, templateCode);
    }

    /**
     * 查询模板使用统计
     */
    @GetMapping("/usageStatistics")
    @SaCheckPermission("equipment:attributeTemplate:query")
    @Operation(summary = "查询模板使用统计")
    public FrResult<List<Map<String, Object>>> usageStatistics() {
        List<Map<String, Object>> statistics = attributeTemplateService.getUsageStatistics();
        return FrResult.success(statistics);
    }

    /**
     * 升级模板版本
     */
    @PostMapping("/upgradeVersion")
    @SaCheckPermission("equipment:attributeTemplate:edit")
    @Operation(summary = "升级模板版本")
    public FrResult<?> upgradeVersion(@RequestParam String templateId,
                                      @RequestParam String newVersion) {
        return attributeTemplateService.upgradeTemplateVersion(templateId, newVersion);
    }

    /**
     * 转换请求数据为模板对象
     */
    private AttributeTemplate convertToTemplate(Map<String, Object> requestData) {
        AttributeTemplate template = new AttributeTemplate();
        template.setId((String) requestData.get("id"));
        template.setTemplateCode((String) requestData.get("templateCode"));
        template.setTemplateName((String) requestData.get("templateName"));
        template.setTemplateDesc((String) requestData.get("templateDesc"));
        template.setCategoryId((String) requestData.get("categoryId"));
        template.setTypeId((String) requestData.get("typeId"));
        template.setParentId((String) requestData.get("parentId"));
        template.setTemplateVersion((String) requestData.get("templateVersion"));
        template.setIsSystem((String) requestData.get("isSystem"));
        template.setIsEnabled((String) requestData.get("isEnabled"));
        
        Object sortOrder = requestData.get("sortOrder");
        if (sortOrder instanceof Integer) {
            template.setSortOrder((Integer) sortOrder);
        }
        
        template.setTemplateConfig((String) requestData.get("templateConfig"));
        return template;
    }
}