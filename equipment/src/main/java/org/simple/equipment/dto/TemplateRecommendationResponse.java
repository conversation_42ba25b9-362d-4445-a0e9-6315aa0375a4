package org.simple.equipment.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 模板推荐响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class TemplateRecommendationResponse {

    /**
     * 推荐的主要模板ID
     */
    private String primaryTemplateId;

    /**
     * 推荐的主要模板信息
     */
    private Map<String, Object> primaryTemplateInfo;

    /**
     * 推荐的次要模板列表
     */
    private List<RecommendedTemplate> secondaryTemplates;

    /**
     * 推荐置信度(0-1)
     */
    private Double confidence;

    /**
     * 推荐原因
     */
    private String reason;

    /**
     * 推荐的模板内部类
     */
    @Data
    public static class RecommendedTemplate {
        /**
         * 模板ID
         */
        private String templateId;
        
        /**
         * 模板名称
         */
        private String templateName;
        
        /**
         * 推荐权重
         */
        private Double weight;
        
        /**
         * 推荐原因
         */
        private String reason;
    }
}