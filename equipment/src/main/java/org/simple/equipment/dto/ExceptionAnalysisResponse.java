package org.simple.equipment.dto;

import lombok.Data;
import org.simple.equipment.entity.ExceptionHandlingRecord.ExceptionLevel;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 异常分析响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class ExceptionAnalysisResponse {

    /**
     * 异常等级
     */
    private ExceptionLevel exceptionLevel;

    /**
     * 异常类型
     */
    private String exceptionType;

    /**
     * 分析置信度
     */
    private BigDecimal confidence;

    /**
     * 分析原因
     */
    private String analysisReason;

    /**
     * 推荐处理动作
     */
    private List<RecommendedAction> recommendedActions;

    /**
     * 预计处理时间(分钟)
     */
    private Integer estimatedHandlingTime;

    /**
     * 建议截止时间
     */
    private LocalDateTime suggestedDeadline;

    /**
     * 需要通知的人员
     */
    private List<NotificationTarget> notificationTargets;

    /**
     * 是否需要立即处理
     */
    private Boolean requiresImmediateAction;

    /**
     * 是否需要生成工单
     */
    private Boolean requiresWorkOrder;

    /**
     * 风险评估
     */
    private RiskAssessment riskAssessment;

    /**
     * 历史相似异常
     */
    private List<SimilarException> similarExceptions;

    /**
     * 额外信息
     */
    private Map<String, Object> additionalInfo;

    /**
     * 推荐处理动作内部类
     */
    @Data
    public static class RecommendedAction {
        /**
         * 动作类型
         */
        private String actionType;
        
        /**
         * 动作描述
         */
        private String description;
        
        /**
         * 优先级
         */
        private Integer priority;
        
        /**
         * 是否自动执行
         */
        private Boolean autoExecute;
    }

    /**
     * 通知目标内部类
     */
    @Data
    public static class NotificationTarget {
        /**
         * 目标类型
         */
        private String targetType;
        
        /**
         * 目标ID
         */
        private String targetId;
        
        /**
         * 目标名称
         */
        private String targetName;
        
        /**
         * 通知方式
         */
        private List<String> notificationMethods;
        
        /**
         * 通知紧急程度
         */
        private String urgency;
    }

    /**
     * 风险评估内部类
     */
    @Data
    public static class RiskAssessment {
        /**
         * 风险等级
         */
        private String riskLevel;
        
        /**
         * 风险评分
         */
        private BigDecimal riskScore;
        
        /**
         * 潜在影响
         */
        private String potentialImpact;
        
        /**
         * 影响范围
         */
        private String impactScope;
    }

    /**
     * 相似异常内部类
     */
    @Data
    public static class SimilarException {
        /**
         * 异常ID
         */
        private String exceptionId;
        
        /**
         * 相似度
         */
        private BigDecimal similarity;
        
        /**
         * 处理结果
         */
        private String handlingResult;
        
        /**
         * 处理时间
         */
        private Integer handlingDuration;
    }
}