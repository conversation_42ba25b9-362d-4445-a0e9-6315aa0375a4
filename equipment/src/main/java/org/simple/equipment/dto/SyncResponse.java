package org.simple.equipment.dto;

import lombok.Data;
import org.simple.equipment.entity.OfflineSyncRecord.SyncStatus;

import java.util.List;
import java.util.Map;

/**
 * 同步响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class SyncResponse {

    /**
     * 同步是否成功
     */
    private Boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 同步结果列表
     */
    private List<SyncResult> results;

    /**
     * 冲突数据列表
     */
    private List<ConflictData> conflicts;

    /**
     * 同步统计
     */
    private SyncStatistics statistics;

    /**
     * 服务器时间戳
     */
    private Long serverTimestamp;

    /**
     * 下次建议同步时间
     */
    private Long nextSyncTime;

    /**
     * 额外信息
     */
    private Map<String, Object> metadata;

    /**
     * 同步结果内部类
     */
    @Data
    public static class SyncResult {
        /**
         * 实体ID
         */
        private String entityId;
        
        /**
         * 实体类型
         */
        private String entityType;
        
        /**
         * 同步状态
         */
        private SyncStatus status;
        
        /**
         * 错误消息
         */
        private String errorMessage;
        
        /**
         * 服务器时间戳
         */
        private Long serverTimestamp;
        
        /**
         * 新生成的ID(适用于创建操作)
         */
        private String newEntityId;
    }

    /**
     * 冲突数据内部类
     */
    @Data
    public static class ConflictData {
        /**
         * 实体ID
         */
        private String entityId;
        
        /**
         * 实体类型
         */
        private String entityType;
        
        /**
         * 冲突字段
         */
        private List<String> conflictFields;
        
        /**
         * 客户端数据
         */
        private Map<String, Object> clientData;
        
        /**
         * 服务器数据
         */
        private Map<String, Object> serverData;
        
        /**
         * 冲突类型
         */
        private String conflictType;
        
        /**
         * 建议解决方案
         */
        private String suggestedResolution;
    }

    /**
     * 同步统计内部类
     */
    @Data
    public static class SyncStatistics {
        /**
         * 总数
         */
        private Integer total;
        
        /**
         * 成功数
         */
        private Integer success;
        
        /**
         * 失败数
         */
        private Integer failed;
        
        /**
         * 冲突数
         */
        private Integer conflicts;
        
        /**
         * 跳过数
         */
        private Integer skipped;
        
        /**
         * 同步耗时(毫秒)
         */
        private Long duration;
        
        /**
         * 数据传输大小(字节)
         */
        private Long dataSize;
    }

    /**
     * 创建成功响应
     */
    public static SyncResponse success(String message) {
        SyncResponse response = new SyncResponse();
        response.setSuccess(true);
        response.setMessage(message);
        response.setServerTimestamp(System.currentTimeMillis());
        return response;
    }

    /**
     * 创建失败响应
     */
    public static SyncResponse failed(String message) {
        SyncResponse response = new SyncResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setServerTimestamp(System.currentTimeMillis());
        return response;
    }
}