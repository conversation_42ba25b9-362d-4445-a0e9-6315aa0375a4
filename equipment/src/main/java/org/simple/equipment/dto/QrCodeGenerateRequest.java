package org.simple.equipment.dto;

import lombok.Data;
import org.simple.equipment.entity.EquipmentQrCode.QrCodeType;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 二维码生成请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class QrCodeGenerateRequest {

    /**
     * 设备ID
     */
    @NotBlank(message = "设备ID不能为空")
    private String equipmentId;

    /**
     * 二维码类型
     */
    @NotNull(message = "二维码类型不能为空")
    private QrCodeType qrType;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 自定义数据
     */
    private Map<String, Object> customData;

    /**
     * 是否生成图片
     */
    private Boolean generateImage = true;

    /**
     * 图片大小(像素)
     */
    private Integer imageSize = 200;
}