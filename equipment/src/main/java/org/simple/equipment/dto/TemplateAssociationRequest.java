package org.simple.equipment.dto;

import lombok.Data;
import org.simple.equipment.entity.EquipmentTemplateAssociation.AssociationType;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 模板关联请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class TemplateAssociationRequest {

    /**
     * 设备ID
     */
    @NotBlank(message = "设备ID不能为空")
    private String equipmentId;

    /**
     * 模板ID
     */
    @NotBlank(message = "模板ID不能为空")
    private String templateId;

    /**
     * 关联类型
     */
    @NotNull(message = "关联类型不能为空")
    private AssociationType associationType;

    /**
     * 是否自动分配
     */
    private Boolean autoAssigned = false;

    /**
     * 分配规则
     */
    private Map<String, Object> assignmentRule;

    /**
     * 优先级
     */
    private Integer priority = 0;

    /**
     * 生效开始时间
     */
    private LocalDateTime effectiveFrom;

    /**
     * 生效结束时间
     */
    private LocalDateTime effectiveTo;
}