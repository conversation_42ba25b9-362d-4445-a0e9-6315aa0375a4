package org.simple.equipment.schedule;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simple.equipment.service.TaskService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 任务定时调度器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskScheduler {

    private final TaskService taskService;

    /**
     * 检查逾期任务
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 60 * 60 * 1000)
    public void checkOverdueTasks() {
        try {
            log.info("开始检查逾期任务");
            taskService.checkAndUpdateOverdueTasks();
            log.info("逾期任务检查完成");
        } catch (Exception e) {
            log.error("检查逾期任务时发生错误", e);
        }
    }

    /**
     * 任务提醒
     * 每天上午9点执行
     */
    @Scheduled(cron = "0 0 9 * * ?")
    public void taskReminder() {
        try {
            log.info("开始发送任务提醒");
            // TODO: 实现任务提醒逻辑
            // 可以查询即将到期的任务，发送提醒
            log.info("任务提醒发送完成");
        } catch (Exception e) {
            log.error("发送任务提醒时发生错误", e);
        }
    }
}