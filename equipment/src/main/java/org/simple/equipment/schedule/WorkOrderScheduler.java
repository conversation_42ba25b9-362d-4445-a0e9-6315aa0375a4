package org.simple.equipment.schedule;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simple.equipment.entity.WorkOrder;
import org.simple.equipment.service.WorkOrderNotificationService;
import org.simple.equipment.service.WorkOrderService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 工单定时调度器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WorkOrderScheduler {

    private final WorkOrderService workOrderService;
    private final WorkOrderNotificationService notificationService;

    /**
     * 检查超时工单
     * 每2小时执行一次
     */
    @Scheduled(fixedRate = 2 * 60 * 60 * 1000)
    public void checkTimeoutWorkOrders() {
        try {
            log.info("开始检查超时工单");
            
            // 检查24小时超时的工单
            List<WorkOrder> timeoutOrders = workOrderService.getTimeoutWorkOrders(24);
            
            for (WorkOrder workOrder : timeoutOrders) {
                // 发送超时提醒
                notificationService.sendTimeoutReminder(workOrder);
            }
            
            log.info("超时工单检查完成，共发现{}个超时工单", timeoutOrders.size());
        } catch (Exception e) {
            log.error("检查超时工单时发生错误", e);
        }
    }

    /**
     * 工单状态统计和报告
     * 每天早上8点执行
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void generateDailyReport() {
        try {
            log.info("开始生成工单日报");
            
            // 获取工单统计信息
            var statistics = workOrderService.getWorkOrderStatistics();
            
            // TODO: 生成并发送日报
            // 1. 统计各状态工单数量
            // 2. 统计各类型工单数量
            // 3. 统计平均处理时长
            // 4. 发送给管理员
            
            log.info("工单日报生成完成: {}", statistics);
        } catch (Exception e) {
            log.error("生成工单日报时发生错误", e);
        }
    }

    /**
     * 自动分配工单
     * 每15分钟执行一次
     */
    @Scheduled(fixedRate = 15 * 60 * 1000)
    public void autoAssignWorkOrders() {
        try {
            log.info("开始自动分配工单");
            
            // 获取待处理的工单
            List<WorkOrder> pendingOrders = workOrderService.getPendingWorkOrders();
            
            int assignedCount = 0;
            for (WorkOrder workOrder : pendingOrders) {
                // 尝试自动分配
                if (workOrder.getAssignedUserId() == null) {
                    boolean assigned = workOrderService.autoAssignWorkOrder(workOrder.getId());
                    if (assigned) {
                        assignedCount++;
                    }
                }
            }
            
            log.info("自动分配工单完成，共分配{}个工单", assignedCount);
        } catch (Exception e) {
            log.error("自动分配工单时发生错误", e);
        }
    }

    /**
     * 清理过期工单数据
     * 每周日凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 ? * SUN")
    public void cleanupExpiredWorkOrders() {
        try {
            log.info("开始清理过期工单数据");
            
            // TODO: 实现过期数据清理逻辑
            // 1. 删除或归档超过一定时间的已完成工单
            // 2. 清理相关的附件文件
            // 3. 记录清理日志
            
            log.info("过期工单数据清理完成");
        } catch (Exception e) {
            log.error("清理过期工单数据时发生错误", e);
        }
    }
}