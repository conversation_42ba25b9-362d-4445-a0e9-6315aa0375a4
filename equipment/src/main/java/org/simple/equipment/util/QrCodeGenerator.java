package org.simple.equipment.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 二维码生成工具类
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QrCodeGenerator {

    private final ObjectMapper objectMapper;

    @Value("${app.qrcode.storage.path:/tmp/qrcodes}")
    private String qrCodeStoragePath;

    @Value("${app.qrcode.base-url:http://localhost:8080/api/qrcode}")
    private String qrCodeBaseUrl;

    /**
     * 编码二维码数据
     */
    public String encodeQrCodeData(Map<String, Object> data) throws IOException {
        String json = objectMapper.writeValueAsString(data);
        return Base64.getEncoder().encodeToString(json.getBytes());
    }

    /**
     * 解码二维码数据
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> decodeQrCodeData(String encodedData) throws IOException {
        byte[] decodedBytes = Base64.getDecoder().decode(encodedData);
        String json = new String(decodedBytes);
        return objectMapper.readValue(json, Map.class);
    }

    /**
     * 生成二维码图片
     */
    public String generateQrCodeImage(String data, int size) throws WriterException, IOException {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 1);
        
        BitMatrix bitMatrix = qrCodeWriter.encode(data, BarcodeFormat.QR_CODE, size, size, hints);
        
        // 生成文件名
        String fileName = UUID.randomUUID().toString() + ".png";
        Path filePath = Paths.get(qrCodeStoragePath, fileName);
        
        // 确保目录存在
        Files.createDirectories(filePath.getParent());
        
        // 写入文件
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
            Files.write(filePath, outputStream.toByteArray());
        }
        
        // 返回访问URL
        return qrCodeBaseUrl + "/" + fileName;
    }

    /**
     * 生成简单的文本二维码
     */
    public String generateSimpleQrCode(String text, int size) throws WriterException, IOException {
        return generateQrCodeImage(text, size);
    }

    /**
     * 生成设备二维码数据
     */
    public Map<String, Object> createEquipmentQrData(String equipmentId, String qrCodeId) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", qrCodeId);
        data.put("equipmentId", equipmentId);
        data.put("type", "EQUIPMENT");
        data.put("version", "1.0");
        data.put("timestamp", System.currentTimeMillis());
        return data;
    }

    /**
     * 验证二维码数据格式
     */
    public boolean validateQrCodeData(String encodedData) {
        try {
            Map<String, Object> data = decodeQrCodeData(encodedData);
            return data.containsKey("id") && 
                   data.containsKey("equipmentId") && 
                   data.containsKey("type");
        } catch (Exception e) {
            log.warn("二维码数据格式验证失败: {}", e.getMessage());
            return false;
        }
    }
}