package org.simple.equipment.entity;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备盘点记录实体
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data

@Accessors(chain = true)
@TableName("eq_inventory_record")
@Schema(description = "设备盘点记录")
public class InventoryRecord implements Serializable {

    @Serial


    private static final long serialVersionUID = 1L;

    @Schema(description = "盘点记录ID")
    @TableId(value = "record_id", type = IdType.ASSIGN_ID)
    private String recordId;

    @Schema(description = "记录编号")
    @TableField("record_number")
    private String recordNumber;

    @Schema(description = "盘点计划ID")
    @TableField("plan_id")
    private String planId;

    @Schema(description = "盘点任务ID")
    @TableField("task_id")
    private String taskId;

    @Schema(description = "设备ID")
    @TableField("equipment_id")
    private String equipmentId;

    @Schema(description = "设备名称")
    @TableField("equipment_name")
    private String equipmentName;

    @Schema(description = "设备编码")
    @TableField("equipment_code")
    private String equipmentCode;

    @Schema(description = "设备型号")
    @TableField("equipment_model")
    private String equipmentModel;

    @Schema(description = "设备类型ID")
    @TableField("equipment_type_id")
    private String equipmentTypeId;

    @Schema(description = "设备类型名称")
    @TableField("equipment_type_name")
    private String equipmentTypeName;

    @Schema(description = "设备分类ID")
    @TableField("equipment_category_id")
    private String equipmentCategoryId;

    @Schema(description = "设备分类名称")
    @TableField("equipment_category_name")
    private String equipmentCategoryName;

    @Schema(description = "账面数量")
    @TableField("book_quantity")
    private Integer bookQuantity;

    @Schema(description = "实盘数量")
    @TableField("actual_quantity")
    private Integer actualQuantity;

    @Schema(description = "差异数量")
    @TableField("difference_quantity")
    private Integer differenceQuantity;

    @Schema(description = "账面价值")
    @TableField("book_value")
    private BigDecimal bookValue;

    @Schema(description = "实盘价值")
    @TableField("actual_value")
    private BigDecimal actualValue;

    @Schema(description = "价值差异")
    @TableField("value_difference")
    private BigDecimal valueDifference;

    @Schema(description = "盘点结果(NORMAL-正常,SURPLUS-盈余,SHORTAGE-亏损,DAMAGED-损坏,SCRAPPED-报废,LOST-丢失,LOCATION_CHANGED-位置变更,STATUS_CHANGED-状态变更,INFO_ERROR-信息错误,PENDING_INVESTIGATION-待查明)")
    @TableField("inventory_result")
    private String inventoryResult;

    @Schema(description = "账面位置")
    @TableField("book_location")
    private String bookLocation;

    @Schema(description = "实际位置")
    @TableField("actual_location")
    private String actualLocation;

    @Schema(description = "账面状态")
    @TableField("book_status")
    private String bookStatus;

    @Schema(description = "实际状态")
    @TableField("actual_status")
    private String actualStatus;

    @Schema(description = "账面使用部门ID")
    @TableField("book_dept_id")
    private String bookDeptId;

    @Schema(description = "账面使用部门名称")
    @TableField("book_dept_name")
    private String bookDeptName;

    @Schema(description = "实际使用部门ID")
    @TableField("actual_dept_id")
    private String actualDeptId;

    @Schema(description = "实际使用部门名称")
    @TableField("actual_dept_name")
    private String actualDeptName;

    @Schema(description = "账面使用人ID")
    @TableField("book_user_id")
    private String bookUserId;

    @Schema(description = "账面使用人姓名")
    @TableField("book_user_name")
    private String bookUserName;

    @Schema(description = "实际使用人ID")
    @TableField("actual_user_id")
    private String actualUserId;

    @Schema(description = "实际使用人姓名")
    @TableField("actual_user_name")
    private String actualUserName;

    @Schema(description = "盘点人员ID")
    @TableField("inventory_person_id")
    private String inventoryPersonId;

    @Schema(description = "盘点人员姓名")
    @TableField("inventory_person_name")
    private String inventoryPersonName;

    @Schema(description = "盘点时间")
    @TableField("inventory_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inventoryTime;

    @Schema(description = "盘点方式(VISUAL-目视,SCAN-扫码,RFID-射频,GPS-定位)")
    @TableField("inventory_method")
    private String inventoryMethod;

    @Schema(description = "差异原因")
    @TableField("difference_reason")
    private String differenceReason;

    @Schema(description = "差异说明")
    @TableField("difference_description")
    private String differenceDescription;

    @Schema(description = "处理建议")
    @TableField("handling_suggestion")
    private String handlingSuggestion;

    @Schema(description = "处理结果")
    @TableField("handling_result")
    private String handlingResult;

    @Schema(description = "处理人ID")
    @TableField("handler_id")
    private String handlerId;

    @Schema(description = "处理人姓名")
    @TableField("handler_name")
    private String handlerName;

    @Schema(description = "处理时间")
    @TableField("handling_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handlingTime;

    @Schema(description = "确认人ID")
    @TableField("confirmer_id")
    private String confirmerId;

    @Schema(description = "确认人姓名")
    @TableField("confirmer_name")
    private String confirmerName;

    @Schema(description = "确认时间")
    @TableField("confirmed_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmedTime;

    @Schema(description = "确认状态(PENDING-待确认,CONFIRMED-已确认,REJECTED-已拒绝)")
    @TableField("confirm_status")
    private String confirmStatus;

    @Schema(description = "确认意见")
    @TableField("confirm_comments")
    private String confirmComments;

    @Schema(description = "照片附件")
    @TableField("photo_attachments")
    private String photoAttachments;

    @Schema(description = "文档附件")
    @TableField("document_attachments")
    private String documentAttachments;

    @Schema(description = "GPS坐标")
    @TableField("gps_coordinates")
    private String gpsCoordinates;

    @Schema(description = "二维码/条码")
    @TableField("qr_code")
    private String qrCode;

    @Schema(description = "RFID标签")
    @TableField("rfid_tag")
    private String rfidTag;

    @Schema(description = "备注")
    @TableField("remarks")
    private String remarks;

    @Schema(description = "是否需要调整(0-否,1-是)")
    @TableField("need_adjustment")
    private Integer needAdjustment;

    @Schema(description = "调整状态(PENDING-待调整,PROCESSING-调整中,COMPLETED-已完成)")
    @TableField("adjustment_status")
    private String adjustmentStatus;

    @Schema(description = "扩展字段1")
    @TableField("ext1")
    private String ext1;

    @Schema(description = "扩展字段2")
    @TableField("ext2")
    private String ext2;

    @Schema(description = "扩展字段3")
    @TableField("ext3")
    private String ext3;

    @Schema(description = "扩展字段4")
    @TableField("ext4")
    private String ext4;

    @Schema(description = "扩展字段5")
    @TableField("ext5")
    private String ext5;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private String tenantId;

    @Schema(description = "逻辑删除(0-未删除,1-已删除)")
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}