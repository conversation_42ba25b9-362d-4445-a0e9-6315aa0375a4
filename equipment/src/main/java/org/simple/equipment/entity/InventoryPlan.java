package org.simple.equipment.entity;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备盘点计划实体
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data

@Accessors(chain = true)
@TableName("eq_inventory_plan")
@Schema(description = "设备盘点计划")
public class InventoryPlan implements Serializable {

    @Serial


    private static final long serialVersionUID = 1L;

    @Schema(description = "盘点计划ID")
    @TableId(value = "plan_id", type = IdType.ASSIGN_ID)
    private String planId;

    @Schema(description = "计划名称")
    @TableField("plan_name")
    private String planName;

    @Schema(description = "计划编号")
    @TableField("plan_number")
    private String planNumber;

    @Schema(description = "盘点类型(FULL_INVENTORY-全面盘点,KEY_INVENTORY-重点盘点,SAMPLE_INVENTORY-抽样盘点,REGULAR_INVENTORY-定期盘点,SPECIAL_INVENTORY-专项盘点,EMERGENCY_INVENTORY-应急盘点,DEPARTMENT_INVENTORY-部门盘点,AREA_INVENTORY-区域盘点)")
    @TableField("inventory_type")
    private String inventoryType;

    @Schema(description = "盘点范围")
    @TableField("inventory_scope")
    private String inventoryScope;

    @Schema(description = "盘点目的")
    @TableField("inventory_purpose")
    private String inventoryPurpose;

    @Schema(description = "盘点说明")
    @TableField("inventory_description")
    private String inventoryDescription;

    @Schema(description = "计划状态(PLANNED-计划中,PREPARING-准备中,IN_PROGRESS-进行中,PAUSED-已暂停,PENDING_REVIEW-待复盘,REVIEWING-复盘中,PENDING_ADJUSTMENT-待调整,ADJUSTING-调整中,PENDING_CONFIRM-待确认,COMPLETED-已完成,CANCELLED-已取消,CLOSED-已关闭)")
    @TableField("plan_status")
    private String planStatus;

    @Schema(description = "计划开始时间")
    @TableField("planned_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedStartTime;

    @Schema(description = "计划结束时间")
    @TableField("planned_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedEndTime;

    @Schema(description = "实际开始时间")
    @TableField("actual_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualStartTime;

    @Schema(description = "实际结束时间")
    @TableField("actual_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualEndTime;

    @Schema(description = "盘点负责人ID")
    @TableField("supervisor_id")
    private String supervisorId;

    @Schema(description = "盘点负责人姓名")
    @TableField("supervisor_name")
    private String supervisorName;

    @Schema(description = "组织部门ID")
    @TableField("organize_dept_id")
    private String organizeDeptId;

    @Schema(description = "组织部门名称")
    @TableField("organize_dept_name")
    private String organizeDeptName;

    @Schema(description = "参与部门")
    @TableField("participate_depts")
    private String participateDepts;

    @Schema(description = "盘点人员")
    @TableField("inventory_personnel")
    private String inventoryPersonnel;

    @Schema(description = "预计盘点设备数")
    @TableField("estimated_equipment_count")
    private Integer estimatedEquipmentCount;

    @Schema(description = "实际盘点设备数")
    @TableField("actual_equipment_count")
    private Integer actualEquipmentCount;

    @Schema(description = "盘点完成数")
    @TableField("completed_count")
    private Integer completedCount;

    @Schema(description = "盘点进度(%)")
    @TableField("progress_percentage")
    private BigDecimal progressPercentage;

    @Schema(description = "正常数量")
    @TableField("normal_count")
    private Integer normalCount;

    @Schema(description = "异常数量")
    @TableField("abnormal_count")
    private Integer abnormalCount;

    @Schema(description = "盈余数量")
    @TableField("surplus_count")
    private Integer surplusCount;

    @Schema(description = "亏损数量")
    @TableField("shortage_count")
    private Integer shortageCount;

    @Schema(description = "损坏数量")
    @TableField("damaged_count")
    private Integer damagedCount;

    @Schema(description = "丢失数量")
    @TableField("lost_count")
    private Integer lostCount;

    @Schema(description = "预计总价值")
    @TableField("estimated_total_value")
    private BigDecimal estimatedTotalValue;

    @Schema(description = "实际总价值")
    @TableField("actual_total_value")
    private BigDecimal actualTotalValue;

    @Schema(description = "价值差异")
    @TableField("value_difference")
    private BigDecimal valueDifference;

    @Schema(description = "盘点要求")
    @TableField("inventory_requirements")
    private String inventoryRequirements;

    @Schema(description = "注意事项")
    @TableField("notice_items")
    private String noticeItems;

    @Schema(description = "审批人ID")
    @TableField("approver_id")
    private String approverId;

    @Schema(description = "审批人姓名")
    @TableField("approver_name")
    private String approverName;

    @Schema(description = "审批时间")
    @TableField("approved_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvedTime;

    @Schema(description = "审批意见")
    @TableField("approval_comments")
    private String approvalComments;

    @Schema(description = "复盘人ID")
    @TableField("reviewer_id")
    private String reviewerId;

    @Schema(description = "复盘人姓名")
    @TableField("reviewer_name")
    private String reviewerName;

    @Schema(description = "复盘时间")
    @TableField("reviewed_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewedTime;

    @Schema(description = "复盘结果")
    @TableField("review_result")
    private String reviewResult;

    @Schema(description = "复盘意见")
    @TableField("review_comments")
    private String reviewComments;

    @Schema(description = "确认人ID")
    @TableField("confirmer_id")
    private String confirmerId;

    @Schema(description = "确认人姓名")
    @TableField("confirmer_name")
    private String confirmerName;

    @Schema(description = "确认时间")
    @TableField("confirmed_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmedTime;

    @Schema(description = "确认意见")
    @TableField("confirm_comments")
    private String confirmComments;

    @Schema(description = "附件")
    @TableField("attachments")
    private String attachments;

    @Schema(description = "备注")
    @TableField("remarks")
    private String remarks;

    @Schema(description = "是否紧急(0-否,1-是)")
    @TableField("is_urgent")
    private Integer isUrgent;

    @Schema(description = "优先级(1-低,2-中,3-高,4-紧急)")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "扩展字段1")
    @TableField("ext1")
    private String ext1;

    @Schema(description = "扩展字段2")
    @TableField("ext2")
    private String ext2;

    @Schema(description = "扩展字段3")
    @TableField("ext3")
    private String ext3;

    @Schema(description = "扩展字段4")
    @TableField("ext4")
    private String ext4;

    @Schema(description = "扩展字段5")
    @TableField("ext5")
    private String ext5;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private String tenantId;

    @Schema(description = "逻辑删除(0-未删除,1-已删除)")
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}