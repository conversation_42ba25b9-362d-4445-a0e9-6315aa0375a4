package org.simple.equipment.entity;
import java.io.Serial;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备档案变更记录
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName(value = "equipment_asset_change_log", autoResultMap = true)
@Schema(description = "设备档案变更记录")
public class AssetChangeLog implements Serializable {

    @Serial


    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    /**
     * 设备ID
     */
    @Schema(description = "设备ID")
    private String assetId;

    /**
     * 版本ID
     */
    @Schema(description = "版本ID")
    private String versionId;

    /**
     * 字段名称
     */
    @Schema(description = "字段名称")
    private String fieldName;

    /**
     * 字段标签
     */
    @Schema(description = "字段标签")
    private String fieldLabel;

    /**
     * 原值
     */
    @Schema(description = "原值")
    private String oldValue;

    /**
     * 新值
     */
    @Schema(description = "新值")
    private String newValue;

    /**
     * 变更类型：ADD-新增，UPDATE-修改，DELETE-删除
     */
    @Schema(description = "变更类型：ADD-新增，UPDATE-修改，DELETE-删除")
    private String changeType;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    private String dataType;

    /**
     * 是否敏感字段：0-否，1-是
     */
    @Schema(description = "是否敏感字段：0-否，1-是")
    private String isSensitive;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createDate;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}