package org.simple.equipment.entity;
import java.io.Serializable;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 异常处理记录实体
 * 
 * <AUTHOR>
 */
@Data

@TableName(value = "exception_handling_records", autoResultMap = true)
public class ExceptionHandlingRecord implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 设备ID
     */
    private String equipmentId;

    /**
     * 点检任务ID
     */
    private String inspectionTaskId;

    /**
     * 异常描述
     */
    private String exceptionDescription;

    /**
     * 异常等级
     */
    private ExceptionLevel exceptionLevel;

    /**
     * 异常类型
     */
    private String exceptionType;

    /**
     * 检测方式
     */
    private DetectionMethod detectionMethod;

    /**
     * 检测置信度
     */
    private BigDecimal detectionConfidence;

    /**
     * 处理状态
     */
    private HandlingStatus handlingStatus;

    /**
     * 分配给
     */
    private String assignedTo;

    /**
     * 分配时间
     */
    private LocalDateTime assignedAt;

    /**
     * 解决人
     */
    private String resolvedBy;

    /**
     * 解决时间
     */
    private LocalDateTime resolvedAt;

    /**
     * 解决描述
     */
    private String resolutionDescription;

    /**
     * 工单ID
     */
    private String workOrderId;

    /**
     * 是否已发送通知
     */
    private Boolean notificationSent;

    /**
     * 升级级别
     */
    private Integer escalationLevel;

    /**
     * 处理截止时间
     */
    private LocalDateTime deadline;

    /**
     * 照片附件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> photos;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 异常等级枚举
     */
    public enum ExceptionLevel {
        /**
         * 一级异常 - 严重
         */
        LEVEL_1,
        /**
         * 二级异常 - 重要
         */
        LEVEL_2,
        /**
         * 三级异常 - 一般
         */
        LEVEL_3,
        /**
         * 四级异常 - 轻微
         */
        LEVEL_4
    }

    /**
     * 检测方式枚举
     */
    public enum DetectionMethod {
        /**
         * 手动检测
         */
        MANUAL,
        /**
         * 自动检测
         */
        AUTO,
        /**
         * AI检测
         */
        AI
    }

    /**
     * 处理状态枚举
     */
    public enum HandlingStatus {
        /**
         * 待处理
         */
        PENDING,
        /**
         * 处理中
         */
        IN_PROGRESS,
        /**
         * 已解决
         */
        RESOLVED,
        /**
         * 已升级
         */
        ESCALATED
    }
}