package org.simple.equipment.entity;
import java.io.Serializable;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 设备巡检计划实体类
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eq_patrol_plan")
@Schema(description = "设备巡检计划")
public class PatrolPlan implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "巡检计划ID")
    @TableId(value = "plan_id", type = IdType.ASSIGN_ID)
    private String planId;

    @Schema(description = "计划编号")
    @TableField("plan_code")
    private String planCode;

    @Schema(description = "计划名称")
    @TableField("plan_name")
    private String planName;

    @Schema(description = "巡检类型(ROUTINE-例行巡检,SPECIAL-专项巡检,EMERGENCY-应急巡检)")
    @TableField("patrol_type")
    private String patrolType;

    @Schema(description = "巡检周期(DAILY-每日,WEEKLY-每周,MONTHLY-每月,QUARTERLY-每季度,YEARLY-每年)")
    @TableField("patrol_cycle")
    private String patrolCycle;

    @Schema(description = "巡检路线ID")
    @TableField("route_id")
    private String routeId;

    @Schema(description = "巡检路线名称")
    @TableField("route_name")
    private String routeName;

    @Schema(description = "责任人ID")
    @TableField("responsible_user_id")
    private String responsibleUserId;

    @Schema(description = "责任人姓名")
    @TableField("responsible_user_name")
    private String responsibleUserName;

    @Schema(description = "责任部门ID")
    @TableField("responsible_dept_id")
    private String responsibleDeptId;

    @Schema(description = "责任部门名称")
    @TableField("responsible_dept_name")
    private String responsibleDeptName;

    @Schema(description = "计划开始时间")
    @TableField("start_time")
    private LocalTime startTime;

    @Schema(description = "计划结束时间")
    @TableField("end_time")
    private LocalTime endTime;

    @Schema(description = "计划持续时间(分钟)")
    @TableField("duration_minutes")
    private Integer durationMinutes;

    @Schema(description = "计划生效日期")
    @TableField("effective_date")
    private LocalDateTime effectiveDate;

    @Schema(description = "计划失效日期")
    @TableField("expiry_date")
    private LocalDateTime expiryDate;

    @Schema(description = "执行规则(JSON配置)")
    @TableField("execution_rule")
    private String executionRule;

    @Schema(description = "巡检要求描述")
    @TableField("patrol_requirements")
    private String patrolRequirements;

    @Schema(description = "注意事项")
    @TableField("notes")
    private String notes;

    @Schema(description = "自动生成任务(0-否,1-是)")
    @TableField("auto_generate")
    private Integer autoGenerate;

    @Schema(description = "提前提醒时间(分钟)")
    @TableField("remind_minutes")
    private Integer remindMinutes;

    @Schema(description = "计划状态(0-草稿,1-生效,2-暂停,3-失效)")
    @TableField("plan_status")
    private Integer planStatus;

    @Schema(description = "优先级(1-低,2-中,3-高,4-紧急)")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private String tenantId;

    @Schema(description = "删除标志(0-未删除,1-已删除)")
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}
