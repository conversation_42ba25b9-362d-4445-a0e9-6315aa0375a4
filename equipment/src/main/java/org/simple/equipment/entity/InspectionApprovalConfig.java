package org.simple.equipment.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备点检审核配置
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
@Accessors(chain = true)
@TableName("eq_inspection_approval_config")
@Schema(description = "设备点检审核配置")
public class InspectionApprovalConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "配置ID")
    @TableId(value = "config_id", type = IdType.ASSIGN_ID)
    private String configId;

    @Schema(description = "配置名称")
    @TableField("config_name")
    private String configName;

    @Schema(description = "配置编码")
    @TableField("config_code")
    private String configCode;

    @Schema(description = "配置描述")
    @TableField("config_description")
    private String configDescription;

    @Schema(description = "适用范围类型(ALL-全部,DEPT-部门,CATEGORY-设备分类,ASSET-指定设备)")
    @TableField("scope_type")
    private String scopeType;

    @Schema(description = "适用范围值(部门ID/分类ID/设备ID)")
    @TableField("scope_value")
    private String scopeValue;

    @Schema(description = "适用范围名称")
    @TableField("scope_name")
    private String scopeName;

    @Schema(description = "是否启用审核")
    @TableField("approval_enabled")
    private Boolean approvalEnabled;

    @Schema(description = "审核级别(1-一级审核,2-二级审核,3-三级审核)")
    @TableField("approval_level")
    private Integer approvalLevel;

    @Schema(description = "自动审核条件(JSON格式)")
    @TableField("auto_approval_condition")
    private String autoApprovalCondition;

    @Schema(description = "审核超时时间(小时)")
    @TableField("approval_timeout")
    private Integer approvalTimeout;

    @Schema(description = "超时自动处理(AUTO_PASS-自动通过,AUTO_REJECT-自动驳回,ESCALATE-升级处理)")
    @TableField("timeout_action")
    private String timeoutAction;

    @Schema(description = "紧急审核条件(JSON格式)")
    @TableField("urgent_condition")
    private String urgentCondition;

    @Schema(description = "紧急审核流程")
    @TableField("urgent_flow")
    private String urgentFlow;

    @Schema(description = "通知配置(JSON格式)")
    @TableField("notification_config")
    private String notificationConfig;

    @Schema(description = "审核流程定义(JSON格式)")
    @TableField("flow_definition")
    private String flowDefinition;

    @Schema(description = "配置状态(ACTIVE-启用,INACTIVE-禁用)")
    @TableField("config_status")
    private String configStatus;

    @Schema(description = "优先级(数字越大优先级越高)")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "生效时间")
    @TableField("effective_time")
    private String effectiveTime;

    @Schema(description = "失效时间")
    @TableField("expiry_time")
    private String expiryTime;

    @Schema(description = "备注")
    @TableField("remark")
    private String remark;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}