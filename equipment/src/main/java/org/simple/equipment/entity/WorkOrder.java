package org.simple.equipment.entity;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工单实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName(value = "equipment_work_order", autoResultMap = true)
@Schema(description = "工单")
public class WorkOrder implements Serializable {

    @Serial


    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "工单编码")
    private String orderCode;

    @Schema(description = "工单标题")
    private String orderTitle;

    @Schema(description = "工单类型 REPAIR-维修 MAINTENANCE-保养 INSPECTION-点检 PATROL-巡检 OTHER-其他")
    private String orderType;

    @Schema(description = "关联设备ID")
    private String equipmentId;

    @Schema(description = "设备编码")
    private String equipmentCode;

    @Schema(description = "设备名称")
    private String equipmentName;

    @Schema(description = "申请人ID")
    private String applicantId;

    @Schema(description = "申请人姓名")
    private String applicantName;

    @Schema(description = "申请科室ID")
    private String departmentId;

    @Schema(description = "处理人ID")
    private String assignedUserId;

    @Schema(description = "处理人姓名")
    private String assignedUserName;

    @Schema(description = "优先级 LOW-低 NORMAL-普通 HIGH-高 URGENT-紧急")
    private String priority;

    @Schema(description = "工单状态 PENDING-待处理 PROCESSING-处理中 COMPLETED-已完成 CANCELLED-已取消")
    private String orderStatus;

    @Schema(description = "问题描述")
    private String description;

    @Schema(description = "解决方案")
    private String solution;

    @Schema(description = "相关图片(JSON格式存储图片ID数组)")
    private String images;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "分配时间")
    private LocalDateTime assignTime;

    @Schema(description = "开始处理时间")
    private LocalDateTime startTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "创建时间")
    private LocalDateTime createDate;

    @Schema(description = "更新时间")
    private LocalDateTime updateDate;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}