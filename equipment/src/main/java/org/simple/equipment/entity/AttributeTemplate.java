package org.simple.equipment.entity;
import java.io.Serial;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备属性模板实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName(value = "equipment_attribute_template", autoResultMap = true)
@Schema(description = "设备属性模板")
public class AttributeTemplate implements Serializable {

    @Serial


    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "模板编码")
    private String templateCode;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "模板描述")
    private String templateDesc;

    @Schema(description = "适用设备分类ID")
    private String categoryId;

    @Schema(description = "适用设备类型ID")
    private String typeId;

    @Schema(description = "父模板ID")
    private String parentId;

    @Schema(description = "模板版本")
    private String templateVersion;

    @Schema(description = "是否系统模板：0-否，1-是")
    private String isSystem;

    @Schema(description = "是否启用：0-否，1-是")
    private String isEnabled;

    @Schema(description = "排序顺序")
    private Integer sortOrder;

    @Schema(description = "模板配置（JSON格式）")
    private String templateConfig;

    @Schema(description = "使用次数")
    private Integer usageCount;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "创建时间")
    private LocalDateTime createDate;

    @Schema(description = "更新时间")
    private LocalDateTime updateDate;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}