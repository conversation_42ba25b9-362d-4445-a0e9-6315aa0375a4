package org.simple.equipment.entity;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 设备保养计划实体
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data

@Accessors(chain = true)
@TableName("eq_maintenance_plan")
@Schema(description = "设备保养计划")
public class MaintenancePlan implements Serializable {

    @Serial


    private static final long serialVersionUID = 1L;

    @Schema(description = "保养计划ID")
    @TableId(value = "plan_id", type = IdType.ASSIGN_ID)
    private String planId;

    @Schema(description = "计划名称")
    @TableField("plan_name")
    private String planName;

    @Schema(description = "计划编码")
    @TableField("plan_code")
    private String planCode;

    @Schema(description = "设备ID")
    @TableField("equipment_id")
    private String equipmentId;

    @Schema(description = "设备名称")
    @TableField("equipment_name")
    private String equipmentName;

    @Schema(description = "设备编码")
    @TableField("equipment_code")
    private String equipmentCode;

    @Schema(description = "保养类型(DAILY-日常保养,LEVEL_ONE-一级保养,LEVEL_TWO-二级保养,LEVEL_THREE-三级保养,SPECIAL-专项保养,SEASONAL-季节性保养,PREVENTIVE-预防性保养)")
    @TableField("maintenance_type")
    private String maintenanceType;

    @Schema(description = "保养周期(DAILY-每日,WEEKLY-每周,MONTHLY-每月,QUARTERLY-每季度,HALF_YEARLY-每半年,YEARLY-每年,HOURLY-按小时,MILEAGE-按里程,USAGE_COUNT-按次数)")
    @TableField("maintenance_cycle")
    private String maintenanceCycle;

    @Schema(description = "周期值(数值)")
    @TableField("cycle_value")
    private Integer cycleValue;

    @Schema(description = "周期单位(天、小时、公里等)")
    @TableField("cycle_unit")
    private String cycleUnit;

    @Schema(description = "保养标准")
    @TableField("maintenance_standard")
    private String maintenanceStandard;

    @Schema(description = "保养内容")
    @TableField("maintenance_content")
    private String maintenanceContent;

    @Schema(description = "保养要求")
    @TableField("maintenance_requirements")
    private String maintenanceRequirements;

    @Schema(description = "所需工时(小时)")
    @TableField("required_hours")
    private BigDecimal requiredHours;

    @Schema(description = "预计费用")
    @TableField("estimated_cost")
    private BigDecimal estimatedCost;

    @Schema(description = "负责人ID")
    @TableField("responsible_person_id")
    private String responsiblePersonId;

    @Schema(description = "负责人姓名")
    @TableField("responsible_person_name")
    private String responsiblePersonName;

    @Schema(description = "执行部门ID")
    @TableField("execute_dept_id")
    private String executeDeptId;

    @Schema(description = "执行部门名称")
    @TableField("execute_dept_name")
    private String executeDeptName;

    @Schema(description = "优先级(1-低,2-中,3-高,4-紧急)")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "开始时间")
    @TableField("start_time")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime startTime;

    @Schema(description = "结束时间")
    @TableField("end_time")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime endTime;

    @Schema(description = "生效日期")
    @TableField("effective_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveDate;

    @Schema(description = "失效日期")
    @TableField("expiry_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiryDate;

    @Schema(description = "计划状态(0-草稿,1-生效,2-暂停,3-失效)")
    @TableField("plan_status")
    private Integer planStatus;

    @Schema(description = "自动生成任务(0-否,1-是)")
    @TableField("auto_generate")
    private Integer autoGenerate;

    @Schema(description = "提前提醒时间(小时)")
    @TableField("remind_hours")
    private Integer remindHours;

    @Schema(description = "跳过节假日(0-否,1-是)")
    @TableField("skip_holidays")
    private Integer skipHolidays;

    @Schema(description = "执行规则(JSON配置)")
    @TableField("execution_rule")
    private String executionRule;

    @Schema(description = "上次执行时间")
    @TableField("last_execution_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastExecutionTime;

    @Schema(description = "下次执行时间")
    @TableField("next_execution_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextExecutionTime;

    @Schema(description = "已执行次数")
    @TableField("execution_count")
    private Integer executionCount;

    @Schema(description = "备注")
    @TableField("notes")
    private String notes;

    @Schema(description = "是否启用(0-禁用,1-启用)")
    @TableField("enabled")
    private Integer enabled;

    @Schema(description = "排序")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "扩展字段1")
    @TableField("ext1")
    private String ext1;

    @Schema(description = "扩展字段2")
    @TableField("ext2")
    private String ext2;

    @Schema(description = "扩展字段3")
    @TableField("ext3")
    private String ext3;

    @Schema(description = "扩展字段4")
    @TableField("ext4")
    private String ext4;

    @Schema(description = "扩展字段5")
    @TableField("ext5")
    private String ext5;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private String tenantId;

    @Schema(description = "逻辑删除(0-未删除,1-已删除)")
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}