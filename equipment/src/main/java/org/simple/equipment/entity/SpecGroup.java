package org.simple.equipment.entity;
import java.io.Serial;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备规格参数组
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@TableName(value = "equipment_spec_group", autoResultMap = true)
@Schema(description = "设备规格参数组")
public class SpecGroup implements Serializable {

    @Serial


    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    /**
     * 分组编码
     */
    @Schema(description = "分组编码")
    private String groupCode;

    /**
     * 分组名称
     */
    @Schema(description = "分组名称")
    private String groupName;

    /**
     * 分组描述
     */
    @Schema(description = "分组描述")
    private String groupDesc;

    /**
     * 上级分组ID
     */
    @Schema(description = "上级分组ID")
    private String parentId;

    /**
     * 适用设备分类ID
     */
    @Schema(description = "适用设备分类ID")
    private String categoryId;

    /**
     * 适用设备类型ID
     */
    @Schema(description = "适用设备类型ID")
    private String typeId;

    /**
     * 是否系统分组：0-否，1-是
     */
    @Schema(description = "是否系统分组：0-否，1-是")
    private String isSystem;

    /**
     * 是否启用：0-否，1-是
     */
    @Schema(description = "是否启用：0-否，1-是")
    private String isEnabled;

    /**
     * 排序顺序
     */
    @Schema(description = "排序顺序")
    private Integer sortOrder;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createDate;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateDate;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String creator;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updater;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}