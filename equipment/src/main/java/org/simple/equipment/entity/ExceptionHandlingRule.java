package org.simple.equipment.entity;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 异常处理规则实体
 * 
 * <AUTHOR>
 */
@Data

@TableName(value = "exception_handling_rules", autoResultMap = true)
public class ExceptionHandlingRule implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 设备类型
     */
    private String equipmentType;

    /**
     * 异常关键词
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> exceptionKeywords;

    /**
     * 异常等级
     */
    private ExceptionLevel exceptionLevel;

    /**
     * 自动处理动作
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> autoActions;

    /**
     * 通知规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> notificationRules;

    /**
     * 升级规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> escalationRules;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 异常等级枚举
     */
    public enum ExceptionLevel {
        /**
         * 一级异常 - 严重
         */
        LEVEL_1,
        /**
         * 二级异常 - 重要
         */
        LEVEL_2,
        /**
         * 三级异常 - 一般
         */
        LEVEL_3,
        /**
         * 四级异常 - 轻微
         */
        LEVEL_4
    }
}