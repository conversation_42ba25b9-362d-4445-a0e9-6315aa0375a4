package org.simple.equipment.entity;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


import java.math.BigDecimal;

/**
 * 设备巡检要点实体类
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eq_patrol_checkpoint")
@Schema(description = "设备巡检要点")
public class PatrolCheckpoint implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "要点ID")
    @TableId(value = "checkpoint_id", type = IdType.ASSIGN_ID)
    private String checkpointId;

    @Schema(description = "要点编号")
    @TableField("checkpoint_code")
    private String checkpointCode;

    @Schema(description = "要点名称")
    @TableField("checkpoint_name")
    private String checkpointName;

    @Schema(description = "要点描述")
    @TableField("checkpoint_desc")
    private String checkpointDesc;

    @Schema(description = "所属路线ID")
    @TableField("route_id")
    private String routeId;

    @Schema(description = "路线名称")
    @TableField("route_name")
    private String routeName;

    @Schema(description = "设备ID")
    @TableField("asset_id")
    private String assetId;

    @Schema(description = "设备名称")
    @TableField("asset_name")
    private String assetName;

    @Schema(description = "设备位置ID")
    @TableField("location_id")
    private String locationId;

    @Schema(description = "设备位置名称")
    @TableField("location_name")
    private String locationName;

    @Schema(description = "要点类型(EQUIPMENT-设备点,AREA-区域点,SAFETY-安全点,ENVIRONMENT-环境点)")
    @TableField("checkpoint_type")
    private String checkpointType;

    @Schema(description = "要点顺序")
    @TableField("checkpoint_order")
    private Integer checkpointOrder;

    @Schema(description = "经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @Schema(description = "二维码内容")
    @TableField("qr_code")
    private String qrCode;

    @Schema(description = "二维码图片")
    @TableField("qr_code_image")
    private String qrCodeImage;

    @Schema(description = "巡检项目列表(JSON)")
    @TableField("patrol_items")
    private String patrolItems;

    @Schema(description = "巡检标准")
    @TableField("patrol_standard")
    private String patrolStandard;

    @Schema(description = "巡检方法")
    @TableField("patrol_method")
    private String patrolMethod;

    @Schema(description = "预计用时(分钟)")
    @TableField("estimated_duration")
    private Integer estimatedDuration;

    @Schema(description = "必须拍照(0-不需要,1-需要)")
    @TableField("require_photo")
    private Integer requirePhoto;

    @Schema(description = "必须签到(0-不需要,1-需要)")
    @TableField("require_checkin")
    private Integer requireCheckin;

    @Schema(description = "安全注意事项")
    @TableField("safety_notes")
    private String safetyNotes;

    @Schema(description = "巡检要求")
    @TableField("requirements")
    private String requirements;

    @Schema(description = "异常处理指导")
    @TableField("exception_guide")
    private String exceptionGuide;

    @Schema(description = "联系人信息")
    @TableField("contact_info")
    private String contactInfo;

    @Schema(description = "要点状态(0-禁用,1-启用,2-维护中)")
    @TableField("checkpoint_status")
    private Integer checkpointStatus;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private String tenantId;

    @Schema(description = "删除标志(0-未删除,1-已删除)")
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}
