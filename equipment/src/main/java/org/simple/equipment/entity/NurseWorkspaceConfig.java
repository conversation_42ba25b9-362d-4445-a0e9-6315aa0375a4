package org.simple.equipment.entity;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 护士工作台配置实体
 * 
 * <AUTHOR>
 */
@Data

@TableName(value = "nurse_workspace_config", autoResultMap = true)
public class NurseWorkspaceConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 护士用户ID
     */
    private String userId;

    /**
     * 工作台名称
     */
    private String workspaceName;

    /**
     * 主题配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> themeConfig;

    /**
     * 布局配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> layoutConfig;

    /**
     * 组件配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> widgetConfig;

    /**
     * 快速操作配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> quickActions;

    /**
     * 通知配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> notificationConfig;

    /**
     * 显示偏好
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> displayPreferences;

    /**
     * 是否启用语音功能
     */
    private Boolean voiceEnabled;

    /**
     * 是否启用离线模式
     */
    private Boolean offlineMode;

    /**
     * 是否自动同步
     */
    private Boolean autoSync;

    /**
     * 语言设置
     */
    private String language;

    /**
     * 字体大小
     */
    private FontSize fontSize;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 字体大小枚举
     */
    public enum FontSize {
        /**
         * 小号字体
         */
        SMALL,
        /**
         * 中号字体
         */
        MEDIUM,
        /**
         * 大号字体
         */
        LARGE
    }
}