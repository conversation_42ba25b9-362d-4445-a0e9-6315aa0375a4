package org.simple.equipment.entity;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备保养模板实体
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data

@Accessors(chain = true)
@TableName("eq_maintenance_template")
@Schema(description = "设备保养模板")
public class MaintenanceTemplate implements Serializable {

    @Serial


    private static final long serialVersionUID = 1L;

    @Schema(description = "保养模板ID")
    @TableId(value = "template_id", type = IdType.ASSIGN_ID)
    private String templateId;

    @Schema(description = "模板名称")
    @TableField("template_name")
    private String templateName;

    @Schema(description = "模板编码")
    @TableField("template_code")
    private String templateCode;

    @Schema(description = "设备类型ID")
    @TableField("equipment_type_id")
    private String equipmentTypeId;

    @Schema(description = "设备类型名称")
    @TableField("equipment_type_name")
    private String equipmentTypeName;

    @Schema(description = "设备型号")
    @TableField("equipment_model")
    private String equipmentModel;

    @Schema(description = "保养类型(DAILY-日常保养,LEVEL_ONE-一级保养,LEVEL_TWO-二级保养,LEVEL_THREE-三级保养,SPECIAL-专项保养,SEASONAL-季节性保养,PREVENTIVE-预防性保养)")
    @TableField("maintenance_type")
    private String maintenanceType;

    @Schema(description = "保养周期(DAILY-每日,WEEKLY-每周,MONTHLY-每月,QUARTERLY-每季度,HALF_YEARLY-每半年,YEARLY-每年,HOURLY-按小时,MILEAGE-按里程,USAGE_COUNT-按次数)")
    @TableField("maintenance_cycle")
    private String maintenanceCycle;

    @Schema(description = "周期值(数值)")
    @TableField("cycle_value")
    private Integer cycleValue;

    @Schema(description = "周期单位(天、小时、公里等)")
    @TableField("cycle_unit")
    private String cycleUnit;

    @Schema(description = "保养标准")
    @TableField("maintenance_standard")
    private String maintenanceStandard;

    @Schema(description = "保养内容")
    @TableField("maintenance_content")
    private String maintenanceContent;

    @Schema(description = "保养项目(JSON格式)")
    @TableField("maintenance_items")
    private String maintenanceItems;

    @Schema(description = "保养要求")
    @TableField("maintenance_requirements")
    private String maintenanceRequirements;

    @Schema(description = "保养步骤")
    @TableField("maintenance_steps")
    private String maintenanceSteps;

    @Schema(description = "所需工时(小时)")
    @TableField("required_hours")
    private BigDecimal requiredHours;

    @Schema(description = "预计费用")
    @TableField("estimated_cost")
    private BigDecimal estimatedCost;

    @Schema(description = "所需技能")
    @TableField("required_skills")
    private String requiredSkills;

    @Schema(description = "所需工具")
    @TableField("required_tools")
    private String requiredTools;

    @Schema(description = "所需材料")
    @TableField("required_materials")
    private String requiredMaterials;

    @Schema(description = "安全注意事项")
    @TableField("safety_precautions")
    private String safetyPrecautions;

    @Schema(description = "质量标准")
    @TableField("quality_standards")
    private String qualityStandards;

    @Schema(description = "验收标准")
    @TableField("acceptance_criteria")
    private String acceptanceCriteria;

    @Schema(description = "创建部门ID")
    @TableField("create_dept_id")
    private String createDeptId;

    @Schema(description = "创建部门名称")
    @TableField("create_dept_name")
    private String createDeptName;

    @Schema(description = "审核人ID")
    @TableField("reviewed_by")
    private String reviewedBy;

    @Schema(description = "审核人姓名")
    @TableField("reviewed_by_name")
    private String reviewedByName;

    @Schema(description = "审核时间")
    @TableField("reviewed_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewedTime;

    @Schema(description = "审核状态(0-待审核,1-已审核,2-已拒绝)")
    @TableField("review_status")
    private Integer reviewStatus;

    @Schema(description = "审核意见")
    @TableField("review_comments")
    private String reviewComments;

    @Schema(description = "模板状态(0-草稿,1-生效,2-停用)")
    @TableField("template_status")
    private Integer templateStatus;

    @Schema(description = "版本号")
    @TableField("version")
    private String version;

    @Schema(description = "生效日期")
    @TableField("effective_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveDate;

    @Schema(description = "失效日期")
    @TableField("expiry_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiryDate;

    @Schema(description = "使用次数")
    @TableField("usage_count")
    private Integer usageCount;

    @Schema(description = "附件")
    @TableField("attachments")
    private String attachments;

    @Schema(description = "备注")
    @TableField("notes")
    private String notes;

    @Schema(description = "是否默认(0-否,1-是)")
    @TableField("is_default")
    private Integer isDefault;

    @Schema(description = "是否公开(0-否,1-是)")
    @TableField("is_public")
    private Integer isPublic;

    @Schema(description = "排序")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "扩展字段1")
    @TableField("ext1")
    private String ext1;

    @Schema(description = "扩展字段2")
    @TableField("ext2")
    private String ext2;

    @Schema(description = "扩展字段3")
    @TableField("ext3")
    private String ext3;

    @Schema(description = "扩展字段4")
    @TableField("ext4")
    private String ext4;

    @Schema(description = "扩展字段5")
    @TableField("ext5")
    private String ext5;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private String tenantId;

    @Schema(description = "逻辑删除(0-未删除,1-已删除)")
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}