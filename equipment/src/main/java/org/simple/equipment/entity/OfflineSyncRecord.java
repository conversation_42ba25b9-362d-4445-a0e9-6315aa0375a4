package org.simple.equipment.entity;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 离线数据同步记录实体
 * 
 * <AUTHOR>
 */
@Data

@TableName(value = "offline_sync_records")
public class OfflineSyncRecord implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 设备标识
     */
    private String deviceId;

    /**
     * 同步类型
     */
    private SyncType syncType;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 实体ID
     */
    private String entityId;

    /**
     * 实体类型
     */
    private String entityType;

    /**
     * 操作类型
     */
    private Operation operation;

    /**
     * 数据内容(JSON)
     */
    @TableField("data_content")
    private String dataContent;

    /**
     * 本地时间戳
     */
    private Long localTimestamp;

    /**
     * 服务器时间戳
     */
    private Long serverTimestamp;

    /**
     * 同步状态
     */
    private SyncStatus syncStatus;

    /**
     * 冲突解决方式
     */
    private ConflictResolution conflictResolution;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 数据校验和
     */
    private String checksum;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人id")
    private String creator;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 同步类型枚举
     */
    public enum SyncType {
        /**
         * 下载
         */
        DOWNLOAD,
        /**
         * 上传
         */
        UPLOAD
    }

    /**
     * 操作类型枚举
     */
    public enum Operation {
        /**
         * 创建
         */
        CREATE,
        /**
         * 更新
         */
        UPDATE,
        /**
         * 删除
         */
        DELETE
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        /**
         * 待同步
         */
        PENDING,
        /**
         * 已同步
         */
        SYNCED,
        /**
         * 冲突
         */
        CONFLICT,
        /**
         * 失败
         */
        FAILED
    }

    /**
     * 冲突解决方式枚举
     */
    public enum ConflictResolution {
        /**
         * 服务器优先
         */
        SERVER_WINS,
        /**
         * 客户端优先
         */
        CLIENT_WINS,
        /**
         * 合并
         */
        MERGE,
        /**
         * 手动处理
         */
        MANUAL
    }
}