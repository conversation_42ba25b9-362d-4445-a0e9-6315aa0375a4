package org.simple.equipment.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.TagCombination;
import org.simple.equipment.mapper.TagCombinationMapper;
import org.simple.equipment.service.TagCombinationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 标签组合服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class TagCombinationServiceImpl extends ServiceImpl<TagCombinationMapper, TagCombination> implements TagCombinationService {

    @Override
    public List<TagCombination> getActiveCombinationList() {
        return baseMapper.selectActiveList(AuthUtil.getTenantId());
    }

    @Override
    public List<TagCombination> getCombinationsByCategory(String categoryId, String typeId) {
        return baseMapper.selectByCategory(categoryId, typeId, AuthUtil.getTenantId());
    }

    @Override
    public List<TagCombination> getCombinationList(TagCombination tagCombination) {
        return baseMapper.selectTagCombinationList(tagCombination);
    }

    @Override
    public Map<String, Object> getCombinationDetail(String combinationId) {
        return baseMapper.selectCombinationDetail(combinationId, AuthUtil.getTenantId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> saveCombination(TagCombination tagCombination) {
        try {
            // 检查组合编码是否重复
            if (checkCombinationCodeExists(tagCombination.getCombinationCode(), null)) {
                return FrResult.failed("组合编码已存在");
            }

            // 设置基础信息
            tagCombination.setId(RandomUtil.getUserId());
            tagCombination.setTenantId(AuthUtil.getTenantId());
            tagCombination.setCreator(AuthUtil.getUserId());
            tagCombination.setCreateDate(LocalDateTime.now());
            tagCombination.setUpdateDate(LocalDateTime.now());
            
            // 设置默认值
            if (StrUtil.isBlank(tagCombination.getIsEnabled())) {
                tagCombination.setIsEnabled("1");
            }
            if (tagCombination.getUsageCount() == null) {
                tagCombination.setUsageCount(0);
            }

            boolean success = save(tagCombination);
            return success ? FrResult.successNodata("新增成功") : FrResult.failed("新增失败");
        } catch (Exception e) {
            log.error("新增标签组合失败", e);
            return FrResult.failed("新增失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> updateCombination(TagCombination tagCombination) {
        try {
            // 检查组合是否存在
            TagCombination existCombination = getById(tagCombination.getId());
            if (existCombination == null) {
                return FrResult.failed("组合不存在");
            }

            // 检查组合编码是否重复
            if (checkCombinationCodeExists(tagCombination.getCombinationCode(), tagCombination.getId())) {
                return FrResult.failed("组合编码已存在");
            }

            // 设置更新信息
            tagCombination.setUpdater(AuthUtil.getUserId());
            tagCombination.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(tagCombination);
            return success ? FrResult.successNodata("修改成功") : FrResult.failed("修改失败");
        } catch (Exception e) {
            log.error("修改标签组合失败", e);
            return FrResult.failed("修改失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> deleteCombination(String id) {
        try {
            TagCombination combination = getById(id);
            if (combination == null) {
                return FrResult.failed("组合不存在");
            }

            boolean success = removeById(id);
            return success ? FrResult.successNodata("删除成功") : FrResult.failed("删除失败");
        } catch (Exception e) {
            log.error("删除标签组合失败", e);
            return FrResult.failed("删除失败：" + e.getMessage());
        }
    }

    @Override
    public boolean checkCombinationCodeExists(String combinationCode, String excludeId) {
        TagCombination combination = baseMapper.selectByCombinationCode(combinationCode, AuthUtil.getTenantId(), excludeId);
        return combination != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> updateCombinationStatus(String id, String isEnabled) {
        try {
            TagCombination combination = getById(id);
            if (combination == null) {
                return FrResult.failed("组合不存在");
            }

            combination.setIsEnabled(isEnabled);
            combination.setUpdater(AuthUtil.getUserId());
            combination.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(combination);
            return success ? FrResult.successNodata("状态更新成功") : FrResult.failed("状态更新失败");
        } catch (Exception e) {
            log.error("更新组合状态失败", e);
            return FrResult.failed("状态更新失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> updateUsageCount(String combinationId) {
        try {
            int count = baseMapper.updateUsageCount(combinationId, AuthUtil.getTenantId());
            return count > 0 ? FrResult.successNodata("更新成功") : FrResult.failed("更新失败");
        } catch (Exception e) {
            log.error("更新组合使用次数失败", e);
            return FrResult.failed("更新失败：" + e.getMessage());
        }
    }

    @Override
    public List<TagCombination> getPopularCombinations(int limit) {
        return baseMapper.selectPopularCombinations(AuthUtil.getTenantId(), limit);
    }

    @Override
    public List<Map<String, Object>> getUsageStatistics() {
        return baseMapper.selectUsageStatistics(AuthUtil.getTenantId());
    }
}