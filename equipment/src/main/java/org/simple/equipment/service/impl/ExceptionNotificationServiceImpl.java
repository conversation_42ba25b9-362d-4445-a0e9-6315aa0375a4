package org.simple.equipment.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simple.equipment.entity.ExceptionHandlingRecord;
import org.simple.equipment.service.ExceptionNotificationService;
import org.simple.equipment.service.IntelligentExceptionHandlerService;
import org.springframework.stereotype.Service;

/**
 * 异常通知服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExceptionNotificationServiceImpl implements ExceptionNotificationService {

    private final IntelligentExceptionHandlerService exceptionHandlerService;

    @Override
    public Boolean sendExceptionNotifications(String exceptionRecordId) {
        log.info("发送异常通知: {}", exceptionRecordId);
        
        try {
            ExceptionHandlingRecord record = exceptionHandlerService.getHandlingProgress(exceptionRecordId);
            if (record == null) {
                log.warn("异常记录不存在: {}", exceptionRecordId);
                return false;
            }
            
            // 根据异常等级发送不同级别的通知
            switch (record.getExceptionLevel()) {
                case LEVEL_1:
                    return sendUrgentNotification(record);
                case LEVEL_2:
                    return sendHighPriorityNotification(record);
                case LEVEL_3:
                    return sendMediumPriorityNotification(record);
                case LEVEL_4:
                    return sendLowPriorityNotification(record);
                default:
                    return false;
            }
            
        } catch (Exception e) {
            log.error("发送异常通知失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean sendEscalationNotification(String exceptionRecordId) {
        log.info("发送升级通知: {}", exceptionRecordId);
        
        try {
            ExceptionHandlingRecord record = exceptionHandlerService.getHandlingProgress(exceptionRecordId);
            if (record == null) {
                return false;
            }
            
            // 发送升级通知给更高级别的人员
            return sendEscalationAlert(record);
            
        } catch (Exception e) {
            log.error("发送升级通知失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean sendWorkOrderNotification(String workOrderId) {
        log.info("发送工单通知: {}", workOrderId);
        
        try {
            // 发送工单创建通知给相关技师
            return sendWorkOrderAlert(workOrderId);
            
        } catch (Exception e) {
            log.error("发送工单通知失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送紧急通知
     */
    private Boolean sendUrgentNotification(ExceptionHandlingRecord record) {
        log.info("发送紧急通知 - 设备: {}, 异常: {}", record.getEquipmentId(), record.getExceptionDescription());
        
        // 实际实现中这里会调用短信、电话、APP推送等服务
        // 目前模拟实现
        
        // 1. 发送短信给设备科
        sendSmsNotification("设备科", buildUrgentMessage(record));
        
        // 2. 发送APP推送给科室主任
        sendAppPushNotification("科室主任", buildUrgentMessage(record));
        
        // 3. 发送电话通知给值班经理
        sendPhoneNotification("值班经理", record);
        
        return true;
    }

    /**
     * 发送高优先级通知
     */
    private Boolean sendHighPriorityNotification(ExceptionHandlingRecord record) {
        log.info("发送高优先级通知 - 设备: {}, 异常: {}", record.getEquipmentId(), record.getExceptionDescription());
        
        // 1. 发送短信给设备科
        sendSmsNotification("设备科", buildHighPriorityMessage(record));
        
        // 2. 发送APP推送给科室负责人
        sendAppPushNotification("科室负责人", buildHighPriorityMessage(record));
        
        return true;
    }

    /**
     * 发送中优先级通知
     */
    private Boolean sendMediumPriorityNotification(ExceptionHandlingRecord record) {
        log.info("发送中优先级通知 - 设备: {}, 异常: {}", record.getEquipmentId(), record.getExceptionDescription());
        
        // 发送APP推送给设备科
        sendAppPushNotification("设备科", buildMediumPriorityMessage(record));
        
        return true;
    }

    /**
     * 发送低优先级通知
     */
    private Boolean sendLowPriorityNotification(ExceptionHandlingRecord record) {
        log.info("记录低优先级异常 - 设备: {}, 异常: {}", record.getEquipmentId(), record.getExceptionDescription());
        
        // 低优先级异常通过日报方式通知，不立即发送
        // 这里可以将异常记录到日报队列中
        
        return true;
    }

    /**
     * 发送升级警报
     */
    private Boolean sendEscalationAlert(ExceptionHandlingRecord record) {
        log.info("发送升级警报 - 设备: {}, 升级级别: {}", record.getEquipmentId(), record.getEscalationLevel());
        
        String message = String.format(
            "【异常升级警报】设备%s异常处理超时，升级级别: %d，请立即处理！异常描述: %s",
            record.getEquipmentId(), record.getEscalationLevel(), record.getExceptionDescription());
        
        // 发送给更高级别的管理人员
        sendSmsNotification("医院管理层", message);
        sendAppPushNotification("医院管理层", message);
        
        return true;
    }

    /**
     * 发送工单警报
     */
    private Boolean sendWorkOrderAlert(String workOrderId) {
        log.info("发送工单创建通知: {}", workOrderId);
        
        String message = String.format("新的维修工单已创建，工单号: %s，请及时处理", workOrderId);
        
        // 通知相关技师
        sendAppPushNotification("维修技师", message);
        
        return true;
    }

    /**
     * 构建紧急消息
     */
    private String buildUrgentMessage(ExceptionHandlingRecord record) {
        return String.format(
            "【紧急异常】设备%s发生严重异常，请立即处理！异常描述: %s，记录ID: %s",
            record.getEquipmentId(), record.getExceptionDescription(), record.getId());
    }

    /**
     * 构建高优先级消息
     */
    private String buildHighPriorityMessage(ExceptionHandlingRecord record) {
        return String.format(
            "【重要异常】设备%s发生异常，请尽快处理。异常描述: %s，记录ID: %s",
            record.getEquipmentId(), record.getExceptionDescription(), record.getId());
    }

    /**
     * 构建中优先级消息
     */
    private String buildMediumPriorityMessage(ExceptionHandlingRecord record) {
        return String.format(
            "【设备异常】设备%s发生异常，请安排处理。异常描述: %s，记录ID: %s",
            record.getEquipmentId(), record.getExceptionDescription(), record.getId());
    }

    /**
     * 发送短信通知
     */
    private void sendSmsNotification(String target, String message) {
        log.info("发送短信给 {} : {}", target, message);
        // 实际实现中会调用短信服务接口
    }

    /**
     * 发送APP推送通知
     */
    private void sendAppPushNotification(String target, String message) {
        log.info("发送APP推送给 {} : {}", target, message);
        // 实际实现中会调用推送服务接口
    }

    /**
     * 发送电话通知
     */
    private void sendPhoneNotification(String target, ExceptionHandlingRecord record) {
        log.info("发送电话通知给 {} - 设备: {}", target, record.getEquipmentId());
        // 实际实现中会调用电话服务接口
    }
}