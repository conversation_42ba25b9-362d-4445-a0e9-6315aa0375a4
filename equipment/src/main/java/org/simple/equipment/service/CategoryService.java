package org.simple.equipment.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.Category;

import java.util.List;

/**
 * 设备分类服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface CategoryService extends IService<Category> {

    /**
     * 查询分类树形结构
     *
     * @return 分类树
     */
    List<Tree<String>> getCategoryTree();

    /**
     * 查询分类列表
     *
     * @param category 查询条件
     * @return 分类列表
     */
    List<Category> getCategoryList(Category category);

    /**
     * 新增分类
     *
     * @param category 分类信息
     * @return 操作结果
     */
    FrResult<?> saveCategory(Category category);

    /**
     * 修改分类
     *
     * @param category 分类信息
     * @return 操作结果
     */
    FrResult<?> updateCategory(Category category);

    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 操作结果
     */
    FrResult<?> deleteCategory(String id);

    /**
     * 检查分类编码是否存在
     *
     * @param categoryCode 分类编码
     * @param excludeId    排除的ID
     * @return 是否存在
     */
    boolean checkCategoryCodeExists(String categoryCode, String excludeId);

    /**
     * 检查分类是否有子节点
     *
     * @param id 分类ID
     * @return 是否有子节点
     */
    boolean hasChildren(String id);

    /**
     * 检查分类下是否有设备
     *
     * @param id 分类ID
     * @return 是否有设备
     */
    boolean hasAssets(String id);

    /**
     * 检查分类下是否有设备类型
     *
     * @param id 分类ID
     * @return 是否有设备类型
     */
    boolean hasTypes(String id);
}