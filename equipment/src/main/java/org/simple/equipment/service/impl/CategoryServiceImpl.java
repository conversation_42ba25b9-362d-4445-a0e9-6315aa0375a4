package org.simple.equipment.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.Category;
import org.simple.equipment.mapper.CategoryMapper;
import org.simple.equipment.service.CategoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备分类服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    @Override
    public List<Tree<String>> getCategoryTree() {
        // 查询所有有效分类
        List<Category> categories = baseMapper.selectActiveList(AuthUtil.getTenantId());
        
        // 构建树形结构
        TreeNodeConfig config = new TreeNodeConfig();
        config.setIdKey("id");
        config.setParentIdKey("parentId");
        config.setNameKey("categoryName");
        config.setWeightKey("sort");
        config.setChildrenKey("children");
        
        return TreeUtil.build(categories, "0", config, (category, tree) -> {
            tree.setId(category.getId());
            tree.setParentId(category.getParentId());
            tree.setName(category.getCategoryName());
            tree.setWeight(category.getSort());
            tree.putExtra("categoryCode", category.getCategoryCode());
            tree.putExtra("status", category.getStatus());
        });
    }

    @Override
    public List<Category> getCategoryList(Category category) {
        return baseMapper.selectCategoryList(category);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> saveCategory(Category category) {
        try {
            // 检查分类编码是否重复
            if (checkCategoryCodeExists(category.getCategoryCode(), null)) {
                return FrResult.failed("分类编码已存在");
            }

            // 设置基础信息
            category.setId(RandomUtil.getUserId());
            category.setTenantId(AuthUtil.getTenantId());
            category.setCreator(AuthUtil.getUserId());
            category.setCreateDate(LocalDateTime.now());
            category.setUpdateDate(LocalDateTime.now());
            category.setStatus("0");

            // 设置默认父级ID
            if (StrUtil.isBlank(category.getParentId())) {
                category.setParentId("0");
            }

            // 设置默认排序
            if (category.getSort() == null) {
                category.setSort(0);
            }

            boolean success = save(category);
            return success ? FrResult.successNodata("新增成功") : FrResult.failed("新增失败");
        } catch (Exception e) {
            log.error("新增设备分类失败", e);
            return FrResult.failed("新增失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> updateCategory(Category category) {
        try {
            // 检查分类是否存在
            Category existCategory = getById(category.getId());
            if (existCategory == null) {
                return FrResult.failed("分类不存在");
            }

            // 检查分类编码是否重复
            if (checkCategoryCodeExists(category.getCategoryCode(), category.getId())) {
                return FrResult.failed("分类编码已存在");
            }

            // 不能将自己设置为自己的父级
            if (category.getId().equals(category.getParentId())) {
                return FrResult.failed("不能将自己设置为父级分类");
            }

            // 设置更新信息
            category.setUpdater(AuthUtil.getUserId());
            category.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(category);
            return success ? FrResult.successNodata("修改成功") : FrResult.failed("修改失败");
        } catch (Exception e) {
            log.error("修改设备分类失败", e);
            return FrResult.failed("修改失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> deleteCategory(String id) {
        try {
            Category category = getById(id);
            if (category == null) {
                return FrResult.failed("分类不存在");
            }

            // 检查是否有子分类
            if (hasChildren(id)) {
                return FrResult.failed("存在子分类，无法删除");
            }

            // 检查是否有设备类型
            if (hasTypes(id)) {
                return FrResult.failed("分类下存在设备类型，无法删除");
            }

            // 检查是否有设备
            if (hasAssets(id)) {
                return FrResult.failed("分类下存在设备，无法删除");
            }

            // 逻辑删除：修改状态
            category.setStatus("1");
            category.setUpdater(AuthUtil.getUserId());
            category.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(category);
            return success ? FrResult.successNodata("删除成功") : FrResult.failed("删除失败");
        } catch (Exception e) {
            log.error("删除设备分类失败", e);
            return FrResult.failed("删除失败：" + e.getMessage());
        }
    }

    @Override
    public boolean checkCategoryCodeExists(String categoryCode, String excludeId) {
        Category category = baseMapper.selectByCategoryCode(categoryCode, AuthUtil.getTenantId(), excludeId);
        return category != null;
    }

    @Override
    public boolean hasChildren(String id) {
        int count = baseMapper.selectChildrenCount(id, AuthUtil.getTenantId());
        return count > 0;
    }

    @Override
    public boolean hasAssets(String id) {
        int count = baseMapper.selectAssetCount(id, AuthUtil.getTenantId());
        return count > 0;
    }

    @Override
    public boolean hasTypes(String id) {
        int count = baseMapper.selectTypeCount(id, AuthUtil.getTenantId());
        return count > 0;
    }
}