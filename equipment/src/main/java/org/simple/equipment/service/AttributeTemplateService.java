package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.AttributeTemplate;

import java.util.List;
import java.util.Map;

/**
 * 设备属性模板服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AttributeTemplateService extends IService<AttributeTemplate> {

    /**
     * 查询有效的模板列表
     *
     * @return 模板列表
     */
    List<AttributeTemplate> getActiveTemplateList();

    /**
     * 根据设备分类和类型查询模板
     *
     * @param categoryId 分类ID
     * @param typeId 类型ID
     * @return 模板列表
     */
    List<AttributeTemplate> getTemplatesByCategory(String categoryId, String typeId);

    /**
     * 查询模板列表
     *
     * @param template 查询条件
     * @return 模板列表
     */
    List<AttributeTemplate> getTemplateList(AttributeTemplate template);

    /**
     * 查询模板详情（包含字段信息）
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    Map<String, Object> getTemplateDetail(String templateId);

    /**
     * 保存属性模板
     *
     * @param template 模板信息
     * @param fieldIds 关联的字段ID列表
     * @return 结果
     */
    FrResult<?> saveAttributeTemplate(AttributeTemplate template, List<String> fieldIds);

    /**
     * 更新属性模板
     *
     * @param template 模板信息
     * @param fieldIds 关联的字段ID列表
     * @return 结果
     */
    FrResult<?> updateAttributeTemplate(AttributeTemplate template, List<String> fieldIds);

    /**
     * 删除属性模板
     *
     * @param id 模板ID
     * @return 结果
     */
    FrResult<?> deleteAttributeTemplate(String id);

    /**
     * 检查模板编码是否存在
     *
     * @param templateCode 模板编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkTemplateCodeExists(String templateCode, String excludeId);

    /**
     * 更新模板状态
     *
     * @param id 模板ID
     * @param isEnabled 是否启用
     * @return 结果
     */
    FrResult<?> updateTemplateStatus(String id, String isEnabled);

    /**
     * 复制模板
     *
     * @param templateId 源模板ID
     * @param newTemplateName 新模板名称
     * @param newTemplateCode 新模板编码
     * @return 结果
     */
    FrResult<?> copyTemplate(String templateId, String newTemplateName, String newTemplateCode);

    /**
     * 导入模板
     *
     * @param templateData 模板数据（JSON格式）
     * @return 结果
     */
    FrResult<?> importTemplate(String templateData);

    /**
     * 导出模板
     *
     * @param templateId 模板ID
     * @return 模板数据
     */
    FrResult<String> exportTemplate(String templateId);

    /**
     * 应用模板到设备
     *
     * @param templateId 模板ID
     * @param assetIds 设备ID列表
     * @param applyMode 应用模式：full-全量，increment-增量
     * @return 结果
     */
    FrResult<?> applyTemplateToAssets(String templateId, List<String> assetIds, String applyMode);

    /**
     * 从设备创建模板
     *
     * @param assetId 设备ID
     * @param templateName 模板名称
     * @param templateCode 模板编码
     * @return 结果
     */
    FrResult<?> createTemplateFromAsset(String assetId, String templateName, String templateCode);

    /**
     * 查询模板使用统计
     *
     * @return 统计信息
     */
    List<Map<String, Object>> getUsageStatistics();

    /**
     * 升级模板版本
     *
     * @param templateId 模板ID
     * @param newVersion 新版本号
     * @return 结果
     */
    FrResult<?> upgradeTemplateVersion(String templateId, String newVersion);
}