package org.simple.equipment.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.dto.mybatis.Page;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.equipment.dto.AssetDto;
import org.simple.equipment.entity.Asset;
import org.simple.equipment.enums.EquipmentStatus;
import org.simple.equipment.mapper.AssetMapper;
import org.simple.equipment.service.AssetService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备档案服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AssetServiceImpl extends ServiceImpl<AssetMapper, Asset> implements AssetService {

    @Override
    public IPage<AssetDto> listAssets(Page<AssetDto> page, Asset asset) {
        return baseMapper.listAssets(page, asset);
    }

    @Override
    public AssetDto getAssetById(String id) {
        AssetDto assetDto = baseMapper.getAssetById(id);
        if (assetDto != null) {
            // 设置状态名称
            assetDto.setEquipmentStatusName(EquipmentStatus.getNameByCode(assetDto.getEquipmentStatus()));
        }
        return assetDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> saveAsset(Asset asset) {
        try {
            // 设置基础信息
            String assetId = RandomUtil.getUserId();
            asset.setId(assetId);
            asset.setTenantId(AuthUtil.getTenantId());
            asset.setCreator(AuthUtil.getUserId());
            asset.setCreateDate(LocalDateTime.now());
            asset.setUpdateDate(LocalDateTime.now());
            asset.setIsDeleted("0");

            // 自动生成设备编码
            if (StrUtil.isBlank(asset.getEquipmentCode())) {
                String equipmentCode = generateEquipmentCode(asset.getCategoryId());
                asset.setEquipmentCode(equipmentCode);
            }

            // 检查设备编码是否重复
            Asset existAsset = baseMapper.getByEquipmentCode(asset.getEquipmentCode(), asset.getTenantId());
            if (existAsset != null) {
                return FrResult.failed("设备编码已存在");
            }

            // 生成二维码
            asset.setQrCode(generateQrCode(assetId));

            // 设置默认状态
            if (StrUtil.isBlank(asset.getEquipmentStatus())) {
                asset.setEquipmentStatus(EquipmentStatus.NORMAL.getCode());
            }

            boolean success = save(asset);
            return success ? FrResult.successNodata("新增成功") : FrResult.failed("新增失败");
        } catch (Exception e) {
            log.error("新增设备档案失败", e);
            return FrResult.failed("新增失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> updateAsset(Asset asset) {
        try {
            // 检查设备是否存在
            Asset existAsset = getById(asset.getId());
            if (existAsset == null) {
                return FrResult.failed("设备不存在");
            }

            // 如果修改了设备编码，检查是否重复
            if (!existAsset.getEquipmentCode().equals(asset.getEquipmentCode())) {
                Asset codeCheck = baseMapper.getByEquipmentCode(asset.getEquipmentCode(), AuthUtil.getTenantId());
                if (codeCheck != null && !codeCheck.getId().equals(asset.getId())) {
                    return FrResult.failed("设备编码已存在");
                }
            }

            // 设置更新信息
            asset.setUpdater(AuthUtil.getUserId());
            asset.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(asset);
            return success ? FrResult.successNodata("修改成功") : FrResult.failed("修改失败");
        } catch (Exception e) {
            log.error("修改设备档案失败", e);
            return FrResult.failed("修改失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> deleteAsset(String id) {
        try {
            Asset asset = getById(id);
            if (asset == null) {
                return FrResult.failed("设备不存在");
            }

            // 逻辑删除
            asset.setIsDeleted("1");
            asset.setUpdater(AuthUtil.getUserId());
            asset.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(asset);
            return success ? FrResult.successNodata("删除成功") : FrResult.failed("删除失败");
        } catch (Exception e) {
            log.error("删除设备档案失败", e);
            return FrResult.failed("删除失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> batchDeleteAssets(List<String> ids) {
        try {
            List<Asset> assets = new ArrayList<>();
            for (String id : ids) {
                Asset asset = new Asset();
                asset.setId(id);
                asset.setIsDeleted("1");
                asset.setUpdater(AuthUtil.getUserId());
                asset.setUpdateDate(LocalDateTime.now());
                assets.add(asset);
            }

            boolean success = updateBatchById(assets);
            return success ? FrResult.successNodata("批量删除成功") : FrResult.failed("批量删除失败");
        } catch (Exception e) {
            log.error("批量删除设备档案失败", e);
            return FrResult.failed("批量删除失败：" + e.getMessage());
        }
    }

    @Override
    public String generateEquipmentCode(String categoryId) {
        // 根据分类生成编码前缀
        String prefix = "EQ";
        if (StrUtil.isNotBlank(categoryId)) {
            // 这里可以根据分类设置不同的前缀
            prefix = "EQ" + categoryId.substring(0, Math.min(2, categoryId.length())).toUpperCase();
        }
        
        return baseMapper.generateEquipmentCode(prefix, AuthUtil.getTenantId());
    }

    @Override
    public String generateQrCode(String assetId) {
        // 生成二维码内容，包含设备ID和租户信息
        Map<String, String> qrData = new HashMap<>();
        qrData.put("assetId", assetId);
        qrData.put("tenantId", AuthUtil.getTenantId());
        qrData.put("type", "equipment");
        
        return JSONUtil.toJsonStr(qrData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> importAssets(MultipartFile file) {
        try {
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            List<Map<String, Object>> dataList = reader.readAll();
            
            int successCount = 0;
            int failCount = 0;
            List<String> errorMessages = new ArrayList<>();

            for (int i = 0; i < dataList.size(); i++) {
                try {
                    Map<String, Object> row = dataList.get(i);
                    
                    Asset asset = new Asset();
                    asset.setEquipmentName(String.valueOf(row.get("设备名称")));
                    asset.setModel(String.valueOf(row.get("型号")));
                    asset.setManufacturer(String.valueOf(row.get("生产商")));
                    asset.setSupplier(String.valueOf(row.get("供应商")));
                    // ... 其他字段映射

                    FrResult<?> result = saveAsset(asset);
                    if (result.getCode() == 0) {
                        successCount++;
                    } else {
                        failCount++;
                        errorMessages.add("第" + (i + 1) + "行：" + result.getMsg());
                    }
                } catch (Exception e) {
                    failCount++;
                    errorMessages.add("第" + (i + 1) + "行：" + e.getMessage());
                }
            }

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("successCount", successCount);
            resultData.put("failCount", failCount);
            resultData.put("errorMessages", errorMessages);

            return FrResult.success(resultData, "导入完成");
        } catch (Exception e) {
            log.error("导入设备档案失败", e);
            return FrResult.failed("导入失败：" + e.getMessage());
        }
    }

    @Override
    public byte[] exportAssets(Asset asset) {
        try {
            // 查询数据
            Page<AssetDto> page = new Page<>();
            page.setSize(10000); // 设置大一点的分页大小
            IPage<AssetDto> result = listAssets(page, asset);

            // 创建Excel
            ExcelWriter writer = ExcelUtil.getWriter();
            
            // 设置表头
            writer.addHeaderAlias("equipmentCode", "设备编码");
            writer.addHeaderAlias("equipmentName", "设备名称");
            writer.addHeaderAlias("categoryName", "设备分类");
            writer.addHeaderAlias("typeName", "设备类型");
            writer.addHeaderAlias("model", "型号");
            writer.addHeaderAlias("manufacturer", "生产商");
            writer.addHeaderAlias("supplier", "供应商");
            writer.addHeaderAlias("departmentName", "所属科室");
            writer.addHeaderAlias("locationName", "所在位置");
            writer.addHeaderAlias("equipmentStatusName", "设备状态");

            // 写入数据
            writer.write(result.getRecords(), true);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            writer.flush(out);
            writer.close();

            return out.toByteArray();
        } catch (Exception e) {
            log.error("导出设备档案失败", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    @Override
    public List<AssetDto> getAssetsByDepartment(String departmentId) {
        return baseMapper.getAssetsByDepartment(departmentId, AuthUtil.getTenantId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> updateAssetStatus(String assetId, String status) {
        try {
            Asset asset = new Asset();
            asset.setId(assetId);
            asset.setEquipmentStatus(status);
            asset.setUpdater(AuthUtil.getUserId());
            asset.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(asset);
            return success ? FrResult.successNodata("状态更新成功") : FrResult.failed("状态更新失败");
        } catch (Exception e) {
            log.error("更新设备状态失败", e);
            return FrResult.failed("状态更新失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getStatusStatistics() {
        return baseMapper.getStatusStatistics(AuthUtil.getTenantId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> transferAsset(String assetId, String newDepartmentId, 
                                   String newLocationId, String reason) {
        try {
            Asset asset = getById(assetId);
            if (asset == null) {
                return FrResult.failed("设备不存在");
            }

            // 更新设备归属
            asset.setDepartmentId(newDepartmentId);
            if (StrUtil.isNotBlank(newLocationId)) {
                asset.setLocationId(newLocationId);
            }
            asset.setUpdater(AuthUtil.getUserId());
            asset.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(asset);
            
            // TODO: 记录调拨历史
            
            return success ? FrResult.successNodata("设备调拨成功") : FrResult.failed("设备调拨失败");
        } catch (Exception e) {
            log.error("设备调拨失败", e);
            return FrResult.failed("设备调拨失败：" + e.getMessage());
        }
    }
}