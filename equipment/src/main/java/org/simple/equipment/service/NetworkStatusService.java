package org.simple.equipment.service;

import org.simple.equipment.dto.NetworkStatusUpdate;

/**
 * 网络状态监控服务接口
 * 
 * <AUTHOR>
 */
public interface NetworkStatusService {

    /**
     * 更新网络状态
     * 
     * @param update 网络状态更新
     * @return 更新结果
     */
    Boolean updateNetworkStatus(NetworkStatusUpdate update);

    /**
     * 检测网络变化
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param currentStatus 当前网络状态
     * @return 是否需要触发同步
     */
    Boolean detectNetworkChange(String userId, String deviceId, String currentStatus);

    /**
     * 获取用户当前网络状态
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 网络状态信息
     */
    NetworkStatusUpdate getCurrentNetworkStatus(String userId, String deviceId);

    /**
     * 记录网络状态变化日志
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param fromStatus 变化前状态
     * @param toStatus 变化后状态
     */
    void logNetworkStatusChange(String userId, String deviceId, String fromStatus, String toStatus);

    /**
     * 获取网络质量评分
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 网络质量评分(0-100)
     */
    Integer getNetworkQualityScore(String userId, String deviceId);

    /**
     * 是否适合进行数据同步
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 是否适合同步
     */
    Boolean isSuitableForSync(String userId, String deviceId);
}