package org.simple.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.IdUtil;
import org.simple.equipment.entity.WorkOrder;
import org.simple.equipment.mapper.WorkOrderMapper;
import org.simple.equipment.service.WorkOrderNotificationService;
import org.simple.equipment.service.WorkOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工单服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class WorkOrderServiceImpl extends ServiceImpl<WorkOrderMapper, WorkOrder> implements WorkOrderService {

    private final WorkOrderNotificationService notificationService;

    public WorkOrderServiceImpl(WorkOrderNotificationService notificationService) {
        this.notificationService = notificationService;
    }

    @Override
    @Transactional
    public boolean createWorkOrder(WorkOrder workOrder) {
        // 检查工单编码是否存在
        if (workOrder.getOrderCode() != null && checkOrderCodeExists(workOrder.getOrderCode(), null)) {
            throw new RuntimeException("工单编码已存在");
        }

        // 设置基本信息
        workOrder.setId(IdUtil.getId());
        workOrder.setTenantId(AuthUtil.getTenantId());
        workOrder.setCreateDate(LocalDateTime.now());
        workOrder.setCreator(AuthUtil.getUserId());
        workOrder.setOrderStatus("PENDING"); // 默认待处理状态
        workOrder.setApplyTime(LocalDateTime.now());

        // 如果没有指定工单编码，自动生成
        if (workOrder.getOrderCode() == null || workOrder.getOrderCode().trim().isEmpty()) {
            workOrder.setOrderCode(generateOrderCode(workOrder.getOrderType()));
        }

        // 设置申请人信息
        if (workOrder.getApplicantId() == null) {
            workOrder.setApplicantId(AuthUtil.getUserId());
            // TODO: 从用户服务获取用户名
            workOrder.setApplicantName(AuthUtil.getUserId());
        }

        boolean success = save(workOrder);
        
        if (success) {
            // 发送创建通知
            notificationService.sendCreateNotification(workOrder);
            
            // 尝试自动分配
            autoAssignWorkOrder(workOrder.getId());
        }
        
        return success;
    }

    @Override
    @Transactional
    public boolean updateWorkOrder(WorkOrder workOrder) {
        // 检查工单编码是否存在
        if (checkOrderCodeExists(workOrder.getOrderCode(), workOrder.getId())) {
            throw new RuntimeException("工单编码已存在");
        }

        // 设置更新信息
        workOrder.setUpdateDate(LocalDateTime.now());
        workOrder.setUpdater(AuthUtil.getUserId());
        workOrder.setTenantId(AuthUtil.getTenantId());

        return updateById(workOrder);
    }

    @Override
    @Transactional
    public boolean deleteWorkOrder(String id) {
        // 检查工单状态
        WorkOrder workOrder = getById(id);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        if ("PROCESSING".equals(workOrder.getOrderStatus())) {
            throw new RuntimeException("处理中的工单不能删除");
        }
        if ("COMPLETED".equals(workOrder.getOrderStatus())) {
            throw new RuntimeException("已完成的工单不能删除");
        }

        return removeById(id);
    }

    @Override
    @Transactional
    public boolean assignWorkOrder(String orderId, String userId, String userName) {
        WorkOrder workOrder = getById(orderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        if (!"PENDING".equals(workOrder.getOrderStatus())) {
            throw new RuntimeException("只有待处理状态的工单才能分配");
        }

        workOrder.setAssignedUserId(userId);
        workOrder.setAssignedUserName(userName);
        workOrder.setAssignTime(LocalDateTime.now());
        workOrder.setUpdateDate(LocalDateTime.now());
        workOrder.setUpdater(AuthUtil.getUserId());
        
        boolean success = updateById(workOrder);
        
        if (success) {
            // 发送分配通知
            notificationService.sendAssignNotification(workOrder);
        }
        
        return success;
    }

    @Override
    @Transactional
    public boolean startWorkOrder(String orderId) {
        WorkOrder workOrder = getById(orderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        if (!"PENDING".equals(workOrder.getOrderStatus())) {
            throw new RuntimeException("只有待处理状态的工单才能开始处理");
        }

        workOrder.setOrderStatus("PROCESSING");
        workOrder.setStartTime(LocalDateTime.now());
        workOrder.setUpdateDate(LocalDateTime.now());
        workOrder.setUpdater(AuthUtil.getUserId());
        
        boolean success = updateById(workOrder);
        
        if (success) {
            // 发送开始处理通知
            notificationService.sendStartNotification(workOrder);
        }
        
        return success;
    }

    @Override
    @Transactional
    public boolean completeWorkOrder(String orderId, String solution) {
        WorkOrder workOrder = getById(orderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        if (!"PROCESSING".equals(workOrder.getOrderStatus())) {
            throw new RuntimeException("只有处理中的工单才能完成");
        }

        workOrder.setOrderStatus("COMPLETED");
        workOrder.setCompleteTime(LocalDateTime.now());
        workOrder.setSolution(solution);
        workOrder.setUpdateDate(LocalDateTime.now());
        workOrder.setUpdater(AuthUtil.getUserId());
        
        boolean success = updateById(workOrder);
        
        if (success) {
            // 发送完成通知
            notificationService.sendCompleteNotification(workOrder);
        }
        
        return success;
    }

    @Override
    @Transactional
    public boolean cancelWorkOrder(String orderId, String reason) {
        WorkOrder workOrder = getById(orderId);
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }
        if ("COMPLETED".equals(workOrder.getOrderStatus())) {
            throw new RuntimeException("已完成的工单不能取消");
        }

        workOrder.setOrderStatus("CANCELLED");
        workOrder.setSolution(reason);
        workOrder.setUpdateDate(LocalDateTime.now());
        workOrder.setUpdater(AuthUtil.getUserId());
        
        boolean success = updateById(workOrder);
        
        if (success) {
            // 发送取消通知
            notificationService.sendCancelNotification(workOrder);
        }
        
        return success;
    }

    @Override
    public Map<String, Object> getWorkOrderStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 基本统计
        QueryWrapper<WorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", AuthUtil.getTenantId());
        
        statistics.put("totalOrders", count(queryWrapper));
        
        // 按状态统计
        List<Map<String, Object>> statusStats = baseMapper.getWorkOrderStatistics(AuthUtil.getTenantId());
        statistics.put("statusStats", statusStats);
        
        // 待处理工单数量
        List<WorkOrder> pendingOrders = getPendingWorkOrders();
        statistics.put("pendingOrders", pendingOrders.size());
        
        // 超时工单数量
        List<WorkOrder> timeoutOrders = getTimeoutWorkOrders(24);
        statistics.put("timeoutOrders", timeoutOrders.size());
        
        return statistics;
    }

    @Override
    public List<WorkOrder> getUserWorkOrders(String userId) {
        return baseMapper.getUserWorkOrders(userId, AuthUtil.getTenantId());
    }

    @Override
    public List<WorkOrder> getEquipmentWorkOrders(String equipmentId) {
        return baseMapper.getEquipmentWorkOrders(equipmentId, AuthUtil.getTenantId());
    }

    @Override
    public List<WorkOrder> getPendingWorkOrders() {
        return baseMapper.getPendingWorkOrders(AuthUtil.getTenantId());
    }

    @Override
    public List<WorkOrder> getTimeoutWorkOrders(Integer hours) {
        return baseMapper.getTimeoutWorkOrders(AuthUtil.getTenantId(), hours);
    }

    @Override
    public boolean checkOrderCodeExists(String orderCode, String excludeId) {
        QueryWrapper<WorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_code", orderCode)
                .eq("tenant_id", AuthUtil.getTenantId());
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) > 0;
    }

    @Override
    public String generateOrderCode(String orderType) {
        // 生成格式：WO + 类型简码 + 日期 + 序号
        // 例如：WO-REPAIR-20240101-001
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 查询当天同类型工单数量
        QueryWrapper<WorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("order_type", orderType)
                .eq("tenant_id", AuthUtil.getTenantId())
                .likeRight("order_code", "WO-" + orderType + "-" + dateStr);
        
        long count = count(queryWrapper);
        String sequence = String.format("%03d", count + 1);
        
        return "WO-" + orderType + "-" + dateStr + "-" + sequence;
    }

    @Override
    public void sendWorkOrderNotification(String orderId, String type) {
        // 兼容旧接口，委托给通知服务
        WorkOrder workOrder = getById(orderId);
        if (workOrder != null) {
            switch (type) {
                case "CREATE":
                    notificationService.sendCreateNotification(workOrder);
                    break;
                case "ASSIGN":
                    notificationService.sendAssignNotification(workOrder);
                    break;
                case "START":
                    notificationService.sendStartNotification(workOrder);
                    break;
                case "COMPLETE":
                    notificationService.sendCompleteNotification(workOrder);
                    break;
                case "CANCEL":
                    notificationService.sendCancelNotification(workOrder);
                    break;
                case "URGE":
                    notificationService.sendUrgeNotification(workOrder);
                    break;
                default:
                    log.warn("未知的通知类型: {}", type);
            }
        }
    }

    @Override
    @Transactional
    public boolean autoAssignWorkOrder(String orderId) {
        // TODO: 实现自动分配逻辑
        // 可以根据设备类型、科室、工作负载等规则自动分配
        log.info("尝试自动分配工单：{}", orderId);
        return false;
    }

    @Override
    public List<Map<String, Object>> getProcessingTimeStatistics() {
        return baseMapper.getProcessingTimeStatistics(AuthUtil.getTenantId());
    }

    @Override
    public boolean urgeWorkOrder(String orderId) {
        WorkOrder workOrder = getById(orderId);
        if (workOrder != null) {
            log.info("催办工单：{}", orderId);
            notificationService.sendUrgeNotification(workOrder);
            return true;
        }
        return false;
    }
}