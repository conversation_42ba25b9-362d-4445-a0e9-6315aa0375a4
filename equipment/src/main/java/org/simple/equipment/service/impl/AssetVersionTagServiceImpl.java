package org.simple.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.equipment.entity.AssetVersionTag;
import org.simple.equipment.mapper.AssetVersionTagMapper;
import org.simple.equipment.service.AssetVersionTagService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备档案版本标签服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AssetVersionTagServiceImpl extends ServiceImpl<AssetVersionTagMapper, AssetVersionTag> implements AssetVersionTagService {

    @Override
    public List<AssetVersionTag> getTagsByVersionId(String versionId) {
        QueryWrapper<AssetVersionTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("version_id", versionId)
                .eq("tenant_id", AuthUtil.getTenantId())
                .orderByAsc("sort_order", "tag_name");
        return list(queryWrapper);
    }
}