package org.simple.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.InspectionTemplate;

import java.util.List;

/**
 * 设备点检模板服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface InspectionTemplateService extends IService<InspectionTemplate> {

    /**
     * 分页查询点检模板
     */
    IPage<InspectionTemplate> getTemplatePage(Page<InspectionTemplate> page, String templateCode, String templateName,
                                               String categoryId, String cycleType, String templateStatus, String templateType);

    /**
     * 创建点检模板
     */
    boolean createTemplate(InspectionTemplate template);

    /**
     * 更新点检模板
     */
    boolean updateTemplate(InspectionTemplate template);

    /**
     * 删除点检模板
     */
    boolean deleteTemplate(String templateId);

    /**
     * 根据ID获取模板详情
     */
    InspectionTemplate getTemplateById(String templateId);

    /**
     * 根据分类查询模板
     */
    List<InspectionTemplate> getTemplatesByCategory(String categoryId);

    /**
     * 根据设备型号查询模板
     */
    List<InspectionTemplate> getTemplatesByModel(String equipmentModel);

    /**
     * 查询已发布的模板
     */
    List<InspectionTemplate> getPublishedTemplates();

    /**
     * 根据周期类型查询模板
     */
    List<InspectionTemplate> getTemplatesByCycle(String cycleType);

    /**
     * 检查模板编码是否存在
     */
    boolean checkTemplateCodeExists(String templateCode, String excludeId);

    /**
     * 生成模板编码
     */
    String generateTemplateCode(String categoryId);

    /**
     * 发布模板
     */
    boolean publishTemplate(String templateId, String reviewComment);

    /**
     * 归档模板
     */
    boolean archiveTemplate(String templateId);

    /**
     * 复制模板
     */
    boolean copyTemplate(String sourceTemplateId, String templateName);

    /**
     * 批量更新状态
     */
    boolean batchUpdateStatus(List<String> templateIds, String status);

    /**
     * 更新使用次数
     */
    void updateUsageCount(String templateId);

    /**
     * 获取模板统计信息
     */
    List<InspectionTemplate> getTemplateStatistics();

    /**
     * 验证模板有效性
     */
    boolean validateTemplate(String templateId);

    /**
     * 导出模板
     */
    void exportTemplate(String templateId);

    /**
     * 导入模板
     */
    boolean importTemplate(String templateData);
}