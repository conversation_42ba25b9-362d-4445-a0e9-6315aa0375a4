package org.simple.equipment.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.AssetFieldValue;
import org.simple.equipment.entity.CustomField;
import org.simple.equipment.mapper.AssetFieldValueMapper;
import org.simple.equipment.service.AssetFieldValueService;
import org.simple.equipment.service.CustomFieldService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 设备自定义字段值服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AssetFieldValueServiceImpl extends ServiceImpl<AssetFieldValueMapper, AssetFieldValue> implements AssetFieldValueService {

    @Resource
    private CustomFieldService customFieldService;

    @Override
    public List<AssetFieldValue> getValuesByAssetId(String assetId) {
        return baseMapper.selectByAssetId(assetId, AuthUtil.getTenantId());
    }

    @Override
    public List<Map<String, Object>> getAssetFieldValues(String assetId) {
        return baseMapper.selectAssetFieldValues(assetId, AuthUtil.getTenantId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> saveAssetFieldValues(String assetId, Map<String, String> fieldValues) {
        try {
            // 先删除原有的字段值
            baseMapper.deleteByAssetId(assetId, AuthUtil.getTenantId());

            // 保存新的字段值
            List<AssetFieldValue> valueList = new ArrayList<>();
            
            for (Map.Entry<String, String> entry : fieldValues.entrySet()) {
                String fieldId = entry.getKey();
                String fieldValue = entry.getValue();
                
                // 跳过空值
                if (StrUtil.isBlank(fieldValue)) {
                    continue;
                }

                // 验证字段值
                FrResult<?> validateResult = validateFieldValue(fieldId, fieldValue);
                if (!validateResult.isSuccess()) {
                    return validateResult;
                }

                AssetFieldValue value = new AssetFieldValue();
                value.setId(RandomUtil.getUserId());
                value.setAssetId(assetId);
                value.setFieldId(fieldId);
                value.setFieldValue(fieldValue);
                value.setTenantId(AuthUtil.getTenantId());
                value.setCreator(AuthUtil.getUserId());
                value.setCreateDate(LocalDateTime.now());
                value.setUpdateDate(LocalDateTime.now());

                valueList.add(value);
            }

            if (!valueList.isEmpty()) {
                boolean success = saveBatch(valueList);
                return success ? FrResult.successNodata("保存成功") : FrResult.failed("保存失败");
            }
            
            return FrResult.successNodata("保存成功");
        } catch (Exception e) {
            log.error("保存设备字段值失败", e);
            return FrResult.failed("保存失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> batchSaveFieldValues(List<AssetFieldValue> fieldValues) {
        try {
            if (fieldValues.isEmpty()) {
                return FrResult.failed("字段值列表为空");
            }

            // 设置基础信息
            for (AssetFieldValue value : fieldValues) {
                if (StrUtil.isBlank(value.getId())) {
                    value.setId(RandomUtil.getUserId());
                }
                value.setTenantId(AuthUtil.getTenantId());
                value.setCreator(AuthUtil.getUserId());
                value.setCreateDate(LocalDateTime.now());
                value.setUpdateDate(LocalDateTime.now());
            }

            boolean success = saveBatch(fieldValues);
            return success ? FrResult.successNodata("批量保存成功") : FrResult.failed("批量保存失败");
        } catch (Exception e) {
            log.error("批量保存字段值失败", e);
            return FrResult.failed("批量保存失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> deleteAssetFieldValues(String assetId) {
        try {
            int count = baseMapper.deleteByAssetId(assetId, AuthUtil.getTenantId());
            return FrResult.successNodata("删除成功，共删除" + count + "条记录");
        } catch (Exception e) {
            log.error("删除设备字段值失败", e);
            return FrResult.failed("删除失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> deleteFieldValues(String fieldId) {
        try {
            int count = baseMapper.deleteByFieldId(fieldId, AuthUtil.getTenantId());
            return FrResult.successNodata("删除成功，共删除" + count + "条记录");
        } catch (Exception e) {
            log.error("删除字段值失败", e);
            return FrResult.failed("删除失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> copyAssetFieldValues(String fromAssetId, String toAssetId) {
        try {
            // 查询源设备的字段值
            List<AssetFieldValue> sourceValues = baseMapper.selectByAssetId(fromAssetId, AuthUtil.getTenantId());
            
            if (sourceValues.isEmpty()) {
                return FrResult.failed("源设备没有自定义字段值");
            }

            // 先删除目标设备的字段值
            baseMapper.deleteByAssetId(toAssetId, AuthUtil.getTenantId());

            // 复制字段值
            List<AssetFieldValue> newValues = new ArrayList<>();
            for (AssetFieldValue sourceValue : sourceValues) {
                AssetFieldValue newValue = new AssetFieldValue();
                newValue.setId(RandomUtil.getUserId());
                newValue.setAssetId(toAssetId);
                newValue.setFieldId(sourceValue.getFieldId());
                newValue.setFieldValue(sourceValue.getFieldValue());
                newValue.setTenantId(AuthUtil.getTenantId());
                newValue.setCreator(AuthUtil.getUserId());
                newValue.setCreateDate(LocalDateTime.now());
                newValue.setUpdateDate(LocalDateTime.now());

                newValues.add(newValue);
            }

            boolean success = saveBatch(newValues);
            return success ? FrResult.successNodata("复制成功") : FrResult.failed("复制失败");
        } catch (Exception e) {
            log.error("复制设备字段值失败", e);
            return FrResult.failed("复制失败：" + e.getMessage());
        }
    }

    @Override
    public FrResult<?> validateFieldValue(String fieldId, String fieldValue) {
        try {
            // 查询字段配置
            CustomField field = customFieldService.getById(fieldId);
            if (field == null) {
                return FrResult.failed("字段不存在");
            }

            // 检查是否必填
            if ("1".equals(field.getIsRequired()) && StrUtil.isBlank(fieldValue)) {
                return FrResult.failed("字段[" + field.getFieldName() + "]为必填项");
            }

            // 如果值为空，跳过其他验证
            if (StrUtil.isBlank(fieldValue)) {
                return FrResult.success();
            }

            // 根据字段类型验证
            switch (field.getFieldType()) {
                case "number":
                    if (!StrUtil.isNumber(fieldValue)) {
                        return FrResult.failed("字段[" + field.getFieldName() + "]必须是数字");
                    }
                    break;
                case "date":
                    // 验证日期格式
                    if (!isValidDate(fieldValue)) {
                        return FrResult.failed("字段[" + field.getFieldName() + "]日期格式不正确");
                    }
                    break;
                case "datetime":
                    // 验证日期时间格式
                    if (!isValidDateTime(fieldValue)) {
                        return FrResult.failed("字段[" + field.getFieldName() + "]日期时间格式不正确");
                    }
                    break;
                case "select":
                case "radio":
                    // 验证选项值
                    if (!isValidOption(fieldValue, field.getOptions())) {
                        return FrResult.failed("字段[" + field.getFieldName() + "]选项值不正确");
                    }
                    break;
                case "checkbox":
                    // 验证多选值
                    if (!isValidCheckbox(fieldValue, field.getOptions())) {
                        return FrResult.failed("字段[" + field.getFieldName() + "]多选值不正确");
                    }
                    break;
            }

            // 验证自定义规则
            if (StrUtil.isNotBlank(field.getValidationRules())) {
                FrResult<?> ruleResult = validateCustomRules(fieldValue, field.getValidationRules(), field.getFieldName());
                if (!ruleResult.isSuccess()) {
                    return ruleResult;
                }
            }

            return FrResult.success();
        } catch (Exception e) {
            log.error("验证字段值失败", e);
            return FrResult.failed("验证失败：" + e.getMessage());
        }
    }

    /**
     * 验证日期格式
     */
    private boolean isValidDate(String value) {
        try {
            // 支持多种日期格式
            return value.matches("\\d{4}-\\d{2}-\\d{2}");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证日期时间格式
     */
    private boolean isValidDateTime(String value) {
        try {
            return value.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证选项值
     */
    private boolean isValidOption(String value, String options) {
        try {
            if (StrUtil.isBlank(options)) {
                return true;
            }
            
            List<Map<String, Object>> optionList = JSONUtil.toList(options, Map.class);
            for (Map<String, Object> option : optionList) {
                if (value.equals(option.get("value"))) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证多选值
     */
    private boolean isValidCheckbox(String value, String options) {
        try {
            if (StrUtil.isBlank(options)) {
                return true;
            }
            
            String[] values = value.split(",");
            List<Map<String, Object>> optionList = JSONUtil.toList(options, Map.class);
            
            for (String v : values) {
                boolean found = false;
                for (Map<String, Object> option : optionList) {
                    if (v.equals(option.get("value"))) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证自定义规则
     */
    private FrResult<?> validateCustomRules(String value, String validationRules, String fieldName) {
        try {
            Map<String, Object> rules = JSONUtil.toBean(validationRules, Map.class);
            
            // 最小长度验证
            if (rules.containsKey("minLength")) {
                int minLength = (Integer) rules.get("minLength");
                if (value.length() < minLength) {
                    return FrResult.failed("字段[" + fieldName + "]长度不能小于" + minLength);
                }
            }
            
            // 最大长度验证
            if (rules.containsKey("maxLength")) {
                int maxLength = (Integer) rules.get("maxLength");
                if (value.length() > maxLength) {
                    return FrResult.failed("字段[" + fieldName + "]长度不能大于" + maxLength);
                }
            }
            
            // 正则表达式验证
            if (rules.containsKey("pattern")) {
                String pattern = (String) rules.get("pattern");
                if (!value.matches(pattern)) {
                    return FrResult.failed("字段[" + fieldName + "]格式不正确");
                }
            }
            
            return FrResult.success();
        } catch (Exception e) {
            log.error("验证自定义规则失败", e);
            return FrResult.failed("验证失败：" + e.getMessage());
        }
    }
}