package org.simple.equipment.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.RandomUtil;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.CustomField;
import org.simple.equipment.mapper.CustomFieldMapper;
import org.simple.equipment.service.AssetFieldValueService;
import org.simple.equipment.service.CustomFieldService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备自定义字段配置服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class CustomFieldServiceImpl extends ServiceImpl<CustomFieldMapper, CustomField> implements CustomFieldService {

    @Resource
    private AssetFieldValueService assetFieldValueService;

    @Override
    public List<CustomField> getActiveFieldList() {
        return baseMapper.selectActiveList(AuthUtil.getTenantId());
    }

    @Override
    public List<CustomField> getFieldsByCategory(String categoryId, String typeId) {
        return baseMapper.selectByCategory(categoryId, typeId, AuthUtil.getTenantId());
    }

    @Override
    public List<CustomField> getCustomFieldList(CustomField customField) {
        return baseMapper.selectCustomFieldList(customField);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> saveCustomField(CustomField customField) {
        try {
            // 检查字段编码是否重复
            if (checkFieldCodeExists(customField.getFieldCode(), null)) {
                return FrResult.failed("字段编码已存在");
            }

            // 设置基础信息
            customField.setId(RandomUtil.getUserId());
            customField.setTenantId(AuthUtil.getTenantId());
            customField.setCreator(AuthUtil.getUserId());
            customField.setCreateDate(LocalDateTime.now());
            customField.setUpdateDate(LocalDateTime.now());
            
            // 设置默认值
            if (StrUtil.isBlank(customField.getIsRequired())) {
                customField.setIsRequired("0");
            }
            if (StrUtil.isBlank(customField.getIsEnabled())) {
                customField.setIsEnabled("1");
            }
            if (customField.getDisplayOrder() == null) {
                Integer maxOrder = baseMapper.selectMaxDisplayOrder(AuthUtil.getTenantId());
                customField.setDisplayOrder(maxOrder == null ? 1 : maxOrder + 1);
            }

            boolean success = save(customField);
            return success ? FrResult.successNodata("新增成功") : FrResult.failed("新增失败");
        } catch (Exception e) {
            log.error("新增自定义字段失败", e);
            return FrResult.failed("新增失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> updateCustomField(CustomField customField) {
        try {
            // 检查字段是否存在
            CustomField existField = getById(customField.getId());
            if (existField == null) {
                return FrResult.failed("字段不存在");
            }

            // 检查字段编码是否重复
            if (checkFieldCodeExists(customField.getFieldCode(), customField.getId())) {
                return FrResult.failed("字段编码已存在");
            }

            // 设置更新信息
            customField.setUpdater(AuthUtil.getUserId());
            customField.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(customField);
            return success ? FrResult.successNodata("修改成功") : FrResult.failed("修改失败");
        } catch (Exception e) {
            log.error("修改自定义字段失败", e);
            return FrResult.failed("修改失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> deleteCustomField(String id) {
        try {
            CustomField customField = getById(id);
            if (customField == null) {
                return FrResult.failed("字段不存在");
            }

            // 删除字段相关的值
            assetFieldValueService.deleteFieldValues(id);

            // 删除字段配置
            boolean success = removeById(id);
            return success ? FrResult.successNodata("删除成功") : FrResult.failed("删除失败");
        } catch (Exception e) {
            log.error("删除自定义字段失败", e);
            return FrResult.failed("删除失败：" + e.getMessage());
        }
    }

    @Override
    public boolean checkFieldCodeExists(String fieldCode, String excludeId) {
        CustomField customField = baseMapper.selectByFieldCode(fieldCode, AuthUtil.getTenantId(), excludeId);
        return customField != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> updateFieldStatus(String id, String isEnabled) {
        try {
            CustomField customField = getById(id);
            if (customField == null) {
                return FrResult.failed("字段不存在");
            }

            customField.setIsEnabled(isEnabled);
            customField.setUpdater(AuthUtil.getUserId());
            customField.setUpdateDate(LocalDateTime.now());

            boolean success = updateById(customField);
            return success ? FrResult.successNodata("状态更新成功") : FrResult.failed("状态更新失败");
        } catch (Exception e) {
            log.error("更新字段状态失败", e);
            return FrResult.failed("状态更新失败：" + e.getMessage());
        }
    }

    @Override
    public List<CustomField> getFieldsByGroup(String fieldGroup) {
        return baseMapper.selectByGroup(fieldGroup, AuthUtil.getTenantId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FrResult<?> copyFields(String fromCategoryId, String toCategoryId) {
        try {
            // 查询源分类的字段
            List<CustomField> sourceFields = baseMapper.selectByCategory(fromCategoryId, null, AuthUtil.getTenantId());
            
            if (sourceFields.isEmpty()) {
                return FrResult.failed("源分类没有自定义字段");
            }

            // 复制字段
            for (CustomField sourceField : sourceFields) {
                CustomField newField = new CustomField();
                newField.setId(RandomUtil.getUserId());
                newField.setFieldCode(sourceField.getFieldCode() + "_copy");
                newField.setFieldName(sourceField.getFieldName() + "_副本");
                newField.setFieldType(sourceField.getFieldType());
                newField.setCategoryId(toCategoryId);
                newField.setTypeId(sourceField.getTypeId());
                newField.setDefaultValue(sourceField.getDefaultValue());
                newField.setOptions(sourceField.getOptions());
                newField.setValidationRules(sourceField.getValidationRules());
                newField.setIsRequired(sourceField.getIsRequired());
                newField.setIsEnabled(sourceField.getIsEnabled());
                newField.setDisplayOrder(sourceField.getDisplayOrder());
                newField.setPlaceholder(sourceField.getPlaceholder());
                newField.setHelpText(sourceField.getHelpText());
                newField.setFieldGroup(sourceField.getFieldGroup());
                newField.setTenantId(AuthUtil.getTenantId());
                newField.setCreator(AuthUtil.getUserId());
                newField.setCreateDate(LocalDateTime.now());
                newField.setUpdateDate(LocalDateTime.now());

                save(newField);
            }

            return FrResult.successNodata("复制成功");
        } catch (Exception e) {
            log.error("复制字段失败", e);
            return FrResult.failed("复制失败：" + e.getMessage());
        }
    }
}