package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.SpecParam;

import java.util.List;
import java.util.Map;

/**
 * 设备规格参数定义服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface SpecParamService extends IService<SpecParam> {

    /**
     * 查询参数列表
     *
     * @param specParam 查询条件
     * @return 参数列表
     */
    List<SpecParam> getParamList(SpecParam specParam);

    /**
     * 根据参数组查询参数列表
     *
     * @param groupId 参数组ID
     * @return 参数列表
     */
    List<SpecParam> getParamsByGroupId(String groupId);

    /**
     * 根据设备分类和类型查询参数
     *
     * @param categoryId 分类ID
     * @param typeId 类型ID
     * @return 参数列表
     */
    List<SpecParam> getParamsByCategory(String categoryId, String typeId);

    /**
     * 查询可搜索的参数列表
     *
     * @return 参数列表
     */
    List<SpecParam> getSearchableParams();

    /**
     * 查询可比较的参数列表
     *
     * @return 参数列表
     */
    List<SpecParam> getComparableParams();

    /**
     * 查询参数详情
     *
     * @param paramId 参数ID
     * @return 参数详情
     */
    Map<String, Object> getParamDetail(String paramId);

    /**
     * 保存参数
     *
     * @param specParam 参数信息
     * @return 结果
     */
    FrResult<?> saveParam(SpecParam specParam);

    /**
     * 更新参数
     *
     * @param specParam 参数信息
     * @return 结果
     */
    FrResult<?> updateParam(SpecParam specParam);

    /**
     * 删除参数
     *
     * @param id 参数ID
     * @return 结果
     */
    FrResult<?> deleteParam(String id);

    /**
     * 检查参数编码是否存在
     *
     * @param paramCode 参数编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkParamCodeExists(String paramCode, String excludeId);

    /**
     * 更新参数状态
     *
     * @param id 参数ID
     * @param isEnabled 是否启用
     * @return 结果
     */
    FrResult<?> updateParamStatus(String id, String isEnabled);

    /**
     * 批量更新排序
     *
     * @param params 参数列表
     * @return 结果
     */
    FrResult<?> batchUpdateSort(List<SpecParam> params);

    /**
     * 复制参数
     *
     * @param id 源参数ID
     * @param newParamCode 新参数编码
     * @param newParamName 新参数名称
     * @param newGroupId 新分组ID
     * @return 结果
     */
    FrResult<?> copyParam(String id, String newParamCode, String newParamName, String newGroupId);

    /**
     * 查询参数使用统计
     *
     * @return 统计信息
     */
    List<Map<String, Object>> getUsageStatistics();

    /**
     * 验证参数值
     *
     * @param paramId 参数ID
     * @param paramValue 参数值
     * @return 验证结果
     */
    FrResult<?> validateParamValue(String paramId, String paramValue);
}