package org.simple.equipment.service;

import org.simple.equipment.dto.TemplateAssociationRequest;
import org.simple.equipment.dto.TemplateRecommendationResponse;

/**
 * 智能模板关联服务接口
 * 
 * <AUTHOR>
 */
public interface IntelligentTemplateService {

    /**
     * 根据设备智能推荐模板
     * 
     * @param equipmentId 设备ID
     * @param userId 用户ID
     * @return 推荐结果
     */
    TemplateRecommendationResponse recommendTemplate(String equipmentId, String userId);

    /**
     * 自动关联设备与模板
     * 
     * @param request 关联请求
     * @return 关联结果
     */
    Boolean autoAssociateTemplate(TemplateAssociationRequest request);

    /**
     * 更新模板关联规则
     * 
     * @param equipmentId 设备ID
     * @param templateId 模板ID
     * @param priority 优先级
     */
    void updateAssociationPriority(String equipmentId, String templateId, Integer priority);

    /**
     * 根据设备类型和历史数据智能推荐模板
     * 
     * @param equipmentType 设备类型
     * @param departmentId 科室ID
     * @return 推荐模板ID
     */
    String getRecommendedTemplateByType(String equipmentType, String departmentId);
}