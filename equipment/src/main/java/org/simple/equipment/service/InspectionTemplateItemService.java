package org.simple.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.InspectionTemplateItem;

import java.util.List;

/**
 * 设备点检模板项目服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface InspectionTemplateItemService extends IService<InspectionTemplateItem> {

    /**
     * 分页查询模板项目
     */
    IPage<InspectionTemplateItem> getItemPage(Page<InspectionTemplateItem> page, String templateId, String itemName,
                                               String itemType, String itemCategory);

    /**
     * 创建模板项目
     */
    boolean createItem(InspectionTemplateItem item);

    /**
     * 更新模板项目
     */
    boolean updateItem(InspectionTemplateItem item);

    /**
     * 删除模板项目
     */
    boolean deleteItem(String itemId);

    /**
     * 根据ID获取项目详情
     */
    InspectionTemplateItem getItemById(String itemId);

    /**
     * 根据模板ID查询项目
     */
    List<InspectionTemplateItem> getItemsByTemplate(String templateId);

    /**
     * 根据项目类型查询项目
     */
    List<InspectionTemplateItem> getItemsByType(String templateId, String itemType);

    /**
     * 查询关键项目
     */
    List<InspectionTemplateItem> getKeyItems(String templateId);

    /**
     * 检查项目编码是否存在
     */
    boolean checkItemCodeExists(String itemCode, String templateId, String excludeId);

    /**
     * 生成项目编码
     */
    String generateItemCode(String templateId, String itemType);

    /**
     * 批量保存项目
     */
    boolean batchSaveItems(String templateId, List<InspectionTemplateItem> items);

    /**
     * 批量删除项目
     */
    boolean batchDeleteItems(String templateId);

    /**
     * 更新项目排序
     */
    boolean updateItemSortOrder(String itemId, Integer sortOrder);

    /**
     * 复制模板项目
     */
    boolean copyTemplateItems(String sourceTemplateId, String targetTemplateId);

    /**
     * 验证项目数据
     */
    boolean validateItemData(InspectionTemplateItem item);

    /**
     * 获取项目统计信息
     */
    List<InspectionTemplateItem> getItemStatistics(String templateId);
}