package org.simple.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.simple.equipment.entity.MaintenancePlan;
import org.simple.equipment.entity.MaintenanceTask;
import org.simple.equipment.enums.MaintenanceStatus;
import org.simple.equipment.mapper.MaintenancePlanMapper;
import org.simple.equipment.service.MaintenancePlanService;
import org.simple.equipment.service.MaintenanceTaskService;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备保养计划服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Slf4j
@Service
public class MaintenancePlanServiceImpl extends ServiceImpl<MaintenancePlanMapper, MaintenancePlan> implements MaintenancePlanService {

    @Autowired
    private MaintenanceTaskService maintenanceTaskService;

    @Override
    public IPage<MaintenancePlan> selectMaintenancePlanPage(Page<MaintenancePlan> page, MaintenancePlan plan) {
        QueryWrapper<MaintenancePlan> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(plan.getPlanName())) {
            queryWrapper.like("plan_name", plan.getPlanName());
        }
        if (StringUtils.hasText(plan.getPlanCode())) {
            queryWrapper.like("plan_code", plan.getPlanCode());
        }
        if (StringUtils.hasText(plan.getEquipmentId())) {
            queryWrapper.eq("equipment_id", plan.getEquipmentId());
        }
        if (StringUtils.hasText(plan.getEquipmentName())) {
            queryWrapper.like("equipment_name", plan.getEquipmentName());
        }
        if (StringUtils.hasText(plan.getMaintenanceType())) {
            queryWrapper.eq("maintenance_type", plan.getMaintenanceType());
        }
        if (StringUtils.hasText(plan.getMaintenanceCycle())) {
            queryWrapper.eq("maintenance_cycle", plan.getMaintenanceCycle());
        }
        if (plan.getPlanStatus() != null) {
            queryWrapper.eq("plan_status", plan.getPlanStatus());
        }
        if (StringUtils.hasText(plan.getExecuteDeptId())) {
            queryWrapper.eq("execute_dept_id", plan.getExecuteDeptId());
        }
        if (StringUtils.hasText(plan.getResponsiblePersonId())) {
            queryWrapper.eq("responsible_person_id", plan.getResponsiblePersonId());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMaintenancePlan(MaintenancePlan plan) {
        try {
            // 生成计划编码
            if (!StringUtils.hasText(plan.getPlanCode())) {
                plan.setPlanCode(generatePlanCode(plan.getMaintenanceType()));
            }
            
            // 设置默认值
            if (plan.getPlanStatus() == null) {
                plan.setPlanStatus(0); // 默认为草稿状态
            }
            if (plan.getAutoGenerate() == null) {
                plan.setAutoGenerate(1); // 默认自动生成任务
            }
            if (plan.getEnabled() == null) {
                plan.setEnabled(1); // 默认启用
            }
            if (plan.getRemindHours() == null) {
                plan.setRemindHours(24); // 默认提前24小时提醒
            }
            
            // 计算下次执行时间
            if (plan.getPlanStatus() == 1) { // 生效状态
                plan.setNextExecutionTime(LocalDateTime.parse(calculateNextExecutionTime(plan)));
            }
            
            boolean result = this.save(plan);
            
            if (result) {
                log.info("保养计划创建成功，计划ID：{}", plan.getPlanId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("创建保养计划失败", e);
            throw new RuntimeException("创建保养计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMaintenancePlan(MaintenancePlan plan) {
        try {
            // 更新下次执行时间
            if (plan.getPlanStatus() != null && plan.getPlanStatus() == 1) {
                plan.setNextExecutionTime(LocalDateTime.parse(calculateNextExecutionTime(plan)));
            }
            
            boolean result = this.updateById(plan);
            
            if (result) {
                log.info("保养计划更新成功，计划ID：{}", plan.getPlanId());
            }
            
            return result;
        } catch (Exception e) {
            log.error("更新保养计划失败", e);
            throw new RuntimeException("更新保养计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMaintenancePlan(String planId) {
        try {
            // 检查是否有关联的任务
            QueryWrapper<MaintenanceTask> taskQueryWrapper = new QueryWrapper<>();
            taskQueryWrapper.eq("plan_id", planId);
            taskQueryWrapper.notIn("task_status", Arrays.asList(
                MaintenanceStatus.COMPLETED.getCode(),
                MaintenanceStatus.CANCELLED.getCode()
            ));
            
            long taskCount = maintenanceTaskService.count(taskQueryWrapper);
            if (taskCount > 0) {
                throw new RuntimeException("该计划存在未完成的保养任务，无法删除");
            }
            
            boolean result = this.removeById(planId);
            
            if (result) {
                log.info("保养计划删除成功，计划ID：{}", planId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("删除保养计划失败", e);
            throw new RuntimeException("删除保养计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlanStatus(String planId, Integer status) {
        try {
            MaintenancePlan plan = this.getById(planId);
            if (plan == null) {
                throw new RuntimeException("保养计划不存在");
            }
            
            plan.setPlanStatus(status);
            
            // 如果状态变为生效，计算下次执行时间
            if (status == 1) {
                plan.setNextExecutionTime(LocalDateTime.parse(calculateNextExecutionTime(plan)));
            }
            
            boolean result = this.updateById(plan);
            
            if (result) {
                log.info("保养计划状态更新成功，计划ID：{}，状态：{}", planId, status);
            }
            
            return result;
        } catch (Exception e) {
            log.error("更新保养计划状态失败", e);
            throw new RuntimeException("更新保养计划状态失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyMaintenancePlan(String planId, String newPlanName) {
        try {
            MaintenancePlan originalPlan = this.getById(planId);
            if (originalPlan == null) {
                throw new RuntimeException("原保养计划不存在");
            }
            
            // 复制计划
            MaintenancePlan newPlan = new MaintenancePlan();
            newPlan.setPlanName(newPlanName);
            newPlan.setPlanCode(generatePlanCode(originalPlan.getMaintenanceType()));
            newPlan.setEquipmentId(originalPlan.getEquipmentId());
            newPlan.setEquipmentName(originalPlan.getEquipmentName());
            newPlan.setEquipmentCode(originalPlan.getEquipmentCode());
            newPlan.setMaintenanceType(originalPlan.getMaintenanceType());
            newPlan.setMaintenanceCycle(originalPlan.getMaintenanceCycle());
            newPlan.setCycleValue(originalPlan.getCycleValue());
            newPlan.setCycleUnit(originalPlan.getCycleUnit());
            newPlan.setMaintenanceStandard(originalPlan.getMaintenanceStandard());
            newPlan.setMaintenanceContent(originalPlan.getMaintenanceContent());
            newPlan.setMaintenanceRequirements(originalPlan.getMaintenanceRequirements());
            newPlan.setRequiredHours(originalPlan.getRequiredHours());
            newPlan.setEstimatedCost(originalPlan.getEstimatedCost());
            newPlan.setResponsiblePersonId(originalPlan.getResponsiblePersonId());
            newPlan.setResponsiblePersonName(originalPlan.getResponsiblePersonName());
            newPlan.setExecuteDeptId(originalPlan.getExecuteDeptId());
            newPlan.setExecuteDeptName(originalPlan.getExecuteDeptName());
            newPlan.setPriority(originalPlan.getPriority());
            newPlan.setStartTime(originalPlan.getStartTime());
            newPlan.setEndTime(originalPlan.getEndTime());
            newPlan.setPlanStatus(0); // 复制的计划默认为草稿状态
            newPlan.setAutoGenerate(originalPlan.getAutoGenerate());
            newPlan.setRemindHours(originalPlan.getRemindHours());
            newPlan.setSkipHolidays(originalPlan.getSkipHolidays());
            newPlan.setExecutionRule(originalPlan.getExecutionRule());
            newPlan.setExecutionCount(0); // 重置执行次数
            newPlan.setNotes(originalPlan.getNotes());
            newPlan.setEnabled(1); // 默认启用
            newPlan.setSortOrder(originalPlan.getSortOrder());
            newPlan.setTenantId(originalPlan.getTenantId());
            
            boolean result = this.save(newPlan);
            
            if (result) {
                log.info("保养计划复制成功，原计划ID：{}，新计划ID：{}", planId, newPlan.getPlanId());
                return newPlan.getPlanId();
            }
            
            throw new RuntimeException("复制保养计划失败");
        } catch (Exception e) {
            log.error("复制保养计划失败", e);
            throw new RuntimeException("复制保养计划失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int generateTasksByPlan(String planId) {
        try {
            MaintenancePlan plan = this.getById(planId);
            if (plan == null) {
                throw new RuntimeException("保养计划不存在");
            }
            
            if (plan.getPlanStatus() != 1) {
                throw new RuntimeException("只有生效状态的计划才能生成任务");
            }
            
            // 生成任务时间列表
            List<LocalDateTime> taskTimes = calculateTaskTimes(plan);
            
            int generatedCount = 0;
            for (LocalDateTime taskTime : taskTimes) {
                // 检查是否已存在相同时间的任务
                QueryWrapper<MaintenanceTask> taskQueryWrapper = new QueryWrapper<>();
                taskQueryWrapper.eq("plan_id", planId);
                taskQueryWrapper.eq("scheduled_start_time", taskTime);
                
                long existingTaskCount = maintenanceTaskService.count(taskQueryWrapper);
                if (existingTaskCount > 0) {
                    continue; // 跳过已存在的任务
                }
                
                // 创建保养任务
                MaintenanceTask task = new MaintenanceTask();
                task.setTaskNumber(generateTaskNumber(plan.getMaintenanceType()));
                task.setTaskName(plan.getPlanName() + "-" + taskTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                task.setPlanId(planId);
                task.setPlanName(plan.getPlanName());
                task.setEquipmentId(plan.getEquipmentId());
                task.setEquipmentName(plan.getEquipmentName());
                task.setEquipmentCode(plan.getEquipmentCode());
                task.setMaintenanceType(plan.getMaintenanceType());
                task.setMaintenanceContent(plan.getMaintenanceContent());
                task.setMaintenanceRequirements(plan.getMaintenanceRequirements());
                task.setScheduledStartTime(taskTime);
                task.setScheduledEndTime(taskTime.plusHours(plan.getRequiredHours().longValue()));
                task.setRequiredHours(plan.getRequiredHours());
                task.setEstimatedCost(plan.getEstimatedCost());
                task.setExecuteDeptId(plan.getExecuteDeptId());
                task.setExecuteDeptName(plan.getExecuteDeptName());
                task.setTaskStatus(MaintenanceStatus.PLANNED.getCode());
                task.setPriority(plan.getPriority());
                task.setCompletionPercentage(java.math.BigDecimal.ZERO);
                task.setIsUrgent(0);
                task.setIsOutsourced(0);
                task.setTenantId(plan.getTenantId());
                
                boolean taskResult = maintenanceTaskService.save(task);
                if (taskResult) {
                    generatedCount++;
                }
            }
            
            // 更新计划的执行次数和下次执行时间
            if (generatedCount > 0) {
                plan.setExecutionCount(plan.getExecutionCount() + generatedCount);
                plan.setLastExecutionTime(LocalDateTime.now());
                plan.setNextExecutionTime(LocalDateTime.parse(calculateNextExecutionTime(plan)));
                this.updateById(plan);
                
                log.info("保养计划生成任务成功，计划ID：{}，生成任务数：{}", planId, generatedCount);
            }
            
            return generatedCount;
        } catch (Exception e) {
            log.error("生成保养任务失败", e);
            throw new RuntimeException("生成保养任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateTasks(List<String> planIds) {
        Map<String, Object> result = new HashMap<>();
        int totalSuccess = 0;
        int totalFail = 0;
        List<String> failedPlans = new ArrayList<>();
        
        for (String planId : planIds) {
            try {
                int count = generateTasksByPlan(planId);
                if (count > 0) {
                    totalSuccess += count;
                } else {
                    totalFail++;
                    failedPlans.add(planId);
                }
            } catch (Exception e) {
                totalFail++;
                failedPlans.add(planId);
                log.error("批量生成任务失败，计划ID：{}", planId, e);
            }
        }
        
        result.put("successCount", totalSuccess);
        result.put("failCount", totalFail);
        result.put("failedPlans", failedPlans);
        
        return result;
    }

    @Override
    public Map<String, Object> getMaintenancePlanStatistics() {
        try {
            Map<String, Object> statistics = baseMapper.selectPlanStatistics();
            
            // 获取更详细的统计信息
            List<Map<String, Object>> typeStatistics = baseMapper.selectPlanStatisticsByType();
            List<Map<String, Object>> cycleStatistics = baseMapper.selectPlanStatisticsByCycle();
            List<Map<String, Object>> equipmentTypeStatistics = baseMapper.selectPlanStatisticsByEquipmentType();
            List<Map<String, Object>> deptStatistics = baseMapper.selectPlanStatisticsByDept();
            
            statistics.put("typeStatistics", typeStatistics);
            statistics.put("cycleStatistics", cycleStatistics);
            statistics.put("equipmentTypeStatistics", equipmentTypeStatistics);
            statistics.put("deptStatistics", deptStatistics);
            
            return statistics;
        } catch (Exception e) {
            log.error("获取保养计划统计信息失败", e);
            throw new RuntimeException("获取保养计划统计信息失败：" + e.getMessage());
        }
    }

    @Override
    public List<MaintenancePlan> getExpiringPlans(Integer days) {
        try {
            LocalDateTime expireTime = LocalDateTime.now().plusDays(days);
            return baseMapper.selectExpiringPlans(expireTime);
        } catch (Exception e) {
            log.error("获取即将到期的保养计划失败", e);
            throw new RuntimeException("获取即将到期的保养计划失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> validatePlanConfiguration(MaintenancePlan plan) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 验证必填字段
        if (!StringUtils.hasText(plan.getPlanName())) {
            errors.add("计划名称不能为空");
        }
        if (!StringUtils.hasText(plan.getEquipmentId())) {
            errors.add("设备ID不能为空");
        }
        if (!StringUtils.hasText(plan.getMaintenanceType())) {
            errors.add("保养类型不能为空");
        }
        if (!StringUtils.hasText(plan.getMaintenanceCycle())) {
            errors.add("保养周期不能为空");
        }
        
        // 验证时间配置
        if (plan.getStartTime() != null && plan.getEndTime() != null) {
            if (plan.getStartTime().isAfter(plan.getEndTime())) {
                errors.add("开始时间不能晚于结束时间");
            }
        }
        
        if (plan.getEffectiveDate() != null && plan.getExpiryDate() != null) {
            if (plan.getEffectiveDate().isAfter(plan.getExpiryDate())) {
                errors.add("生效日期不能晚于失效日期");
            }
        }
        
        // 验证周期配置
        if (plan.getCycleValue() != null && plan.getCycleValue() <= 0) {
            errors.add("周期值必须大于0");
        }
        
        // 验证工时和费用
        if (plan.getRequiredHours() != null && plan.getRequiredHours().compareTo(java.math.BigDecimal.ZERO) < 0) {
            errors.add("所需工时不能为负数");
        }
        
        if (plan.getEstimatedCost() != null && plan.getEstimatedCost().compareTo(java.math.BigDecimal.ZERO) < 0) {
            errors.add("预计费用不能为负数");
        }
        
        // 生成警告
        if (plan.getRequiredHours() != null && plan.getRequiredHours().compareTo(java.math.BigDecimal.valueOf(24)) > 0) {
            warnings.add("所需工时超过24小时，请确认是否正确");
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        
        return result;
    }

    @Override
    public Map<String, Object> getPlanExecutionStatus(String planId, Integer days) {
        try {
            LocalDateTime startTime = LocalDateTime.now().minusDays(days);
            LocalDateTime endTime = LocalDateTime.now();
            
            return baseMapper.selectPlanExecutionStatus(planId, startTime, endTime);
        } catch (Exception e) {
            log.error("获取保养计划执行情况失败", e);
            throw new RuntimeException("获取保养计划执行情况失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> importMaintenancePlans(List<Map<String, Object>> planData) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();
        
        for (Map<String, Object> data : planData) {
            try {
                MaintenancePlan plan = convertMapToPlan(data);
                boolean success = createMaintenancePlan(plan);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                    errors.add("导入计划失败：" + data.get("planName"));
                }
            } catch (Exception e) {
                failCount++;
                errors.add("导入计划失败：" + data.get("planName") + "，错误：" + e.getMessage());
            }
        }
        
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        
        return result;
    }

    @Override
    public List<Map<String, Object>> exportMaintenancePlans(List<String> planIds) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        QueryWrapper<MaintenancePlan> queryWrapper = new QueryWrapper<>();
        if (planIds != null && !planIds.isEmpty()) {
            queryWrapper.in("plan_id", planIds);
        }
        
        List<MaintenancePlan> plans = this.list(queryWrapper);
        
        for (MaintenancePlan plan : plans) {
            Map<String, Object> planData = convertPlanToMap(plan);
            result.add(planData);
        }
        
        return result;
    }

    @Override
    public List<MaintenancePlan> getEquipmentMaintenancePlans(String equipmentId) {
        try {
            return baseMapper.selectPlansByEquipment(equipmentId);
        } catch (Exception e) {
            log.error("获取设备保养计划失败", e);
            throw new RuntimeException("获取设备保养计划失败：" + e.getMessage());
        }
    }

    @Override
    public List<MaintenancePlan> getDeptMaintenancePlans(String deptId) {
        try {
            return baseMapper.selectPlansByDept(deptId);
        } catch (Exception e) {
            log.error("获取部门保养计划失败", e);
            throw new RuntimeException("获取部门保养计划失败：" + e.getMessage());
        }
    }

    @Override
    public String calculateNextExecutionTime(MaintenancePlan plan) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextTime = now;
        
        // 根据保养周期计算下次执行时间
        switch (plan.getMaintenanceCycle()) {
            case "DAILY":
                nextTime = now.plusDays(plan.getCycleValue());
                break;
            case "WEEKLY":
                nextTime = now.plusWeeks(plan.getCycleValue());
                break;
            case "MONTHLY":
                nextTime = now.plusMonths(plan.getCycleValue());
                break;
            case "QUARTERLY":
                nextTime = now.plusMonths(plan.getCycleValue() * 3);
                break;
            case "HALF_YEARLY":
                nextTime = now.plusMonths(plan.getCycleValue() * 6);
                break;
            case "YEARLY":
                nextTime = now.plusYears(plan.getCycleValue());
                break;
            default:
                nextTime = now.plusDays(plan.getCycleValue());
        }
        
        // 设置执行时间
        if (plan.getStartTime() != null) {
            nextTime = nextTime.with(plan.getStartTime());
        }
        
        return nextTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    public boolean updatePlanExecutionCount(String planId) {
        try {
            MaintenancePlan plan = this.getById(planId);
            if (plan != null) {
                plan.setExecutionCount(plan.getExecutionCount() + 1);
                plan.setLastExecutionTime(LocalDateTime.now());
                return this.updateById(plan);
            }
            return false;
        } catch (Exception e) {
            log.error("更新保养计划执行次数失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getPlanTaskStatistics(String planId, Integer days) {
        try {
            LocalDateTime startTime = LocalDateTime.now().minusDays(days);
            LocalDateTime endTime = LocalDateTime.now();
            
            return baseMapper.selectPlanTaskStatistics(planId, startTime, endTime);
        } catch (Exception e) {
            log.error("获取保养计划任务统计失败", e);
            throw new RuntimeException("获取保养计划任务统计失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getPlanCostStatistics(String planId, Integer days) {
        try {
            LocalDateTime startTime = LocalDateTime.now().minusDays(days);
            LocalDateTime endTime = LocalDateTime.now();
            
            return baseMapper.selectPlanCostStatistics(planId, startTime, endTime);
        } catch (Exception e) {
            log.error("获取保养计划费用统计失败", e);
            throw new RuntimeException("获取保养计划费用统计失败：" + e.getMessage());
        }
    }

    @Override
    public boolean enableMaintenancePlan(String planId, Integer enabled) {
        try {
            MaintenancePlan plan = this.getById(planId);
            if (plan != null) {
                plan.setEnabled(enabled);
                return this.updateById(plan);
            }
            return false;
        } catch (Exception e) {
            log.error("启用/禁用保养计划失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getMaintenancePlanDetails(String planId) {
        try {
            return baseMapper.selectPlanDetails(planId);
        } catch (Exception e) {
            log.error("获取保养计划详情失败", e);
            throw new RuntimeException("获取保养计划详情失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> checkPlanConflict(MaintenancePlan plan) {
        Map<String, Object> result = new HashMap<>();
        List<String> conflicts = new ArrayList<>();
        
        // 检查同设备同时间段的计划冲突
        QueryWrapper<MaintenancePlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equipment_id", plan.getEquipmentId());
        queryWrapper.eq("plan_status", 1); // 生效状态
        queryWrapper.ne("plan_id", plan.getPlanId()); // 排除自己
        
        List<MaintenancePlan> existingPlans = this.list(queryWrapper);
        
        for (MaintenancePlan existingPlan : existingPlans) {
            if (isTimeConflict(plan, existingPlan)) {
                conflicts.add("与计划《" + existingPlan.getPlanName() + "》存在时间冲突");
            }
        }
        
        result.put("hasConflict", !conflicts.isEmpty());
        result.put("conflicts", conflicts);
        
        return result;
    }

    @Override
    public Map<String, Object> autoGenerateMaintenanceTasks() {
        Map<String, Object> result = new HashMap<>();
        int totalGenerated = 0;
        int totalFailed = 0;
        
        try {
            // 获取需要生成任务的计划
            LocalDateTime currentTime = LocalDateTime.now();
            List<MaintenancePlan> plans = baseMapper.selectPlansForTaskGeneration(currentTime);
            
            for (MaintenancePlan plan : plans) {
                try {
                    int count = generateTasksByPlan(plan.getPlanId());
                    totalGenerated += count;
                } catch (Exception e) {
                    totalFailed++;
                    log.error("自动生成保养任务失败，计划ID：{}", plan.getPlanId(), e);
                }
            }
            
            result.put("totalGenerated", totalGenerated);
            result.put("totalFailed", totalFailed);
            result.put("processedPlans", plans.size());
            
        } catch (Exception e) {
            log.error("自动生成保养任务失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    // 私有方法

    private String generatePlanCode(String maintenanceType) {
        String prefix = "MP";
        switch (maintenanceType) {
            case "DAILY":
                prefix = "MP_DAILY";
                break;
            case "LEVEL_ONE":
                prefix = "MP_L1";
                break;
            case "LEVEL_TWO":
                prefix = "MP_L2";
                break;
            case "LEVEL_THREE":
                prefix = "MP_L3";
                break;
            case "SPECIAL":
                prefix = "MP_SPECIAL";
                break;
            case "SEASONAL":
                prefix = "MP_SEASONAL";
                break;
            case "PREVENTIVE":
                prefix = "MP_PREVENTIVE";
                break;
        }
        
        return prefix + "_" + System.currentTimeMillis();
    }

    private String generateTaskNumber(String maintenanceType) {
        String prefix = "MT";
        switch (maintenanceType) {
            case "DAILY":
                prefix = "MT_DAILY";
                break;
            case "LEVEL_ONE":
                prefix = "MT_L1";
                break;
            case "LEVEL_TWO":
                prefix = "MT_L2";
                break;
            case "LEVEL_THREE":
                prefix = "MT_L3";
                break;
            case "SPECIAL":
                prefix = "MT_SPECIAL";
                break;
            case "SEASONAL":
                prefix = "MT_SEASONAL";
                break;
            case "PREVENTIVE":
                prefix = "MT_PREVENTIVE";
                break;
        }
        
        return prefix + "_" + System.currentTimeMillis();
    }

    private List<LocalDateTime> calculateTaskTimes(MaintenancePlan plan) {
        List<LocalDateTime> taskTimes = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 根据计划生成未来7天的任务时间
        for (int i = 0; i < 7; i++) {
            LocalDateTime taskTime = now.plusDays(i);
            
            // 根据保养周期判断是否需要生成任务
            if (shouldGenerateTask(plan, taskTime)) {
                if (plan.getStartTime() != null) {
                    taskTime = taskTime.with(plan.getStartTime());
                }
                taskTimes.add(taskTime);
            }
        }
        
        return taskTimes;
    }

    private boolean shouldGenerateTask(MaintenancePlan plan, LocalDateTime taskTime) {
        // 这里可以根据计划的执行规则判断是否需要生成任务
        // 简化处理，根据周期判断
        LocalDateTime lastExecution = plan.getLastExecutionTime();
        if (lastExecution == null) {
            return true; // 首次执行
        }
        
        switch (plan.getMaintenanceCycle()) {
            case "DAILY":
                return taskTime.isAfter(lastExecution.plusDays(plan.getCycleValue()));
            case "WEEKLY":
                return taskTime.isAfter(lastExecution.plusWeeks(plan.getCycleValue()));
            case "MONTHLY":
                return taskTime.isAfter(lastExecution.plusMonths(plan.getCycleValue()));
            default:
                return false;
        }
    }

    private boolean isTimeConflict(MaintenancePlan plan1, MaintenancePlan plan2) {
        // 简化处理，检查执行时间是否冲突
        if (plan1.getStartTime() != null && plan1.getEndTime() != null &&
            plan2.getStartTime() != null && plan2.getEndTime() != null) {
            
            return !(plan1.getEndTime().isBefore(plan2.getStartTime()) ||
                     plan1.getStartTime().isAfter(plan2.getEndTime()));
        }
        
        return false;
    }

    private MaintenancePlan convertMapToPlan(Map<String, Object> data) {
        MaintenancePlan plan = new MaintenancePlan();
        plan.setPlanName((String) data.get("planName"));
        plan.setEquipmentId((String) data.get("equipmentId"));
        plan.setEquipmentName((String) data.get("equipmentName"));
        plan.setMaintenanceType((String) data.get("maintenanceType"));
        plan.setMaintenanceCycle((String) data.get("maintenanceCycle"));
        // 其他字段转换...
        return plan;
    }

    private Map<String, Object> convertPlanToMap(MaintenancePlan plan) {
        Map<String, Object> data = new HashMap<>();
        data.put("planId", plan.getPlanId());
        data.put("planName", plan.getPlanName());
        data.put("planCode", plan.getPlanCode());
        data.put("equipmentId", plan.getEquipmentId());
        data.put("equipmentName", plan.getEquipmentName());
        data.put("maintenanceType", plan.getMaintenanceType());
        data.put("maintenanceCycle", plan.getMaintenanceCycle());
        // 其他字段转换...
        return data;
    }
}