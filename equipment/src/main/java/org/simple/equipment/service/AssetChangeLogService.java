package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.AssetChangeLog;

import java.util.List;
import java.util.Map;

/**
 * 设备档案变更记录服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AssetChangeLogService extends IService<AssetChangeLog> {

    /**
     * 查询设备的变更记录
     *
     * @param assetId 设备ID
     * @return 变更记录列表
     */
    List<AssetChangeLog> getChangeLogsByAssetId(String assetId);

    /**
     * 查询版本的变更记录
     *
     * @param versionId 版本ID
     * @return 变更记录列表
     */
    List<AssetChangeLog> getChangeLogsByVersionId(String versionId);

    /**
     * 查询变更统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getChangeStatistics();
}