package org.simple.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.base.util.IdUtil;
import org.simple.equipment.entity.Task;
import org.simple.equipment.mapper.TaskMapper;
import org.simple.equipment.service.TaskService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class TaskServiceImpl extends ServiceImpl<TaskMapper, Task> implements TaskService {

    @Override
    @Transactional
    public boolean createTask(Task task) {
        // 检查任务编码是否存在
        if (checkTaskCodeExists(task.getTaskCode(), null)) {
            throw new RuntimeException("任务编码已存在");
        }

        // 设置基本信息
        task.setId(IdUtil.getId());
        task.setTenantId(AuthUtil.getTenantId());
        task.setCreateDate(LocalDateTime.now());
        task.setCreator(AuthUtil.getUserId());
        task.setTaskStatus("PENDING"); // 默认待执行状态

        // 如果没有指定任务编码，自动生成
        if (task.getTaskCode() == null || task.getTaskCode().trim().isEmpty()) {
            task.setTaskCode(generateTaskCode(task.getTaskTypeId()));
        }

        return save(task);
    }

    @Override
    @Transactional
    public boolean updateTask(Task task) {
        // 检查任务编码是否存在
        if (checkTaskCodeExists(task.getTaskCode(), task.getId())) {
            throw new RuntimeException("任务编码已存在");
        }

        // 设置更新信息
        task.setUpdateDate(LocalDateTime.now());
        task.setUpdater(AuthUtil.getUserId());
        task.setTenantId(AuthUtil.getTenantId());

        return updateById(task);
    }

    @Override
    @Transactional
    public boolean deleteTask(String id) {
        // 检查任务状态
        Task task = getById(id);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        if ("PROCESSING".equals(task.getTaskStatus())) {
            throw new RuntimeException("执行中的任务不能删除");
        }

        return removeById(id);
    }

    @Override
    @Transactional
    public boolean assignTask(String taskId, String userId, String userName) {
        Task task = new Task();
        task.setId(taskId);
        task.setAssignedUserId(userId);
        task.setAssignedUserName(userName);
        task.setUpdateDate(LocalDateTime.now());
        task.setUpdater(AuthUtil.getUserId());
        return updateById(task);
    }

    @Override
    @Transactional
    public boolean startTask(String taskId) {
        Task task = getById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        if (!"PENDING".equals(task.getTaskStatus())) {
            throw new RuntimeException("只有待执行状态的任务才能开始");
        }

        task.setTaskStatus("PROCESSING");
        task.setActualStartTime(LocalDateTime.now());
        task.setUpdateDate(LocalDateTime.now());
        task.setUpdater(AuthUtil.getUserId());
        return updateById(task);
    }

    @Override
    @Transactional
    public boolean completeTask(String taskId, String result) {
        Task task = getById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        if (!"PROCESSING".equals(task.getTaskStatus())) {
            throw new RuntimeException("只有执行中的任务才能完成");
        }

        task.setTaskStatus("COMPLETED");
        task.setActualEndTime(LocalDateTime.now());
        task.setResult(result);
        task.setUpdateDate(LocalDateTime.now());
        task.setUpdater(AuthUtil.getUserId());
        return updateById(task);
    }

    @Override
    @Transactional
    public boolean cancelTask(String taskId, String reason) {
        Task task = getById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }
        if ("COMPLETED".equals(task.getTaskStatus())) {
            throw new RuntimeException("已完成的任务不能取消");
        }

        task.setTaskStatus("CANCELLED");
        task.setRemark(reason);
        task.setUpdateDate(LocalDateTime.now());
        task.setUpdater(AuthUtil.getUserId());
        return updateById(task);
    }

    @Override
    public Map<String, Object> getTaskStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 基本统计
        QueryWrapper<Task> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", AuthUtil.getTenantId());
        
        statistics.put("totalTasks", count(queryWrapper));
        
        // 按状态统计
        List<Map<String, Object>> statusStats = baseMapper.getTaskStatistics(AuthUtil.getTenantId());
        statistics.put("statusStats", statusStats);
        
        // 逾期任务数量
        List<Task> overdueTasks = getOverdueTasks();
        statistics.put("overdueTasks", overdueTasks.size());
        
        return statistics;
    }

    @Override
    public List<Task> getOverdueTasks() {
        return baseMapper.getOverdueTasks(AuthUtil.getTenantId());
    }

    @Override
    public List<Task> getUserTasks(String userId) {
        return baseMapper.getUserTasks(userId, AuthUtil.getTenantId());
    }

    @Override
    public List<Task> getEquipmentTasks(String equipmentId) {
        return baseMapper.getEquipmentTasks(equipmentId, AuthUtil.getTenantId());
    }

    @Override
    public boolean checkTaskCodeExists(String taskCode, String excludeId) {
        QueryWrapper<Task> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_code", taskCode)
                .eq("tenant_id", AuthUtil.getTenantId());
        if (excludeId != null) {
            queryWrapper.ne("id", excludeId);
        }
        return count(queryWrapper) > 0;
    }

    @Override
    public String generateTaskCode(String taskTypeId) {
        // 生成格式：TASK + 任务类型简码 + 日期 + 序号
        // 例如：TASK-REPAIR-20240101-001
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 查询当天同类型任务数量
        QueryWrapper<Task> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_type_id", taskTypeId)
                .eq("tenant_id", AuthUtil.getTenantId())
                .likeRight("task_code", "TASK-" + dateStr);
        
        long count = count(queryWrapper);
        String sequence = String.format("%03d", count + 1);
        
        return "TASK-" + dateStr + "-" + sequence;
    }

    @Override
    public void checkAndUpdateOverdueTasks() {
        // 查询计划结束时间已过且状态为待执行或执行中的任务
        QueryWrapper<Task> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("planned_end_time", LocalDateTime.now())
                .in("task_status", "PENDING", "PROCESSING")
                .eq("tenant_id", AuthUtil.getTenantId());
        
        List<Task> tasks = list(queryWrapper);
        for (Task task : tasks) {
            task.setTaskStatus("OVERDUE");
            task.setUpdateDate(LocalDateTime.now());
            updateById(task);
        }
        
        log.info("检查并更新逾期任务，共更新{}个任务", tasks.size());
    }

    @Override
    public void sendTaskReminder(String taskId) {
        // TODO: 实现任务提醒功能
        // 可以集成邮件、短信、系统内通知等
        log.info("发送任务提醒：{}", taskId);
    }
}