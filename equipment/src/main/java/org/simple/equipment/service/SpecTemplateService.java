package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.SpecTemplate;

import java.util.List;
import java.util.Map;

/**
 * 设备规格参数模板服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface SpecTemplateService extends IService<SpecTemplate> {

    /**
     * 查询模板列表
     *
     * @param specTemplate 查询条件
     * @return 模板列表
     */
    List<SpecTemplate> getTemplateList(SpecTemplate specTemplate);

    /**
     * 根据分类和类型查询模板
     *
     * @param categoryId 分类ID
     * @param typeId 类型ID
     * @return 模板列表
     */
    List<SpecTemplate> getTemplatesByCategory(String categoryId, String typeId);

    /**
     * 查询有效的模板列表
     *
     * @return 模板列表
     */
    List<SpecTemplate> getEnabledTemplateList();

    /**
     * 查询模板详情
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    Map<String, Object> getTemplateDetail(String templateId);

    /**
     * 保存模板
     *
     * @param specTemplate 模板信息
     * @return 结果
     */
    FrResult<?> saveTemplate(SpecTemplate specTemplate);

    /**
     * 更新模板
     *
     * @param specTemplate 模板信息
     * @return 结果
     */
    FrResult<?> updateTemplate(SpecTemplate specTemplate);

    /**
     * 删除模板
     *
     * @param id 模板ID
     * @return 结果
     */
    FrResult<?> deleteTemplate(String id);

    /**
     * 检查模板编码是否存在
     *
     * @param templateCode 模板编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkTemplateCodeExists(String templateCode, String excludeId);

    /**
     * 更新模板状态
     *
     * @param id 模板ID
     * @param isEnabled 是否启用
     * @return 结果
     */
    FrResult<?> updateTemplateStatus(String id, String isEnabled);

    /**
     * 更新模板使用次数
     *
     * @param templateId 模板ID
     * @return 结果
     */
    FrResult<?> updateUsageCount(String templateId);

    /**
     * 查询热门模板
     *
     * @param limit 限制数量
     * @return 热门模板列表
     */
    List<SpecTemplate> getPopularTemplates(int limit);

    /**
     * 查询模板使用统计
     *
     * @return 统计信息
     */
    List<Map<String, Object>> getUsageStatistics();

    /**
     * 复制模板
     *
     * @param id 源模板ID
     * @param newTemplateCode 新模板编码
     * @param newTemplateName 新模板名称
     * @return 结果
     */
    FrResult<?> copyTemplate(String id, String newTemplateCode, String newTemplateName);

    /**
     * 从设备创建模板
     *
     * @param assetId 设备ID
     * @param templateCode 模板编码
     * @param templateName 模板名称
     * @param templateDesc 模板描述
     * @return 结果
     */
    FrResult<?> createTemplateFromAsset(String assetId, String templateCode, String templateName, String templateDesc);

    /**
     * 导出模板
     *
     * @param templateId 模板ID
     * @return 导出数据
     */
    Map<String, Object> exportTemplate(String templateId);

    /**
     * 导入模板
     *
     * @param templateData 模板数据
     * @return 结果
     */
    FrResult<?> importTemplate(Map<String, Object> templateData);
}