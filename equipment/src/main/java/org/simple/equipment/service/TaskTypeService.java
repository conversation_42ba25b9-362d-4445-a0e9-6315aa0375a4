package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.TaskType;

import java.util.List;

/**
 * 任务类型服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface TaskTypeService extends IService<TaskType> {

    /**
     * 获取启用的任务类型列表
     *
     * @return 任务类型列表
     */
    List<TaskType> getEnabledTaskTypes();

    /**
     * 检查任务类型编码是否存在
     *
     * @param typeCode 任务类型编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkTypeCodeExists(String typeCode, String excludeId);

    /**
     * 创建任务类型
     *
     * @param taskType 任务类型信息
     * @return 创建结果
     */
    boolean createTaskType(TaskType taskType);

    /**
     * 更新任务类型
     *
     * @param taskType 任务类型信息
     * @return 更新结果
     */
    boolean updateTaskType(TaskType taskType);

    /**
     * 删除任务类型
     *
     * @param id 任务类型ID
     * @return 删除结果
     */
    boolean deleteTaskType(String id);

    /**
     * 切换任务类型状态
     *
     * @param id 任务类型ID
     * @param status 状态
     * @return 切换结果
     */
    boolean toggleStatus(String id, String status);
}