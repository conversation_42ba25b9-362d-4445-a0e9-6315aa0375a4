package org.simple.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simple.equipment.dto.QrCodeGenerateRequest;
import org.simple.equipment.dto.QrCodeScanRequest;
import org.simple.equipment.dto.QrCodeScanResponse;
import org.simple.equipment.dto.TemplateRecommendationResponse;
import org.simple.equipment.entity.EquipmentQrCode;
import org.simple.equipment.entity.EquipmentScanRecord;
import org.simple.equipment.entity.EquipmentTemplateAssociation;
import org.simple.equipment.mapper.EquipmentQrCodeMapper;
import org.simple.equipment.service.EquipmentQrCodeService;
import org.simple.equipment.service.EquipmentScanRecordService;
import org.simple.equipment.service.EquipmentTemplateAssociationService;
import org.simple.equipment.service.IntelligentTemplateService;
import org.simple.equipment.util.QrCodeGenerator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 设备二维码服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EquipmentQrCodeServiceImpl extends ServiceImpl<EquipmentQrCodeMapper, EquipmentQrCode> 
        implements EquipmentQrCodeService {

    private final EquipmentScanRecordService scanRecordService;
    private final EquipmentTemplateAssociationService templateAssociationService;
    private final IntelligentTemplateService intelligentTemplateService;
    private final QrCodeGenerator qrCodeGenerator;

    @Override
    @Transactional
    public EquipmentQrCode generateEquipmentQrCode(QrCodeGenerateRequest request) {
        log.info("生成设备二维码: {}", request.getEquipmentId());
        
        // 检查是否已存在二维码
        EquipmentQrCode existingQrCode = getQrCodeByEquipmentId(request.getEquipmentId());
        if (existingQrCode != null && existingQrCode.getIsActive()) {
            log.info("设备{}已存在活跃的二维码", request.getEquipmentId());
            return existingQrCode;
        }

        // 生成唯一ID
        String qrCodeId = UUID.randomUUID().toString();
        
        // 构建二维码数据
        Map<String, Object> qrCodeData = new HashMap<>();
        qrCodeData.put("id", qrCodeId);
        qrCodeData.put("equipmentId", request.getEquipmentId());
        qrCodeData.put("type", request.getQrType().name());
        qrCodeData.put("timestamp", System.currentTimeMillis());
        
        // 添加自定义数据
        if (request.getCustomData() != null) {
            qrCodeData.putAll(request.getCustomData());
        }

        // 创建二维码实体
        EquipmentQrCode qrCode = new EquipmentQrCode();
        qrCode.setId(qrCodeId);
        qrCode.setEquipmentId(request.getEquipmentId());
        qrCode.setQrCodeData(qrCodeData);
        qrCode.setQrType(request.getQrType());
        qrCode.setGeneratedAt(LocalDateTime.now());
        qrCode.setExpiresAt(request.getExpiresAt());
        qrCode.setIsActive(true);
        qrCode.setScanCount(0);

        // 生成二维码图片
        if (request.getGenerateImage()) {
            try {
                String qrCodeDataStr = qrCodeGenerator.encodeQrCodeData(qrCodeData);
                String imageUrl = qrCodeGenerator.generateQrCodeImage(
                    qrCodeDataStr, 
                    request.getImageSize() != null ? request.getImageSize() : 200
                );
                qrCode.setQrCodeImageUrl(imageUrl);
            } catch (Exception e) {
                log.error("生成二维码图片失败: {}", e.getMessage(), e);
                // 不影响二维码数据的保存
            }
        }

        // 保存到数据库
        save(qrCode);
        
        log.info("设备二维码生成成功: {}", qrCodeId);
        return qrCode;
    }

    @Override
    @Transactional
    public QrCodeScanResponse scanQrCode(QrCodeScanRequest request) {
        log.info("扫描二维码: {}", request.getQrCodeData());
        
        try {
            // 解析二维码数据
            Map<String, Object> qrData = qrCodeGenerator.decodeQrCodeData(request.getQrCodeData());
            String qrCodeId = (String) qrData.get("id");
            String equipmentId = (String) qrData.get("equipmentId");
            
            if (qrCodeId == null || equipmentId == null) {
                return QrCodeScanResponse.invalid();
            }

            // 查找二维码记录
            EquipmentQrCode qrCode = getById(qrCodeId);
            if (qrCode == null || !qrCode.getIsActive()) {
                return QrCodeScanResponse.invalid();
            }

            // 检查是否过期
            if (qrCode.getExpiresAt() != null && LocalDateTime.now().isAfter(qrCode.getExpiresAt())) {
                return QrCodeScanResponse.expired();
            }

            // 更新扫描统计
            updateScanStatistics(qrCodeId, request.getScannerId());

            // 记录扫描记录
            recordScanActivity(request, qrCode, EquipmentScanRecord.ScanResult.SUCCESS);

            // 构建响应
            QrCodeScanResponse response = QrCodeScanResponse.success(equipmentId);
            
            // 获取智能推荐的点检模板
            TemplateRecommendationResponse recommendation = 
                intelligentTemplateService.recommendTemplate(equipmentId, request.getScannerId());
            
            if (recommendation != null && recommendation.getPrimaryTemplateId() != null) {
                response.setRecommendedTemplateId(recommendation.getPrimaryTemplateId());
                response.setTemplateInfo(recommendation.getPrimaryTemplateInfo());
                
                // 添加推荐信息到额外数据
                Map<String, Object> extraData = new HashMap<>();
                extraData.put("confidence", recommendation.getConfidence());
                extraData.put("reason", recommendation.getReason());
                extraData.put("secondaryTemplates", recommendation.getSecondaryTemplates());
                response.setExtraData(extraData);
            }

            return response;
            
        } catch (Exception e) {
            log.error("扫描二维码失败: {}", e.getMessage(), e);
            
            // 记录失败的扫描记录
            recordFailedScan(request, e.getMessage());
            
            return QrCodeScanResponse.failed("扫描失败: " + e.getMessage());
        }
    }

    @Override
    public EquipmentQrCode getQrCodeByEquipmentId(String equipmentId) {
        return getOne(new LambdaQueryWrapper<EquipmentQrCode>()
            .eq(EquipmentQrCode::getEquipmentId, equipmentId)
            .eq(EquipmentQrCode::getIsActive, true)
            .orderByDesc(EquipmentQrCode::getGeneratedAt)
            .last("LIMIT 1"));
    }

    @Override
    @Transactional
    public void updateScanStatistics(String qrCodeId, String scannerId) {
        EquipmentQrCode qrCode = getById(qrCodeId);
        if (qrCode != null) {
            qrCode.setScanCount(qrCode.getScanCount() + 1);
            qrCode.setLastScanAt(LocalDateTime.now());
            updateById(qrCode);
        }
    }

    @Override
    @Transactional
    public void deactivateQrCode(String equipmentId) {
        update(new LambdaQueryWrapper<EquipmentQrCode>()
            .eq(EquipmentQrCode::getEquipmentId, equipmentId)
            .set(EquipmentQrCode::getIsActive, false));
    }

    @Override
    @Transactional
    public EquipmentQrCode regenerateQrCode(String equipmentId) {
        // 使原有二维码失效
        deactivateQrCode(equipmentId);
        
        // 生成新的二维码
        QrCodeGenerateRequest request = new QrCodeGenerateRequest();
        request.setEquipmentId(equipmentId);
        request.setQrType(EquipmentQrCode.QrCodeType.EQUIPMENT);
        
        return generateEquipmentQrCode(request);
    }

    /**
     * 获取推荐的点检模板
     */
    private String getRecommendedTemplate(String equipmentId) {
        // 查找设备关联的主要模板
        EquipmentTemplateAssociation association = templateAssociationService.getOne(
            new LambdaQueryWrapper<EquipmentTemplateAssociation>()
                .eq(EquipmentTemplateAssociation::getEquipmentId, equipmentId)
                .eq(EquipmentTemplateAssociation::getAssociationType, 
                    EquipmentTemplateAssociation.AssociationType.PRIMARY)
                .orderByDesc(EquipmentTemplateAssociation::getPriority)
                .last("LIMIT 1"));
        
        return association != null ? association.getTemplateId() : null;
    }

    /**
     * 记录扫描活动
     */
    private void recordScanActivity(QrCodeScanRequest request, EquipmentQrCode qrCode, 
                                  EquipmentScanRecord.ScanResult result) {
        EquipmentScanRecord record = new EquipmentScanRecord();
        record.setId(UUID.randomUUID().toString());
        record.setQrCodeId(qrCode.getId());
        record.setEquipmentId(qrCode.getEquipmentId());
        record.setScannerId(request.getScannerId());
        record.setScannerName(request.getScannerName());
        record.setScanType(request.getScanType());
        record.setScanLocation(request.getScanLocation());
        record.setScanDeviceInfo(request.getScanDeviceInfo());
        record.setScanResult(result);
        record.setScanTime(LocalDateTime.now());
        record.setIpAddress(request.getIpAddress());
        record.setUserAgent(request.getUserAgent());
        
        scanRecordService.save(record);
    }

    /**
     * 记录失败的扫描
     */
    private void recordFailedScan(QrCodeScanRequest request, String errorMessage) {
        try {
            EquipmentScanRecord record = new EquipmentScanRecord();
            record.setId(UUID.randomUUID().toString());
            record.setScannerId(request.getScannerId());
            record.setScannerName(request.getScannerName());
            record.setScanType(request.getScanType());
            record.setScanResult(EquipmentScanRecord.ScanResult.FAILED);
            record.setErrorMessage(errorMessage);
            record.setScanTime(LocalDateTime.now());
            record.setIpAddress(request.getIpAddress());
            record.setUserAgent(request.getUserAgent());
            
            scanRecordService.save(record);
        } catch (Exception e) {
            log.error("记录失败扫描记录时出错: {}", e.getMessage(), e);
        }
    }
}