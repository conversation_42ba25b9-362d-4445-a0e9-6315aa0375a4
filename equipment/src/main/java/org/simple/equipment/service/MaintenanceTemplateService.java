package org.simple.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.MaintenanceTemplate;

import java.util.List;
import java.util.Map;

/**
 * 设备保养模板服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface MaintenanceTemplateService extends IService<MaintenanceTemplate> {

    /**
     * 分页查询保养模板
     *
     * @param page 分页对象
     * @param template 查询条件
     * @return 分页结果
     */
    IPage<MaintenanceTemplate> selectMaintenanceTemplatePage(Page<MaintenanceTemplate> page, MaintenanceTemplate template);

    /**
     * 创建保养模板
     *
     * @param template 保养模板
     * @return 是否成功
     */
    boolean createMaintenanceTemplate(MaintenanceTemplate template);

    /**
     * 更新保养模板
     *
     * @param template 保养模板
     * @return 是否成功
     */
    boolean updateMaintenanceTemplate(MaintenanceTemplate template);

    /**
     * 删除保养模板
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean deleteMaintenanceTemplate(String templateId);

    /**
     * 获取设备类型的保养模板
     *
     * @param equipmentTypeId 设备类型ID
     * @param maintenanceType 保养类型
     * @return 模板列表
     */
    List<MaintenanceTemplate> getEquipmentTypeMaintenanceTemplates(String equipmentTypeId, String maintenanceType);

    /**
     * 获取默认保养模板
     *
     * @param equipmentTypeId 设备类型ID
     * @param maintenanceType 保养类型
     * @return 模板
     */
    MaintenanceTemplate getDefaultMaintenanceTemplate(String equipmentTypeId, String maintenanceType);

    /**
     * 获取保养模板统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getTemplateStatistics();

    /**
     * 按保养类型统计模板数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> getTemplateStatisticsByType();

    /**
     * 按设备类型统计模板数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> getTemplateStatisticsByEquipmentType();

    /**
     * 按创建部门统计模板数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> getTemplateStatisticsByDept();

    /**
     * 获取模板使用情况
     *
     * @param templateId 模板ID
     * @return 使用情况
     */
    Map<String, Object> getTemplateUsageStatistics(String templateId);

    /**
     * 获取公开的保养模板
     *
     * @param equipmentTypeId 设备类型ID
     * @param maintenanceType 保养类型
     * @return 模板列表
     */
    List<MaintenanceTemplate> getPublicMaintenanceTemplates(String equipmentTypeId, String maintenanceType);

    /**
     * 获取保养模板详情
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    Map<String, Object> getMaintenanceTemplateDetails(String templateId);

    /**
     * 获取需要审核的模板
     *
     * @param reviewStatus 审核状态
     * @return 模板列表
     */
    List<MaintenanceTemplate> getTemplatesForReview(Integer reviewStatus);

    /**
     * 获取热门保养模板
     *
     * @param limit 限制数量
     * @return 模板列表
     */
    List<MaintenanceTemplate> getPopularMaintenanceTemplates(Integer limit);

    /**
     * 获取最新保养模板
     *
     * @param limit 限制数量
     * @return 模板列表
     */
    List<MaintenanceTemplate> getLatestMaintenanceTemplates(Integer limit);

    /**
     * 获取相似保养模板
     *
     * @param templateId 模板ID
     * @param limit 限制数量
     * @return 模板列表
     */
    List<MaintenanceTemplate> getSimilarMaintenanceTemplates(String templateId, Integer limit);

    /**
     * 更新模板使用次数
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean updateTemplateUsageCount(String templateId);

    /**
     * 批量更新模板状态
     *
     * @param templateIds 模板ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateTemplateStatus(List<String> templateIds, Integer status);

    /**
     * 复制保养模板
     *
     * @param templateId 模板ID
     * @param newTemplateName 新模板名称
     * @return 新模板ID
     */
    String copyMaintenanceTemplate(String templateId, String newTemplateName);

    /**
     * 审核保养模板
     *
     * @param templateId 模板ID
     * @param reviewedBy 审核人ID
     * @param reviewStatus 审核状态
     * @param reviewComments 审核意见
     * @return 是否成功
     */
    boolean reviewMaintenanceTemplate(String templateId, String reviewedBy, Integer reviewStatus, String reviewComments);

    /**
     * 发布保养模板
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean publishMaintenanceTemplate(String templateId);

    /**
     * 停用保养模板
     *
     * @param templateId 模板ID
     * @return 是否成功
     */
    boolean disableMaintenanceTemplate(String templateId);

    /**
     * 导出保养模板
     *
     * @param templateIds 模板ID列表
     * @return 导出结果
     */
    List<Map<String, Object>> exportMaintenanceTemplates(List<String> templateIds);

    /**
     * 导入保养模板
     *
     * @param templateData 模板数据
     * @return 导入结果
     */
    Map<String, Object> importMaintenanceTemplates(List<Map<String, Object>> templateData);

    /**
     * 验证模板配置
     *
     * @param template 保养模板
     * @return 验证结果
     */
    Map<String, Object> validateTemplateConfiguration(MaintenanceTemplate template);

    /**
     * 设置默认模板
     *
     * @param templateId 模板ID
     * @param equipmentTypeId 设备类型ID
     * @param maintenanceType 保养类型
     * @return 是否成功
     */
    boolean setDefaultTemplate(String templateId, String equipmentTypeId, String maintenanceType);

    /**
     * 批量审核保养模板
     *
     * @param templateIds 模板ID列表
     * @param reviewedBy 审核人ID
     * @param reviewStatus 审核状态
     * @param reviewComments 审核意见
     * @return 审核结果
     */
    Map<String, Object> batchReviewMaintenanceTemplates(List<String> templateIds, String reviewedBy, Integer reviewStatus, String reviewComments);

    /**
     * 获取模板的使用历史
     *
     * @param templateId 模板ID
     * @return 使用历史
     */
    List<Map<String, Object>> getTemplateUsageHistory(String templateId);

    /**
     * 更新模板版本
     *
     * @param templateId 模板ID
     * @param newVersion 新版本号
     * @return 是否成功
     */
    boolean updateTemplateVersion(String templateId, String newVersion);

    /**
     * 获取模板的版本历史
     *
     * @param templateId 模板ID
     * @return 版本历史
     */
    List<Map<String, Object>> getTemplateVersionHistory(String templateId);
}