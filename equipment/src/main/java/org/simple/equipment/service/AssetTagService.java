package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.AssetTag;

import java.util.List;
import java.util.Map;

/**
 * 设备标签关联服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AssetTagService extends IService<AssetTag> {

    /**
     * 根据设备ID查询标签
     *
     * @param assetId 设备ID
     * @return 标签列表
     */
    List<AssetTag> getTagsByAssetId(String assetId);

    /**
     * 查询设备的标签信息（包含标签详情）
     *
     * @param assetId 设备ID
     * @return 标签信息列表
     */
    List<Map<String, Object>> getAssetTagDetails(String assetId);

    /**
     * 根据标签查询设备列表
     *
     * @param tagIds 标签ID列表
     * @return 设备列表
     */
    List<Map<String, Object>> getAssetsByTags(List<String> tagIds);

    /**
     * 保存设备标签
     *
     * @param assetId 设备ID
     * @param tagIds 标签ID列表
     * @return 结果
     */
    FrResult<?> saveAssetTags(String assetId, List<String> tagIds);

    /**
     * 保存设备标签（带标签值）
     *
     * @param assetId 设备ID
     * @param tagData 标签数据（标签ID和标签值的映射）
     * @return 结果
     */
    FrResult<?> saveAssetTagsWithValue(String assetId, Map<String, String> tagData);

    /**
     * 批量保存设备标签
     *
     * @param assetTags 设备标签列表
     * @return 结果
     */
    FrResult<?> batchSaveAssetTags(List<AssetTag> assetTags);

    /**
     * 删除设备标签
     *
     * @param assetId 设备ID
     * @param tagId 标签ID
     * @return 结果
     */
    FrResult<?> deleteAssetTag(String assetId, String tagId);

    /**
     * 删除设备的所有标签
     *
     * @param assetId 设备ID
     * @return 结果
     */
    FrResult<?> deleteAssetTags(String assetId);

    /**
     * 复制设备标签
     *
     * @param fromAssetId 源设备ID
     * @param toAssetId 目标设备ID
     * @return 结果
     */
    FrResult<?> copyAssetTags(String fromAssetId, String toAssetId);

    /**
     * 设置主要标签
     *
     * @param assetId 设备ID
     * @param tagId 标签ID
     * @return 结果
     */
    FrResult<?> setPrimaryTag(String assetId, String tagId);

    /**
     * 查询设备的主要标签
     *
     * @param assetId 设备ID
     * @return 主要标签列表
     */
    List<Map<String, Object>> getPrimaryTags(String assetId);

    /**
     * 应用标签组合到设备
     *
     * @param assetId 设备ID
     * @param combinationId 组合ID
     * @return 结果
     */
    FrResult<?> applyTagCombination(String assetId, String combinationId);

    /**
     * 批量应用标签到设备
     *
     * @param assetIds 设备ID列表
     * @param tagIds 标签ID列表
     * @return 结果
     */
    FrResult<?> batchApplyTags(List<String> assetIds, List<String> tagIds);

    /**
     * 查询标签云数据
     *
     * @param limit 限制数量
     * @return 标签云数据
     */
    List<Map<String, Object>> getTagCloud(int limit);

    /**
     * 查询设备标签统计
     *
     * @return 统计信息
     */
    Map<String, Object> getAssetTagStatistics();

    /**
     * 查询相似设备（基于标签）
     *
     * @param assetId 设备ID
     * @param limit 限制数量
     * @return 相似设备列表
     */
    List<Map<String, Object>> getSimilarAssets(String assetId, int limit);
}