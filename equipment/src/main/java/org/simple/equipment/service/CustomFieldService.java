package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.CustomField;

import java.util.List;

/**
 * 设备自定义字段配置服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface CustomFieldService extends IService<CustomField> {

    /**
     * 查询有效的自定义字段列表
     *
     * @return 字段列表
     */
    List<CustomField> getActiveFieldList();

    /**
     * 根据设备分类和类型查询自定义字段
     *
     * @param categoryId 分类ID
     * @param typeId 类型ID
     * @return 字段列表
     */
    List<CustomField> getFieldsByCategory(String categoryId, String typeId);

    /**
     * 查询自定义字段列表
     *
     * @param customField 查询条件
     * @return 字段列表
     */
    List<CustomField> getCustomFieldList(CustomField customField);

    /**
     * 保存自定义字段
     *
     * @param customField 字段信息
     * @return 结果
     */
    FrResult<?> saveCustomField(CustomField customField);

    /**
     * 更新自定义字段
     *
     * @param customField 字段信息
     * @return 结果
     */
    FrResult<?> updateCustomField(CustomField customField);

    /**
     * 删除自定义字段
     *
     * @param id 字段ID
     * @return 结果
     */
    FrResult<?> deleteCustomField(String id);

    /**
     * 检查字段编码是否存在
     *
     * @param fieldCode 字段编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkFieldCodeExists(String fieldCode, String excludeId);

    /**
     * 更新字段状态
     *
     * @param id 字段ID
     * @param isEnabled 是否启用
     * @return 结果
     */
    FrResult<?> updateFieldStatus(String id, String isEnabled);

    /**
     * 根据分组查询字段
     *
     * @param fieldGroup 字段分组
     * @return 字段列表
     */
    List<CustomField> getFieldsByGroup(String fieldGroup);

    /**
     * 复制字段配置
     *
     * @param fromCategoryId 源分类ID
     * @param toCategoryId 目标分类ID
     * @return 结果
     */
    FrResult<?> copyFields(String fromCategoryId, String toCategoryId);
}