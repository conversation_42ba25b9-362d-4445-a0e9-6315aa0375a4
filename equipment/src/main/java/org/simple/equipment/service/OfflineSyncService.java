package org.simple.equipment.service;

import org.simple.equipment.dto.OfflineDataPackage;
import org.simple.equipment.dto.SyncConflictResolution;
import org.simple.equipment.dto.SyncRequest;
import org.simple.equipment.dto.SyncResponse;
import org.simple.equipment.entity.OfflineSyncRecord;

import java.util.List;

/**
 * 离线同步服务接口
 * 
 * <AUTHOR>
 */
public interface OfflineSyncService {

    /**
     * 下载离线数据包
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param dataTypes 数据类型列表
     * @return 离线数据包
     */
    OfflineDataPackage downloadOfflineData(String userId, String deviceId, List<String> dataTypes);

    /**
     * 上传同步数据
     * 
     * @param request 同步请求
     * @return 同步响应
     */
    SyncResponse uploadSyncData(SyncRequest request);

    /**
     * 检测并解决数据冲突
     * 
     * @param syncRecordId 同步记录ID
     * @return 冲突解决结果
     */
    SyncConflictResolution detectAndResolveConflicts(String syncRecordId);

    /**
     * 获取待同步记录
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 待同步记录列表
     */
    List<OfflineSyncRecord> getPendingSyncRecords(String userId, String deviceId);

    /**
     * 批量同步数据
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 同步结果
     */
    SyncResponse batchSyncData(String userId, String deviceId);

    /**
     * 清理过期缓存
     * 
     * @return 清理数量
     */
    Integer cleanExpiredCache();

    /**
     * 获取同步状态
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 同步状态信息
     */
    SyncResponse getSyncStatus(String userId, String deviceId);

    /**
     * 强制同步指定实体
     * 
     * @param entityId 实体ID
     * @param entityType 实体类型
     * @param userId 用户ID
     * @return 同步结果
     */
    Boolean forceSyncEntity(String entityId, String entityType, String userId);

    /**
     * 重置同步状态
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 重置结果
     */
    Boolean resetSyncStatus(String userId, String deviceId);
}