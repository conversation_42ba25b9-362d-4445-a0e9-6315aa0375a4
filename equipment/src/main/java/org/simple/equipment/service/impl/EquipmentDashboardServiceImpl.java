package org.simple.equipment.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.simple.equipment.mapper.EquipmentMapper;
import org.simple.equipment.mapper.InspectionTaskMapper;
import org.simple.equipment.mapper.MaintenanceTaskMapper;
import org.simple.equipment.mapper.PatrolTaskMapper;
import org.simple.equipment.mapper.RepairOrderMapper;
import org.simple.equipment.service.EquipmentDashboardService;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 设备管理仪表盘服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Slf4j
@Service
public class EquipmentDashboardServiceImpl implements EquipmentDashboardService {

    @Autowired
    private EquipmentMapper equipmentMapper;

    @Autowired
    private RepairOrderMapper repairOrderMapper;

    @Autowired
    private MaintenanceTaskMapper maintenanceTaskMapper;

    @Autowired
    private InspectionTaskMapper inspectionTaskMapper;

    @Autowired
    private PatrolTaskMapper patrolTaskMapper;

    @Override
    public Map<String, Object> getEquipmentOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        try {
            // 设备总数
            overview.put("totalCount", 1250);
            
            // 运行设备数
            overview.put("runningCount", 1180);
            
            // 停机设备数
            overview.put("shutdownCount", 45);
            
            // 维修设备数
            overview.put("repairCount", 25);
            
            // 设备完好率
            overview.put("intactRate", "94.4%");
            
            // 设备可用率
            overview.put("availabilityRate", "96.0%");
            
            // 今日新增设备
            overview.put("todayNewCount", 3);
            
            // 今日设备异常
            overview.put("todayAbnormalCount", 8);
            
        } catch (Exception e) {
            log.error("获取设备总览统计失败", e);
            overview.put("error", "获取数据失败");
        }
        
        return overview;
    }

    @Override
    public Map<String, Object> getEquipmentStatusDistribution() {
        Map<String, Object> distribution = new HashMap<>();
        
        try {
            List<Map<String, Object>> statusData = new ArrayList<>();
            
            Map<String, Object> running = new HashMap<>();
            running.put("name", "运行中");
            running.put("value", 1180);
            running.put("percentage", "94.4%");
            running.put("color", "#52c41a");
            statusData.add(running);
            
            Map<String, Object> shutdown = new HashMap<>();
            shutdown.put("name", "停机");
            shutdown.put("value", 45);
            shutdown.put("percentage", "3.6%");
            shutdown.put("color", "#faad14");
            statusData.add(shutdown);
            
            Map<String, Object> repair = new HashMap<>();
            repair.put("name", "维修中");
            repair.put("value", 25);
            repair.put("percentage", "2.0%");
            repair.put("color", "#ff4d4f");
            statusData.add(repair);
            
            distribution.put("data", statusData);
            distribution.put("total", 1250);
            
        } catch (Exception e) {
            log.error("获取设备状态分布失败", e);
            distribution.put("error", "获取数据失败");
        }
        
        return distribution;
    }

    @Override
    public Map<String, Object> getEquipmentRunningStatus() {
        Map<String, Object> runningStatus = new HashMap<>();
        
        try {
            // 设备运行时长统计
            List<Map<String, Object>> runningTimeData = new ArrayList<>();
            runningTimeData.add(createRunningTimeItem("< 8小时", 156, "#1890ff"));
            runningTimeData.add(createRunningTimeItem("8-16小时", 890, "#52c41a"));
            runningTimeData.add(createRunningTimeItem("16-24小时", 134, "#faad14"));
            runningTimeData.add(createRunningTimeItem("> 24小时", 70, "#ff4d4f"));
            
            runningStatus.put("runningTimeDistribution", runningTimeData);
            
            // 设备负载状态
            List<Map<String, Object>> loadData = new ArrayList<>();
            loadData.add(createLoadItem("轻载", 420, "< 30%", "#52c41a"));
            loadData.add(createLoadItem("正常", 680, "30-80%", "#1890ff"));
            loadData.add(createLoadItem("重载", 120, "80-95%", "#faad14"));
            loadData.add(createLoadItem("超载", 30, "> 95%", "#ff4d4f"));
            
            runningStatus.put("loadDistribution", loadData);
            
            // 今日运行概况
            Map<String, Object> todayOverview = new HashMap<>();
            todayOverview.put("averageRunningTime", "18.5小时");
            todayOverview.put("averageLoad", "65.2%");
            todayOverview.put("peakLoadTime", "14:30");
            todayOverview.put("peakLoadValue", "87.6%");
            
            runningStatus.put("todayOverview", todayOverview);
            
        } catch (Exception e) {
            log.error("获取设备运行情况失败", e);
            runningStatus.put("error", "获取数据失败");
        }
        
        return runningStatus;
    }

    @Override
    public Map<String, Object> getTaskOverview() {
        Map<String, Object> taskOverview = new HashMap<>();
        
        try {
            // 任务总览
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalTasks", 346);
            overview.put("pendingTasks", 89);
            overview.put("inProgressTasks", 156);
            overview.put("completedTasks", 101);
            
            taskOverview.put("overview", overview);
            
            // 各类任务统计
            List<Map<String, Object>> taskTypeData = new ArrayList<>();
            taskTypeData.add(createTaskTypeItem("维修任务", 89, 23, "#ff4d4f"));
            taskTypeData.add(createTaskTypeItem("保养任务", 134, 45, "#52c41a"));
            taskTypeData.add(createTaskTypeItem("点检任务", 78, 12, "#1890ff"));
            taskTypeData.add(createTaskTypeItem("巡检任务", 45, 9, "#722ed1"));
            
            taskOverview.put("taskTypeData", taskTypeData);
            
            // 今日任务
            Map<String, Object> todayTasks = new HashMap<>();
            todayTasks.put("total", 45);
            todayTasks.put("completed", 28);
            todayTasks.put("inProgress", 12);
            todayTasks.put("pending", 5);
            todayTasks.put("completionRate", "62.2%");
            
            taskOverview.put("todayTasks", todayTasks);
            
        } catch (Exception e) {
            log.error("获取任务概览失败", e);
            taskOverview.put("error", "获取数据失败");
        }
        
        return taskOverview;
    }

    @Override
    public Map<String, Object> getRepairTaskStatistics() {
        Map<String, Object> repairStats = new HashMap<>();
        
        try {
            // 维修任务统计
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalOrders", 89);
            overview.put("pendingOrders", 23);
            overview.put("inProgressOrders", 34);
            overview.put("completedOrders", 32);
            
            repairStats.put("overview", overview);
            
            // 故障类型分布
            List<Map<String, Object>> faultTypeData = new ArrayList<>();
            faultTypeData.add(createFaultTypeItem("机械故障", 34, "#ff4d4f"));
            faultTypeData.add(createFaultTypeItem("电气故障", 28, "#faad14"));
            faultTypeData.add(createFaultTypeItem("控制故障", 18, "#1890ff"));
            faultTypeData.add(createFaultTypeItem("其他故障", 9, "#52c41a"));
            
            repairStats.put("faultTypeData", faultTypeData);
            
            // 紧急程度分布
            List<Map<String, Object>> urgencyData = new ArrayList<>();
            urgencyData.add(createUrgencyItem("特急", 8, "#ff4d4f"));
            urgencyData.add(createUrgencyItem("紧急", 15, "#faad14"));
            urgencyData.add(createUrgencyItem("一般", 45, "#1890ff"));
            urgencyData.add(createUrgencyItem("较低", 21, "#52c41a"));
            
            repairStats.put("urgencyData", urgencyData);
            
            // 维修效率
            Map<String, Object> efficiency = new HashMap<>();
            efficiency.put("averageRepairTime", "4.2小时");
            efficiency.put("firstTimeFixRate", "85.6%");
            efficiency.put("customerSatisfaction", "4.3分");
            efficiency.put("mttr", "3.8小时"); // 平均修复时间
            
            repairStats.put("efficiency", efficiency);
            
        } catch (Exception e) {
            log.error("获取维修任务统计失败", e);
            repairStats.put("error", "获取数据失败");
        }
        
        return repairStats;
    }

    @Override
    public Map<String, Object> getMaintenanceTaskStatistics() {
        Map<String, Object> maintenanceStats = new HashMap<>();
        
        try {
            // 保养任务统计
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalTasks", 134);
            overview.put("scheduledTasks", 45);
            overview.put("inProgressTasks", 23);
            overview.put("completedTasks", 66);
            
            maintenanceStats.put("overview", overview);
            
            // 保养类型分布
            List<Map<String, Object>> typeData = new ArrayList<>();
            typeData.add(createMaintenanceTypeItem("日常保养", 56, "#52c41a"));
            typeData.add(createMaintenanceTypeItem("一级保养", 34, "#1890ff"));
            typeData.add(createMaintenanceTypeItem("二级保养", 28, "#faad14"));
            typeData.add(createMaintenanceTypeItem("三级保养", 16, "#ff4d4f"));
            
            maintenanceStats.put("typeData", typeData);
            
            // 保养完成率趋势（最近7天）
            List<Map<String, Object>> trendData = new ArrayList<>();
            String[] dates = getRecentDates(7);
            double[] rates = {88.5, 92.3, 89.7, 94.2, 87.6, 91.8, 93.5};
            
            for (int i = 0; i < dates.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", dates[i]);
                trend.put("completionRate", rates[i]);
                trendData.add(trend);
            }
            
            maintenanceStats.put("trendData", trendData);
            
            // 保养质量评价
            Map<String, Object> quality = new HashMap<>();
            quality.put("averageScore", "4.5分");
            quality.put("excellentRate", "78.9%");
            quality.put("goodRate", "18.7%");
            quality.put("averageRate", "2.4%");
            
            maintenanceStats.put("quality", quality);
            
        } catch (Exception e) {
            log.error("获取保养任务统计失败", e);
            maintenanceStats.put("error", "获取数据失败");
        }
        
        return maintenanceStats;
    }

    @Override
    public Map<String, Object> getInspectionTaskStatistics() {
        Map<String, Object> inspectionStats = new HashMap<>();
        
        try {
            // 点检任务统计
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalTasks", 78);
            overview.put("pendingTasks", 12);
            overview.put("completedTasks", 54);
            overview.put("overdueTaskss", 3);
            
            inspectionStats.put("overview", overview);
            
            // 点检频率分布
            List<Map<String, Object>> frequencyData = new ArrayList<>();
            frequencyData.add(createFrequencyItem("每日", 28, "#52c41a"));
            frequencyData.add(createFrequencyItem("每周", 25, "#1890ff"));
            frequencyData.add(createFrequencyItem("每月", 18, "#faad14"));
            frequencyData.add(createFrequencyItem("每季度", 7, "#722ed1"));
            
            inspectionStats.put("frequencyData", frequencyData);
            
            // 点检异常发现率
            Map<String, Object> abnormalStats = new HashMap<>();
            abnormalStats.put("totalInspections", 234);
            abnormalStats.put("abnormalFound", 18);
            abnormalStats.put("abnormalRate", "7.7%");
            abnormalStats.put("criticalIssues", 3);
            
            inspectionStats.put("abnormalStats", abnormalStats);
            
        } catch (Exception e) {
            log.error("获取点检任务统计失败", e);
            inspectionStats.put("error", "获取数据失败");
        }
        
        return inspectionStats;
    }

    @Override
    public Map<String, Object> getPatrolTaskStatistics() {
        Map<String, Object> patrolStats = new HashMap<>();
        
        try {
            // 巡检任务统计
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalTasks", 45);
            overview.put("scheduledTasks", 9);
            overview.put("completedTasks", 32);
            overview.put("missedTasks", 4);
            
            patrolStats.put("overview", overview);
            
            // 巡检路线分布
            List<Map<String, Object>> routeData = new ArrayList<>();
            routeData.add(createRouteItem("机房巡检路线", 15, "#1890ff"));
            routeData.add(createRouteItem("办公楼巡检路线", 18, "#52c41a"));
            routeData.add(createRouteItem("厂区巡检路线", 12, "#faad14"));
            
            patrolStats.put("routeData", routeData);
            
            // 巡检覆盖率
            Map<String, Object> coverage = new HashMap<>();
            coverage.put("totalCheckpoints", 156);
            coverage.put("coveredCheckpoints", 142);
            coverage.put("coverageRate", "91.0%");
            coverage.put("missedCheckpoints", 14);
            
            patrolStats.put("coverage", coverage);
            
        } catch (Exception e) {
            log.error("获取巡检任务统计失败", e);
            patrolStats.put("error", "获取数据失败");
        }
        
        return patrolStats;
    }

    @Override
    public Map<String, Object> getEquipmentValueStatistics() {
        Map<String, Object> valueStats = new HashMap<>();
        
        try {
            // 资产价值概览
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalValue", "5.2亿元");
            overview.put("originalValue", "6.8亿元");
            overview.put("depreciationRate", "23.5%");
            overview.put("netValue", "5.2亿元");
            
            valueStats.put("overview", overview);
            
            // 价值分布（按设备类型）
            List<Map<String, Object>> typeValueData = new ArrayList<>();
            typeValueData.add(createValueItem("生产设备", "3.2亿元", 61.5, "#1890ff"));
            typeValueData.add(createValueItem("检测设备", "0.8亿元", 15.4, "#52c41a"));
            typeValueData.add(createValueItem("办公设备", "0.6亿元", 11.5, "#faad14"));
            typeValueData.add(createValueItem("其他设备", "0.6亿元", 11.6, "#722ed1"));
            
            valueStats.put("typeValueData", typeValueData);
            
            // 折旧趋势（最近12个月）
            List<Map<String, Object>> depreciationTrend = new ArrayList<>();
            String[] months = getRecentMonths(12);
            double[] values = {6.8, 6.7, 6.5, 6.3, 6.1, 5.9, 5.8, 5.6, 5.5, 5.4, 5.3, 5.2};
            
            for (int i = 0; i < months.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("month", months[i]);
                trend.put("value", values[i]);
                depreciationTrend.add(trend);
            }
            
            valueStats.put("depreciationTrend", depreciationTrend);
            
        } catch (Exception e) {
            log.error("获取设备价值统计失败", e);
            valueStats.put("error", "获取数据失败");
        }
        
        return valueStats;
    }

    @Override
    public Map<String, Object> getEquipmentTypeDistribution() {
        Map<String, Object> typeDistribution = new HashMap<>();
        
        try {
            List<Map<String, Object>> typeData = new ArrayList<>();
            typeData.add(createTypeItem("生产设备", 456, 36.5, "#1890ff"));
            typeData.add(createTypeItem("检测设备", 234, 18.7, "#52c41a"));
            typeData.add(createTypeItem("办公设备", 298, 23.8, "#faad14"));
            typeData.add(createTypeItem("运输设备", 89, 7.1, "#722ed1"));
            typeData.add(createTypeItem("安全设备", 123, 9.8, "#eb2f96"));
            typeData.add(createTypeItem("其他设备", 50, 4.0, "#13c2c2"));
            
            typeDistribution.put("data", typeData);
            typeDistribution.put("total", 1250);
            
        } catch (Exception e) {
            log.error("获取设备类型分布失败", e);
            typeDistribution.put("error", "获取数据失败");
        }
        
        return typeDistribution;
    }

    @Override
    public Map<String, Object> getDepartmentEquipmentDistribution() {
        Map<String, Object> deptDistribution = new HashMap<>();
        
        try {
            List<Map<String, Object>> deptData = new ArrayList<>();
            deptData.add(createDeptItem("生产部", 456, 36.5, "#1890ff"));
            deptData.add(createDeptItem("技术部", 234, 18.7, "#52c41a"));
            deptData.add(createDeptItem("行政部", 198, 15.8, "#faad14"));
            deptData.add(createDeptItem("质量部", 145, 11.6, "#722ed1"));
            deptData.add(createDeptItem("设备部", 128, 10.2, "#eb2f96"));
            deptData.add(createDeptItem("其他部门", 89, 7.1, "#13c2c2"));
            
            deptDistribution.put("data", deptData);
            deptDistribution.put("total", 1250);
            
        } catch (Exception e) {
            log.error("获取部门设备分布失败", e);
            deptDistribution.put("error", "获取数据失败");
        }
        
        return deptDistribution;
    }

    @Override
    public Map<String, Object> getEquipmentHealthAssessment() {
        Map<String, Object> healthAssessment = new HashMap<>();
        
        try {
            // 健康度概览
            Map<String, Object> overview = new HashMap<>();
            overview.put("averageHealth", 85.6);
            overview.put("excellentCount", 789);
            overview.put("goodCount", 298);
            overview.put("averageCount", 134);
            overview.put("poorCount", 29);
            
            healthAssessment.put("overview", overview);
            
            // 健康度分布
            List<Map<String, Object>> healthData = new ArrayList<>();
            healthData.add(createHealthItem("优秀", 789, "90-100分", "#52c41a"));
            healthData.add(createHealthItem("良好", 298, "80-89分", "#1890ff"));
            healthData.add(createHealthItem("一般", 134, "70-79分", "#faad14"));
            healthData.add(createHealthItem("较差", 29, "< 70分", "#ff4d4f"));
            
            healthAssessment.put("healthData", healthData);
            
            // 健康度变化趋势
            List<Map<String, Object>> trendData = new ArrayList<>();
            String[] dates = getRecentDates(30);
            double[] scores = generateHealthTrendData(30);
            
            for (int i = 0; i < Math.min(dates.length, scores.length); i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", dates[i]);
                trend.put("score", scores[i]);
                trendData.add(trend);
            }
            
            healthAssessment.put("trendData", trendData);
            
        } catch (Exception e) {
            log.error("获取设备健康度评估失败", e);
            healthAssessment.put("error", "获取数据失败");
        }
        
        return healthAssessment;
    }

    @Override
    public Map<String, Object> getFailureRateStatistics() {
        Map<String, Object> failureStats = new HashMap<>();
        
        try {
            // 故障率概览
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalFailures", 89);
            overview.put("mtbf", "2160小时"); // 平均无故障时间
            overview.put("monthlyFailureRate", "2.3%");
            overview.put("criticalFailures", 12);
            
            failureStats.put("overview", overview);
            
            // 故障趋势（最近12个月）
            List<Map<String, Object>> trendData = new ArrayList<>();
            String[] months = getRecentMonths(12);
            int[] failures = {15, 12, 18, 14, 16, 11, 13, 17, 14, 12, 15, 13};
            
            for (int i = 0; i < months.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("month", months[i]);
                trend.put("failures", failures[i]);
                trend.put("rate", (failures[i] / 1250.0 * 100));
                trendData.add(trend);
            }
            
            failureStats.put("trendData", trendData);
            
            // 故障原因分析
            List<Map<String, Object>> causeData = new ArrayList<>();
            causeData.add(createCauseItem("设备老化", 28, "#ff4d4f"));
            causeData.add(createCauseItem("操作不当", 21, "#faad14"));
            causeData.add(createCauseItem("维护不足", 18, "#1890ff"));
            causeData.add(createCauseItem("环境因素", 12, "#52c41a"));
            causeData.add(createCauseItem("其他原因", 10, "#722ed1"));
            
            failureStats.put("causeData", causeData);
            
        } catch (Exception e) {
            log.error("获取故障率统计失败", e);
            failureStats.put("error", "获取数据失败");
        }
        
        return failureStats;
    }

    @Override
    public Map<String, Object> getEquipmentUtilizationStatistics() {
        Map<String, Object> utilizationStats = new HashMap<>();
        
        try {
            // 利用率概览
            Map<String, Object> overview = new HashMap<>();
            overview.put("averageUtilization", 76.8);
            overview.put("highUtilizationCount", 456);
            overview.put("mediumUtilizationCount", 589);
            overview.put("lowUtilizationCount", 205);
            
            utilizationStats.put("overview", overview);
            
            // 利用率分布
            List<Map<String, Object>> utilizationData = new ArrayList<>();
            utilizationData.add(createUtilizationItem("高利用率", 456, "> 80%", "#52c41a"));
            utilizationData.add(createUtilizationItem("中等利用率", 589, "50-80%", "#1890ff"));
            utilizationData.add(createUtilizationItem("低利用率", 205, "< 50%", "#faad14"));
            
            utilizationStats.put("utilizationData", utilizationData);
            
            // 利用率趋势（最近24小时）
            List<Map<String, Object>> hourlyTrend = new ArrayList<>();
            for (int i = 0; i < 24; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("hour", String.format("%02d:00", i));
                trend.put("utilization", 60 + Math.random() * 30); // 模拟数据
                hourlyTrend.add(trend);
            }
            
            utilizationStats.put("hourlyTrend", hourlyTrend);
            
        } catch (Exception e) {
            log.error("获取设备利用率统计失败", e);
            utilizationStats.put("error", "获取数据失败");
        }
        
        return utilizationStats;
    }

    @Override
    public Map<String, Object> getRecentAlerts() {
        Map<String, Object> alerts = new HashMap<>();
        
        try {
            // 告警概览
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalAlerts", 34);
            overview.put("criticalAlerts", 5);
            overview.put("warningAlerts", 15);
            overview.put("infoAlerts", 14);
            
            alerts.put("overview", overview);
            
            // 最新告警
            List<Map<String, Object>> recentAlerts = new ArrayList<>();
            recentAlerts.add(createAlertItem("生产线A-3号机床", "温度过高", "严重", "2分钟前", "#ff4d4f"));
            recentAlerts.add(createAlertItem("空调系统-1号机", "制冷效果差", "警告", "15分钟前", "#faad14"));
            recentAlerts.add(createAlertItem("电梯系统-2号梯", "运行异响", "提醒", "1小时前", "#1890ff"));
            recentAlerts.add(createAlertItem("UPS电源-主机", "电池电量低", "警告", "2小时前", "#faad14"));
            recentAlerts.add(createAlertItem("消防系统-泵房", "水压不足", "严重", "3小时前", "#ff4d4f"));
            
            alerts.put("recentAlerts", recentAlerts);
            
            // 告警趋势（最近7天）
            List<Map<String, Object>> trendData = new ArrayList<>();
            String[] dates = getRecentDates(7);
            int[] alertCounts = {8, 12, 6, 15, 9, 11, 7};
            
            for (int i = 0; i < dates.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", dates[i]);
                trend.put("count", alertCounts[i]);
                trendData.add(trend);
            }
            
            alerts.put("trendData", trendData);
            
        } catch (Exception e) {
            log.error("获取近期告警信息失败", e);
            alerts.put("error", "获取数据失败");
        }
        
        return alerts;
    }

    @Override
    public Map<String, Object> getPendingItems() {
        Map<String, Object> pendingItems = new HashMap<>();
        
        try {
            // 待处理事项概览
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalItems", 156);
            overview.put("urgentItems", 23);
            overview.put("overdueItems", 8);
            overview.put("todayItems", 45);
            
            pendingItems.put("overview", overview);
            
            // 待处理事项分类
            List<Map<String, Object>> categoryData = new ArrayList<>();
            categoryData.add(createPendingItem("待审批维修工单", 23, "#ff4d4f"));
            categoryData.add(createPendingItem("待执行保养任务", 45, "#faad14"));
            categoryData.add(createPendingItem("待完成点检任务", 34, "#1890ff"));
            categoryData.add(createPendingItem("待处理设备异常", 28, "#722ed1"));
            categoryData.add(createPendingItem("待确认调拨申请", 15, "#52c41a"));
            categoryData.add(createPendingItem("待更新设备档案", 11, "#13c2c2"));
            
            pendingItems.put("categoryData", categoryData);
            
            // 紧急待办
            List<Map<String, Object>> urgentItems = new ArrayList<>();
            urgentItems.add(createUrgentItem("生产线B故障维修", "维修工单", "2小时前", "#ff4d4f"));
            urgentItems.add(createUrgentItem("消防设备月度保养", "保养任务", "今天", "#faad14"));
            urgentItems.add(createUrgentItem("电梯安全检查", "点检任务", "明天", "#1890ff"));
            
            pendingItems.put("urgentItems", urgentItems);
            
        } catch (Exception e) {
            log.error("获取待处理事项失败", e);
            pendingItems.put("error", "获取数据失败");
        }
        
        return pendingItems;
    }

    @Override
    public Map<String, Object> getEquipmentTrendAnalysis(Integer days) {
        Map<String, Object> trendAnalysis = new HashMap<>();
        
        try {
            if (days == null) days = 30;
            
            // 设备数量趋势
            List<Map<String, Object>> countTrend = new ArrayList<>();
            String[] dates = getRecentDates(days);
            
            for (int i = 0; i < dates.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", dates[i]);
                trend.put("total", 1250 + (int)(Math.random() * 10 - 5));
                trend.put("running", 1180 + (int)(Math.random() * 20 - 10));
                trend.put("shutdown", 45 + (int)(Math.random() * 10 - 5));
                trend.put("repair", 25 + (int)(Math.random() * 6 - 3));
                countTrend.add(trend);
            }
            
            trendAnalysis.put("countTrend", countTrend);
            
            // 故障率趋势
            List<Map<String, Object>> failureTrend = new ArrayList<>();
            for (int i = 0; i < dates.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", dates[i]);
                trend.put("failureRate", 2.0 + Math.random() * 2.0);
                failureTrend.add(trend);
            }
            
            trendAnalysis.put("failureTrend", failureTrend);
            
            // 健康度趋势
            List<Map<String, Object>> healthTrend = new ArrayList<>();
            for (int i = 0; i < dates.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", dates[i]);
                trend.put("healthScore", 80 + Math.random() * 15);
                healthTrend.add(trend);
            }
            
            trendAnalysis.put("healthTrend", healthTrend);
            
        } catch (Exception e) {
            log.error("获取设备趋势分析失败", e);
            trendAnalysis.put("error", "获取数据失败");
        }
        
        return trendAnalysis;
    }

    @Override
    public Map<String, Object> getCostStatistics(String startDate, String endDate) {
        Map<String, Object> costStats = new HashMap<>();
        
        try {
            // 成本概览
            Map<String, Object> overview = new HashMap<>();
            overview.put("totalCost", "458.6万元");
            overview.put("repairCost", "156.8万元");
            overview.put("maintenanceCost", "234.5万元");
            overview.put("operationCost", "67.3万元");
            
            costStats.put("overview", overview);
            
            // 成本分布
            List<Map<String, Object>> costDistribution = new ArrayList<>();
            costDistribution.add(createCostItem("维修费用", "156.8万元", 34.2, "#ff4d4f"));
            costDistribution.add(createCostItem("保养费用", "234.5万元", 51.1, "#52c41a"));
            costDistribution.add(createCostItem("运行费用", "67.3万元", 14.7, "#1890ff"));
            
            costStats.put("costDistribution", costDistribution);
            
            // 月度成本趋势
            List<Map<String, Object>> monthlyTrend = new ArrayList<>();
            String[] months = getRecentMonths(12);
            double[] costs = {42.5, 38.9, 45.2, 41.3, 39.8, 44.1, 46.3, 43.7, 40.2, 42.8, 45.9, 38.6};
            
            for (int i = 0; i < months.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("month", months[i]);
                trend.put("cost", costs[i]);
                monthlyTrend.add(trend);
            }
            
            costStats.put("monthlyTrend", monthlyTrend);
            
        } catch (Exception e) {
            log.error("获取成本统计失败", e);
            costStats.put("error", "获取数据失败");
        }
        
        return costStats;
    }

    @Override
    public Map<String, Object> getWorkEfficiencyStatistics() {
        Map<String, Object> efficiencyStats = new HashMap<>();
        
        try {
            // 效率概览
            Map<String, Object> overview = new HashMap<>();
            overview.put("oee", "78.5%"); // 设备综合效率
            overview.put("availability", "96.2%"); // 可用性
            overview.put("performance", "85.3%"); // 性能效率
            overview.put("quality", "95.8%"); // 质量效率
            
            efficiencyStats.put("overview", overview);
            
            // 效率趋势（最近30天）
            List<Map<String, Object>> trendData = new ArrayList<>();
            String[] dates = getRecentDates(30);
            
            for (int i = 0; i < dates.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("date", dates[i]);
                trend.put("oee", 75 + Math.random() * 10);
                trend.put("availability", 94 + Math.random() * 4);
                trend.put("performance", 82 + Math.random() * 8);
                trend.put("quality", 93 + Math.random() * 5);
                trendData.add(trend);
            }
            
            efficiencyStats.put("trendData", trendData);
            
            // 部门效率对比
            List<Map<String, Object>> deptEfficiency = new ArrayList<>();
            deptEfficiency.add(createEfficiencyItem("生产部", 82.3, "#1890ff"));
            deptEfficiency.add(createEfficiencyItem("技术部", 76.8, "#52c41a"));
            deptEfficiency.add(createEfficiencyItem("质量部", 79.5, "#faad14"));
            deptEfficiency.add(createEfficiencyItem("设备部", 85.2, "#722ed1"));
            
            efficiencyStats.put("deptEfficiency", deptEfficiency);
            
        } catch (Exception e) {
            log.error("获取工作效率统计失败", e);
            efficiencyStats.put("error", "获取数据失败");
        }
        
        return efficiencyStats;
    }

    @Override
    public Map<String, Object> getEquipmentRealTimeMonitoring() {
        Map<String, Object> realTimeData = new HashMap<>();
        
        try {
            // 实时状态
            Map<String, Object> realTimeStatus = new HashMap<>();
            realTimeStatus.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            realTimeStatus.put("onlineCount", 1187);
            realTimeStatus.put("offlineCount", 63);
            realTimeStatus.put("alertCount", 12);
            realTimeStatus.put("connectionRate", "95.0%");
            
            realTimeData.put("realTimeStatus", realTimeStatus);
            
            // 关键指标监控
            List<Map<String, Object>> keyMetrics = new ArrayList<>();
            keyMetrics.add(createMetricItem("设备开机率", "96.2%", "normal", "#52c41a"));
            keyMetrics.add(createMetricItem("平均负载", "73.5%", "normal", "#1890ff"));
            keyMetrics.add(createMetricItem("告警数量", "12", "warning", "#faad14"));
            keyMetrics.add(createMetricItem("离线设备", "63", "error", "#ff4d4f"));
            
            realTimeData.put("keyMetrics", keyMetrics);
            
            // 最新设备状态变化
            List<Map<String, Object>> statusChanges = new ArrayList<>();
            statusChanges.add(createStatusChange("生产线A-5号机", "运行中", "正常", "1分钟前"));
            statusChanges.add(createStatusChange("空调系统-3号机", "故障", "异常", "3分钟前"));
            statusChanges.add(createStatusChange("电梯-1号梯", "维修中", "维修", "15分钟前"));
            statusChanges.add(createStatusChange("UPS-备用机", "启动", "正常", "25分钟前"));
            
            realTimeData.put("statusChanges", statusChanges);
            
        } catch (Exception e) {
            log.error("获取设备实时监控数据失败", e);
            realTimeData.put("error", "获取数据失败");
        }
        
        return realTimeData;
    }

    @Override
    public Map<String, Object> getKPIIndicators() {
        Map<String, Object> kpiData = new HashMap<>();
        
        try {
            // 核心KPI指标
            List<Map<String, Object>> kpiList = new ArrayList<>();
            
            kpiList.add(createKPIItem("设备完好率", "94.4%", "95.0%", "normal", "↑0.3%"));
            kpiList.add(createKPIItem("设备利用率", "76.8%", "80.0%", "warning", "↓1.2%"));
            kpiList.add(createKPIItem("故障率", "2.3%", "2.0%", "warning", "↑0.3%"));
            kpiList.add(createKPIItem("维修及时率", "96.5%", "95.0%", "normal", "↑1.5%"));
            kpiList.add(createKPIItem("保养完成率", "93.2%", "95.0%", "warning", "↓1.8%"));
            kpiList.add(createKPIItem("OEE综合效率", "78.5%", "80.0%", "warning", "↓1.5%"));
            
            kpiData.put("kpiList", kpiList);
            
            // KPI达成情况
            Map<String, Object> achievement = new HashMap<>();
            achievement.put("totalKPIs", 6);
            achievement.put("achievedKPIs", 2);
            achievement.put("warningKPIs", 4);
            achievement.put("failedKPIs", 0);
            achievement.put("achievementRate", "33.3%");
            
            kpiData.put("achievement", achievement);
            
            // KPI趋势分析
            List<Map<String, Object>> kpiTrend = new ArrayList<>();
            String[] months = getRecentMonths(6);
            
            for (int i = 0; i < months.length; i++) {
                Map<String, Object> trend = new HashMap<>();
                trend.put("month", months[i]);
                trend.put("intactRate", 93 + Math.random() * 4);
                trend.put("utilizationRate", 75 + Math.random() * 8);
                trend.put("repairTimeliness", 94 + Math.random() * 4);
                kpiTrend.add(trend);
            }
            
            kpiData.put("kpiTrend", kpiTrend);
            
        } catch (Exception e) {
            log.error("获取KPI指标失败", e);
            kpiData.put("error", "获取数据失败");
        }
        
        return kpiData;
    }

    // 辅助方法
    private Map<String, Object> createRunningTimeItem(String range, int count, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("range", range);
        item.put("count", count);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 1250.0 * 100));
        return item;
    }

    private Map<String, Object> createLoadItem(String level, int count, String range, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("level", level);
        item.put("count", count);
        item.put("range", range);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 1250.0 * 100));
        return item;
    }

    private Map<String, Object> createTaskTypeItem(String type, int total, int pending, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("type", type);
        item.put("total", total);
        item.put("pending", pending);
        item.put("color", color);
        item.put("completionRate", String.format("%.1f%%", (total - pending) / (double) total * 100));
        return item;
    }

    private Map<String, Object> createFaultTypeItem(String type, int count, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("type", type);
        item.put("count", count);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 89.0 * 100));
        return item;
    }

    private Map<String, Object> createUrgencyItem(String level, int count, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("level", level);
        item.put("count", count);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 89.0 * 100));
        return item;
    }

    private Map<String, Object> createMaintenanceTypeItem(String type, int count, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("type", type);
        item.put("count", count);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 134.0 * 100));
        return item;
    }

    private Map<String, Object> createFrequencyItem(String frequency, int count, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("frequency", frequency);
        item.put("count", count);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 78.0 * 100));
        return item;
    }

    private Map<String, Object> createRouteItem(String route, int count, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("route", route);
        item.put("count", count);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 45.0 * 100));
        return item;
    }

    private Map<String, Object> createValueItem(String type, String value, double percentage, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("type", type);
        item.put("value", value);
        item.put("percentage", percentage);
        item.put("color", color);
        return item;
    }

    private Map<String, Object> createTypeItem(String type, int count, double percentage, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("type", type);
        item.put("count", count);
        item.put("percentage", percentage);
        item.put("color", color);
        return item;
    }

    private Map<String, Object> createDeptItem(String dept, int count, double percentage, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("department", dept);
        item.put("count", count);
        item.put("percentage", percentage);
        item.put("color", color);
        return item;
    }

    private Map<String, Object> createHealthItem(String level, int count, String range, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("level", level);
        item.put("count", count);
        item.put("range", range);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 1250.0 * 100));
        return item;
    }

    private Map<String, Object> createCauseItem(String cause, int count, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("cause", cause);
        item.put("count", count);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 89.0 * 100));
        return item;
    }

    private Map<String, Object> createUtilizationItem(String level, int count, String range, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("level", level);
        item.put("count", count);
        item.put("range", range);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 1250.0 * 100));
        return item;
    }

    private Map<String, Object> createAlertItem(String equipment, String message, String level, String time, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("equipment", equipment);
        item.put("message", message);
        item.put("level", level);
        item.put("time", time);
        item.put("color", color);
        return item;
    }

    private Map<String, Object> createPendingItem(String type, int count, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("type", type);
        item.put("count", count);
        item.put("color", color);
        item.put("percentage", String.format("%.1f%%", count / 156.0 * 100));
        return item;
    }

    private Map<String, Object> createUrgentItem(String title, String type, String time, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("title", title);
        item.put("type", type);
        item.put("time", time);
        item.put("color", color);
        return item;
    }

    private Map<String, Object> createCostItem(String type, String amount, double percentage, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("type", type);
        item.put("amount", amount);
        item.put("percentage", percentage);
        item.put("color", color);
        return item;
    }

    private Map<String, Object> createEfficiencyItem(String department, double efficiency, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("department", department);
        item.put("efficiency", efficiency);
        item.put("color", color);
        return item;
    }

    private Map<String, Object> createMetricItem(String name, String value, String status, String color) {
        Map<String, Object> item = new HashMap<>();
        item.put("name", name);
        item.put("value", value);
        item.put("status", status);
        item.put("color", color);
        return item;
    }

    private Map<String, Object> createStatusChange(String equipment, String status, String type, String time) {
        Map<String, Object> item = new HashMap<>();
        item.put("equipment", equipment);
        item.put("status", status);
        item.put("type", type);
        item.put("time", time);
        return item;
    }

    private Map<String, Object> createKPIItem(String name, String actual, String target, String status, String trend) {
        Map<String, Object> item = new HashMap<>();
        item.put("name", name);
        item.put("actual", actual);
        item.put("target", target);
        item.put("status", status);
        item.put("trend", trend);
        return item;
    }

    private String[] getRecentDates(int days) {
        String[] dates = new String[days];
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");
        
        for (int i = days - 1; i >= 0; i--) {
            dates[days - 1 - i] = now.minusDays(i).format(formatter);
        }
        
        return dates;
    }

    private String[] getRecentMonths(int months) {
        String[] monthArray = new String[months];
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        
        for (int i = months - 1; i >= 0; i--) {
            monthArray[months - 1 - i] = now.minusMonths(i).format(formatter);
        }
        
        return monthArray;
    }

    private double[] generateHealthTrendData(int days) {
        double[] data = new double[days];
        double baseScore = 85.0;
        
        for (int i = 0; i < days; i++) {
            data[i] = baseScore + (Math.random() - 0.5) * 10;
        }
        
        return data;
    }
}