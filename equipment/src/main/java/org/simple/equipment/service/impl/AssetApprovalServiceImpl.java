package org.simple.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.equipment.entity.AssetApproval;
import org.simple.equipment.mapper.AssetApprovalMapper;
import org.simple.equipment.service.AssetApprovalService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备档案审批流程服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AssetApprovalServiceImpl extends ServiceImpl<AssetApprovalMapper, AssetApproval> implements AssetApprovalService {

    @Override
    public List<AssetApproval> getApprovalsByVersionId(String versionId) {
        QueryWrapper<AssetApproval> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("version_id", versionId)
                .eq("tenant_id", AuthUtil.getTenantId())
                .orderByAsc("approval_level");
        return list(queryWrapper);
    }

    @Override
    public List<Map<String, Object>> getPendingApprovals(String approver) {
        QueryWrapper<AssetApproval> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("approver", approver)
                .eq("approval_status", "PENDING")
                .eq("tenant_id", AuthUtil.getTenantId())
                .orderByDesc("create_date");
        return listMaps(queryWrapper);
    }

    @Override
    public Map<String, Object> getApprovalStatistics() {
        QueryWrapper<AssetApproval> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", AuthUtil.getTenantId());
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalApprovals", count(queryWrapper));
        
        // 统计审批状态
        queryWrapper.select("approval_status", "count(*) as count")
                .groupBy("approval_status");
        List<Map<String, Object>> statusStats = listMaps(queryWrapper);
        statistics.put("statusStats", statusStats);
        
        return statistics;
    }
}