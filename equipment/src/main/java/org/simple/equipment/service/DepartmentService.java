package org.simple.equipment.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.base.vo.FrResult;
import org.simple.equipment.entity.Department;

import java.util.List;

/**
 * 科室管理服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface DepartmentService extends IService<Department> {

    /**
     * 查询科室树形结构
     *
     * @return 科室树
     */
    List<Tree<String>> getDepartmentTree();

    /**
     * 查询科室列表
     *
     * @param department 查询条件
     * @return 科室列表
     */
    List<Department> getDepartmentList(Department department);

    /**
     * 新增科室
     *
     * @param department 科室信息
     * @return 操作结果
     */
    FrResult<?> saveDepartment(Department department);

    /**
     * 修改科室
     *
     * @param department 科室信息
     * @return 操作结果
     */
    FrResult<?> updateDepartment(Department department);

    /**
     * 删除科室
     *
     * @param id 科室ID
     * @return 操作结果
     */
    FrResult<?> deleteDepartment(String id);

    /**
     * 检查科室编码是否存在
     *
     * @param deptCode 科室编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean checkDeptCodeExists(String deptCode, String excludeId);

    /**
     * 检查科室是否有子节点
     *
     * @param id 科室ID
     * @return 是否有子节点
     */
    boolean hasChildren(String id);

    /**
     * 检查科室下是否有设备
     *
     * @param id 科室ID
     * @return 是否有设备
     */
    boolean hasAssets(String id);
}