package org.simple.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.AssetVersionTag;

import java.util.List;

/**
 * 设备档案版本标签服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public interface AssetVersionTagService extends IService<AssetVersionTag> {

    /**
     * 查询版本的标签列表
     *
     * @param versionId 版本ID
     * @return 标签列表
     */
    List<AssetVersionTag> getTagsByVersionId(String versionId);
}