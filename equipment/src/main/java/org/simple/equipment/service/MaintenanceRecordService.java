package org.simple.equipment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.simple.equipment.entity.MaintenanceRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 设备保养记录服务接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface MaintenanceRecordService extends IService<MaintenanceRecord> {

    /**
     * 分页查询保养记录
     *
     * @param page 分页对象
     * @param record 查询条件
     * @return 分页结果
     */
    IPage<MaintenanceRecord> selectMaintenanceRecordPage(Page<MaintenanceRecord> page, MaintenanceRecord record);

    /**
     * 创建保养记录
     *
     * @param record 保养记录
     * @return 是否成功
     */
    boolean createMaintenanceRecord(MaintenanceRecord record);

    /**
     * 更新保养记录
     *
     * @param record 保养记录
     * @return 是否成功
     */
    boolean updateMaintenanceRecord(MaintenanceRecord record);

    /**
     * 删除保养记录
     *
     * @param recordId 记录ID
     * @return 是否成功
     */
    boolean deleteMaintenanceRecord(String recordId);

    /**
     * 获取设备的保养记录
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录列表
     */
    List<MaintenanceRecord> getEquipmentMaintenanceRecords(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取保养记录统计信息
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getRecordStatistics(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 按保养类型统计保养记录
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> getRecordStatisticsByType(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 按时间统计保养记录
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param timeUnit 时间单位
     * @return 统计结果
     */
    List<Map<String, Object>> getRecordStatisticsByTime(String equipmentId, LocalDateTime startTime, LocalDateTime endTime, String timeUnit);

    /**
     * 获取保养记录的费用统计
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 费用统计
     */
    Map<String, Object> getRecordCostStatistics(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取保养记录的工时统计
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工时统计
     */
    Map<String, Object> getRecordHourStatistics(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取保养记录的质量统计
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 质量统计
     */
    Map<String, Object> getRecordQualityStatistics(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取用户的保养记录
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录列表
     */
    List<MaintenanceRecord> getUserMaintenanceRecords(String userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取部门的保养记录
     *
     * @param deptId 部门ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录列表
     */
    List<MaintenanceRecord> getDeptMaintenanceRecords(String deptId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取保养记录详情
     *
     * @param recordId 记录ID
     * @return 记录详情
     */
    Map<String, Object> getMaintenanceRecordDetails(String recordId);

    /**
     * 获取最近的保养记录
     *
     * @param equipmentId 设备ID
     * @param maintenanceType 保养类型
     * @param limit 限制数量
     * @return 记录列表
     */
    List<MaintenanceRecord> getRecentMaintenanceRecords(String equipmentId, String maintenanceType, Integer limit);

    /**
     * 获取保养记录的问题统计
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 问题统计
     */
    Map<String, Object> getRecordProblemStatistics(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取保养记录的外包统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 外包统计
     */
    Map<String, Object> getRecordOutsourcedStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 导出保养记录
     *
     * @param recordIds 记录ID列表
     * @return 导出结果
     */
    List<Map<String, Object>> exportMaintenanceRecords(List<String> recordIds);

    /**
     * 导入保养记录
     *
     * @param recordData 记录数据
     * @return 导入结果
     */
    Map<String, Object> importMaintenanceRecords(List<Map<String, Object>> recordData);

    /**
     * 生成保养报告
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 报告数据
     */
    Map<String, Object> generateMaintenanceReport(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 审核保养记录
     *
     * @param recordId 记录ID
     * @param reviewedBy 审核人ID
     * @param reviewResult 审核结果
     * @param reviewComments 审核意见
     * @return 是否成功
     */
    boolean reviewMaintenanceRecord(String recordId, String reviewedBy, String reviewResult, String reviewComments);

    /**
     * 获取保养记录的附件
     *
     * @param recordId 记录ID
     * @return 附件列表
     */
    List<Map<String, Object>> getRecordAttachments(String recordId);

    /**
     * 上传保养记录附件
     *
     * @param recordId 记录ID
     * @param attachments 附件信息
     * @return 是否成功
     */
    boolean uploadRecordAttachments(String recordId, List<Map<String, Object>> attachments);

    /**
     * 删除保养记录附件
     *
     * @param recordId 记录ID
     * @param attachmentId 附件ID
     * @return 是否成功
     */
    boolean deleteRecordAttachment(String recordId, String attachmentId);

    /**
     * 分析保养趋势
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 趋势分析
     */
    Map<String, Object> analyzeMaintenanceTrend(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取保养效果评估
     *
     * @param equipmentId 设备ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 效果评估
     */
    Map<String, Object> evaluateMaintenanceEffect(String equipmentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取保养建议
     *
     * @param equipmentId 设备ID
     * @return 保养建议
     */
    Map<String, Object> getMaintenanceSuggestions(String equipmentId);

    /**
     * 批量审核保养记录
     *
     * @param recordIds 记录ID列表
     * @param reviewedBy 审核人ID
     * @param reviewResult 审核结果
     * @param reviewComments 审核意见
     * @return 审核结果
     */
    Map<String, Object> batchReviewMaintenanceRecords(List<String> recordIds, String reviewedBy, String reviewResult, String reviewComments);
}