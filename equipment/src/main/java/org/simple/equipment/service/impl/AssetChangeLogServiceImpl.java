package org.simple.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simple.base.util.AuthUtil;
import org.simple.equipment.entity.AssetChangeLog;
import org.simple.equipment.mapper.AssetChangeLogMapper;
import org.simple.equipment.service.AssetChangeLogService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备档案变更记录服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class AssetChangeLogServiceImpl extends ServiceImpl<AssetChangeLogMapper, AssetChangeLog> implements AssetChangeLogService {

    @Override
    public List<AssetChangeLog> getChangeLogsByAssetId(String assetId) {
        QueryWrapper<AssetChangeLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("asset_id", assetId)
                .eq("tenant_id", AuthUtil.getTenantId())
                .orderByDesc("create_date");
        return list(queryWrapper);
    }

    @Override
    public List<AssetChangeLog> getChangeLogsByVersionId(String versionId) {
        QueryWrapper<AssetChangeLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("version_id", versionId)
                .eq("tenant_id", AuthUtil.getTenantId())
                .orderByAsc("field_name");
        return list(queryWrapper);
    }

    @Override
    public Map<String, Object> getChangeStatistics() {
        QueryWrapper<AssetChangeLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", AuthUtil.getTenantId());
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalChanges", count(queryWrapper));
        
        // 统计变更类型
        queryWrapper.select("change_type", "count(*) as count")
                .groupBy("change_type");
        List<Map<String, Object>> changeTypeStats = listMaps(queryWrapper);
        statistics.put("changeTypeStats", changeTypeStats);
        
        return statistics;
    }
}