package org.simple.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.simple.equipment.dto.TemplateAssociationRequest;
import org.simple.equipment.dto.TemplateRecommendationResponse;
import org.simple.equipment.entity.EquipmentTemplateAssociation;
import org.simple.equipment.service.EquipmentTemplateAssociationService;
import org.simple.equipment.service.IntelligentTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

/**
 * 智能模板关联服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IntelligentTemplateServiceImpl implements IntelligentTemplateService {

    private final EquipmentTemplateAssociationService templateAssociationService;

    @Override
    public TemplateRecommendationResponse recommendTemplate(String equipmentId, String userId) {
        log.info("为设备{}推荐模板", equipmentId);
        
        TemplateRecommendationResponse response = new TemplateRecommendationResponse();
        
        // 查找现有的主要关联
        EquipmentTemplateAssociation primaryAssociation = templateAssociationService.getOne(
            new LambdaQueryWrapper<EquipmentTemplateAssociation>()
                .eq(EquipmentTemplateAssociation::getEquipmentId, equipmentId)
                .eq(EquipmentTemplateAssociation::getAssociationType, 
                    EquipmentTemplateAssociation.AssociationType.PRIMARY)
                .orderByDesc(EquipmentTemplateAssociation::getPriority)
                .last("LIMIT 1"));
        
        if (primaryAssociation != null) {
            response.setPrimaryTemplateId(primaryAssociation.getTemplateId());
            response.setConfidence(0.9);
            response.setReason("基于已有关联");
            
            // 构建模板信息
            HashMap<String, Object> templateInfo = new HashMap<>();
            templateInfo.put("templateId", primaryAssociation.getTemplateId());
            templateInfo.put("associationType", primaryAssociation.getAssociationType());
            templateInfo.put("priority", primaryAssociation.getPriority());
            response.setPrimaryTemplateInfo(templateInfo);
        } else {
            // 智能推荐逻辑
            String recommendedTemplateId = intelligentRecommendation(equipmentId);
            if (recommendedTemplateId != null) {
                response.setPrimaryTemplateId(recommendedTemplateId);
                response.setConfidence(0.7);
                response.setReason("基于设备类型智能推荐");
            } else {
                response.setConfidence(0.0);
                response.setReason("无推荐模板");
            }
        }
        
        // 查找次要关联
        List<EquipmentTemplateAssociation> secondaryAssociations = templateAssociationService.list(
            new LambdaQueryWrapper<EquipmentTemplateAssociation>()
                .eq(EquipmentTemplateAssociation::getEquipmentId, equipmentId)
                .eq(EquipmentTemplateAssociation::getAssociationType, 
                    EquipmentTemplateAssociation.AssociationType.SECONDARY)
                .orderByDesc(EquipmentTemplateAssociation::getPriority));
        
        List<TemplateRecommendationResponse.RecommendedTemplate> secondaryTemplates = new ArrayList<>();
        for (EquipmentTemplateAssociation association : secondaryAssociations) {
            TemplateRecommendationResponse.RecommendedTemplate template = 
                new TemplateRecommendationResponse.RecommendedTemplate();
            template.setTemplateId(association.getTemplateId());
            template.setWeight(association.getPriority() / 100.0);
            template.setReason("次要关联模板");
            secondaryTemplates.add(template);
        }
        response.setSecondaryTemplates(secondaryTemplates);
        
        return response;
    }

    @Override
    @Transactional
    public Boolean autoAssociateTemplate(TemplateAssociationRequest request) {
        log.info("自动关联设备{}与模板{}", request.getEquipmentId(), request.getTemplateId());
        
        try {
            // 检查是否已存在关联
            EquipmentTemplateAssociation existing = templateAssociationService.getOne(
                new LambdaQueryWrapper<EquipmentTemplateAssociation>()
                    .eq(EquipmentTemplateAssociation::getEquipmentId, request.getEquipmentId())
                    .eq(EquipmentTemplateAssociation::getTemplateId, request.getTemplateId()));
            
            if (existing != null) {
                log.info("设备{}与模板{}已存在关联", request.getEquipmentId(), request.getTemplateId());
                return true;
            }
            
            // 创建新关联
            EquipmentTemplateAssociation association = new EquipmentTemplateAssociation();
            association.setId(UUID.randomUUID().toString());
            association.setEquipmentId(request.getEquipmentId());
            association.setTemplateId(request.getTemplateId());
            association.setAssociationType(request.getAssociationType());
            association.setAutoAssigned(request.getAutoAssigned());
            association.setAssignmentRule(request.getAssignmentRule());
            association.setPriority(request.getPriority());
            association.setEffectiveFrom(request.getEffectiveFrom() != null ? 
                request.getEffectiveFrom() : LocalDateTime.now());
            association.setEffectiveTo(request.getEffectiveTo());
            
            templateAssociationService.save(association);
            
            log.info("设备{}与模板{}关联成功", request.getEquipmentId(), request.getTemplateId());
            return true;
            
        } catch (Exception e) {
            log.error("自动关联失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public void updateAssociationPriority(String equipmentId, String templateId, Integer priority) {
        EquipmentTemplateAssociation association = templateAssociationService.getOne(
            new LambdaQueryWrapper<EquipmentTemplateAssociation>()
                .eq(EquipmentTemplateAssociation::getEquipmentId, equipmentId)
                .eq(EquipmentTemplateAssociation::getTemplateId, templateId));
        
        if (association != null) {
            association.setPriority(priority);
            templateAssociationService.updateById(association);
            log.info("更新设备{}模板{}优先级为{}", equipmentId, templateId, priority);
        }
    }

    @Override
    public String getRecommendedTemplateByType(String equipmentType, String departmentId) {
        // 这里可以实现基于设备类型和科室的智能推荐算法
        // 当前返回简单的推荐逻辑，可根据实际需求扩展
        
        // 基于设备类型的默认推荐
        switch (equipmentType.toLowerCase()) {
            case "monitor":
            case "监护仪":
                return "MONITOR_TEMPLATE_001";
            case "ventilator":
            case "呼吸机":
                return "VENTILATOR_TEMPLATE_001";
            case "infusion_pump":
            case "输液泵":
                return "INFUSION_PUMP_TEMPLATE_001";
            case "defibrillator":
            case "除颤仪":
                return "DEFIBRILLATOR_TEMPLATE_001";
            default:
                return "DEFAULT_TEMPLATE_001";
        }
    }

    /**
     * 智能推荐算法
     * 
     * @param equipmentId 设备ID
     * @return 推荐的模板ID
     */
    private String intelligentRecommendation(String equipmentId) {
        // 这里实现智能推荐算法
        // 1. 基于设备类型
        // 2. 基于历史使用数据
        // 3. 基于同类设备的关联情况
        // 4. 基于用户行为模式
        
        // 当前简化实现，返回默认推荐
        return "DEFAULT_TEMPLATE_001";
    }
}