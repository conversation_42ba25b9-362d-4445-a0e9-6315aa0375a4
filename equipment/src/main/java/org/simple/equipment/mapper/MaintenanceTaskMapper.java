package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.MaintenanceTask;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 设备保养任务 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Mapper
public interface MaintenanceTaskMapper extends BaseMapper<MaintenanceTask> {

    /**
     * 查询用户的保养任务
     *
     * @param userId 用户ID
     * @param status 任务状态
     * @return 任务列表
     */
    List<MaintenanceTask> selectUserTasks(@Param("userId") String userId, @Param("status") String status);

    /**
     * 查询部门的保养任务
     *
     * @param deptId 部门ID
     * @param status 任务状态
     * @return 任务列表
     */
    List<MaintenanceTask> selectDeptTasks(@Param("deptId") String deptId, @Param("status") String status);

    /**
     * 查询即将到期的保养任务
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 任务列表
     */
    List<MaintenanceTask> selectUpcomingTasks(
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询超时的保养任务
     *
     * @param currentTime 当前时间
     * @return 任务列表
     */
    List<MaintenanceTask> selectOverdueTasks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询保养任务统计信息
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 统计信息
     */
    Map<String, Object> selectTaskStatistics(
        @Param("userId") String userId,
        @Param("deptId") String deptId
    );

    /**
     * 批量更新超时任务状态
     *
     * @param currentTime 当前时间
     * @return 影响行数
     */
    int batchUpdateOverdueTasks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询保养任务执行详情
     *
     * @param taskId 任务ID
     * @return 执行详情
     */
    Map<String, Object> selectTaskExecutionDetails(@Param("taskId") String taskId);

    /**
     * 按时间范围统计保养任务
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 统计结果
     */
    List<Map<String, Object>> selectTaskStatisticsByTime(
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime,
        @Param("userId") String userId,
        @Param("deptId") String deptId
    );

    /**
     * 按状态统计保养任务
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 统计结果
     */
    List<Map<String, Object>> selectTaskStatisticsByStatus(
        @Param("userId") String userId,
        @Param("deptId") String deptId
    );

    /**
     * 按保养类型统计保养任务
     *
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 统计结果
     */
    List<Map<String, Object>> selectTaskStatisticsByType(
        @Param("userId") String userId,
        @Param("deptId") String deptId
    );

    /**
     * 查询设备的保养任务
     *
     * @param equipmentId 设备ID
     * @param status 任务状态
     * @return 任务列表
     */
    List<MaintenanceTask> selectTasksByEquipment(@Param("equipmentId") String equipmentId, @Param("status") String status);

    /**
     * 查询保养任务的费用统计
     *
     * @param taskId 任务ID
     * @return 费用统计
     */
    Map<String, Object> selectTaskCostStatistics(@Param("taskId") String taskId);

    /**
     * 查询保养任务的工时统计
     *
     * @param taskId 任务ID
     * @return 工时统计
     */
    Map<String, Object> selectTaskHourStatistics(@Param("taskId") String taskId);

    /**
     * 查询保养任务的完成情况
     *
     * @param taskId 任务ID
     * @return 完成情况
     */
    Map<String, Object> selectTaskCompletionStatus(@Param("taskId") String taskId);

    /**
     * 查询保养任务的质量评价
     *
     * @param taskId 任务ID
     * @return 质量评价
     */
    Map<String, Object> selectTaskQualityEvaluation(@Param("taskId") String taskId);

    /**
     * 查询保养任务的外包情况
     *
     * @param isOutsourced 是否外包
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 外包任务列表
     */
    List<MaintenanceTask> selectOutsourcedTasks(
        @Param("isOutsourced") Integer isOutsourced,
        @Param("userId") String userId,
        @Param("deptId") String deptId
    );
}