package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.SpecTemplate;

import java.util.List;
import java.util.Map;

/**
 * 设备规格参数模板Mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper
public interface SpecTemplateMapper extends BaseMapper<SpecTemplate> {

    /**
     * 查询模板列表
     *
     * @param specTemplate 查询条件
     * @return 模板列表
     */
    List<SpecTemplate> selectSpecTemplateList(@Param("specTemplate") SpecTemplate specTemplate);

    /**
     * 根据分类和类型查询模板
     *
     * @param categoryId 分类ID
     * @param typeId 类型ID
     * @param tenantId 租户ID
     * @return 模板列表
     */
    List<SpecTemplate> selectByCategory(@Param("categoryId") String categoryId,
                                        @Param("typeId") String typeId,
                                        @Param("tenantId") String tenantId);

    /**
     * 根据编码查询模板
     *
     * @param templateCode 模板编码
     * @param tenantId 租户ID
     * @param excludeId 排除的ID
     * @return 模板
     */
    SpecTemplate selectByTemplateCode(@Param("templateCode") String templateCode,
                                      @Param("tenantId") String tenantId,
                                      @Param("excludeId") String excludeId);

    /**
     * 查询有效的模板列表
     *
     * @param tenantId 租户ID
     * @return 模板列表
     */
    List<SpecTemplate> selectEnabledList(@Param("tenantId") String tenantId);

    /**
     * 更新模板使用次数
     *
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @return 更新数量
     */
    int updateUsageCount(@Param("templateId") String templateId,
                         @Param("tenantId") String tenantId);

    /**
     * 查询热门模板
     *
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 热门模板列表
     */
    List<SpecTemplate> selectPopularTemplates(@Param("tenantId") String tenantId,
                                              @Param("limit") int limit);

    /**
     * 查询模板使用统计
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectUsageStatistics(@Param("tenantId") String tenantId);

    /**
     * 查询模板详情（包含参数信息）
     *
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @return 模板详情
     */
    Map<String, Object> selectTemplateDetail(@Param("templateId") String templateId,
                                             @Param("tenantId") String tenantId);
}