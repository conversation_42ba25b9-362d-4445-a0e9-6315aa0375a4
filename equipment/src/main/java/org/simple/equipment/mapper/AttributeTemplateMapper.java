package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.AttributeTemplate;

import java.util.List;
import java.util.Map;

/**
 * 设备属性模板 Mapper 接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper
public interface AttributeTemplateMapper extends BaseMapper<AttributeTemplate> {

    /**
     * 查询有效的模板列表
     *
     * @param tenantId 租户ID
     * @return 模板列表
     */
    List<AttributeTemplate> selectActiveList(@Param("tenantId") String tenantId);

    /**
     * 根据设备分类和类型查询模板
     *
     * @param categoryId 分类ID
     * @param typeId 类型ID
     * @param tenantId 租户ID
     * @return 模板列表
     */
    List<AttributeTemplate> selectByCategory(@Param("categoryId") String categoryId,
                                             @Param("typeId") String typeId,
                                             @Param("tenantId") String tenantId);

    /**
     * 根据模板编码查询模板
     *
     * @param templateCode 模板编码
     * @param tenantId 租户ID
     * @param excludeId 排除的ID
     * @return 模板信息
     */
    AttributeTemplate selectByTemplateCode(@Param("templateCode") String templateCode,
                                           @Param("tenantId") String tenantId,
                                           @Param("excludeId") String excludeId);

    /**
     * 查询模板列表
     *
     * @param template 查询条件
     * @return 模板列表
     */
    List<AttributeTemplate> selectTemplateList(AttributeTemplate template);

    /**
     * 查询模板详情（包含字段信息）
     *
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @return 模板详情
     */
    Map<String, Object> selectTemplateDetail(@Param("templateId") String templateId,
                                             @Param("tenantId") String tenantId);

    /**
     * 查询子模板列表
     *
     * @param parentId 父模板ID
     * @param tenantId 租户ID
     * @return 子模板列表
     */
    List<AttributeTemplate> selectChildTemplates(@Param("parentId") String parentId,
                                                 @Param("tenantId") String tenantId);

    /**
     * 更新模板使用次数
     *
     * @param templateId 模板ID
     * @param tenantId 租户ID
     * @return 影响行数
     */
    int updateUsageCount(@Param("templateId") String templateId,
                         @Param("tenantId") String tenantId);

    /**
     * 查询模板使用统计
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectUsageStatistics(@Param("tenantId") String tenantId);

    /**
     * 查询最大排序顺序
     *
     * @param tenantId 租户ID
     * @return 最大排序顺序
     */
    Integer selectMaxSortOrder(@Param("tenantId") String tenantId);
}