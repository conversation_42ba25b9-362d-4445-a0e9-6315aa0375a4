package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.AssetVersion;

import java.util.List;
import java.util.Map;

/**
 * 设备档案版本Mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper
public interface AssetVersionMapper extends BaseMapper<AssetVersion> {

    /**
     * 查询设备的版本列表
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 版本列表
     */
    List<AssetVersion> selectVersionsByAssetId(@Param("assetId") String assetId,
                                               @Param("tenantId") String tenantId);

    /**
     * 查询设备的当前版本
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 当前版本
     */
    AssetVersion selectCurrentVersion(@Param("assetId") String assetId,
                                      @Param("tenantId") String tenantId);

    /**
     * 查询版本详情（包含标签和变更记录）
     *
     * @param versionId 版本ID
     * @param tenantId 租户ID
     * @return 版本详情
     */
    Map<String, Object> selectVersionDetail(@Param("versionId") String versionId,
                                            @Param("tenantId") String tenantId);

    /**
     * 查询待审批的版本列表
     *
     * @param approver 审批人
     * @param tenantId 租户ID
     * @return 待审批版本列表
     */
    List<Map<String, Object>> selectPendingApprovalVersions(@Param("approver") String approver,
                                                             @Param("tenantId") String tenantId);

    /**
     * 查询版本对比数据
     *
     * @param fromVersionId 源版本ID
     * @param toVersionId 目标版本ID
     * @param tenantId 租户ID
     * @return 对比数据
     */
    Map<String, Object> selectVersionCompare(@Param("fromVersionId") String fromVersionId,
                                             @Param("toVersionId") String toVersionId,
                                             @Param("tenantId") String tenantId);

    /**
     * 更新版本状态为当前版本
     *
     * @param versionId 版本ID
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 更新数量
     */
    int updateCurrentVersion(@Param("versionId") String versionId,
                             @Param("assetId") String assetId,
                             @Param("tenantId") String tenantId);

    /**
     * 清除设备的当前版本标识
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 更新数量
     */
    int clearCurrentVersion(@Param("assetId") String assetId,
                            @Param("tenantId") String tenantId);

    /**
     * 查询版本统计信息
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    Map<String, Object> selectVersionStatistics(@Param("tenantId") String tenantId);

    /**
     * 批量归档版本
     *
     * @param versionIds 版本ID列表
     * @param tenantId 租户ID
     * @return 更新数量
     */
    int batchArchiveVersions(@Param("versionIds") List<String> versionIds,
                             @Param("tenantId") String tenantId);

    /**
     * 查询需要清理的历史版本
     *
     * @param retentionDays 保留天数
     * @param tenantId 租户ID
     * @return 历史版本列表
     */
    List<AssetVersion> selectExpiredVersions(@Param("retentionDays") int retentionDays,
                                             @Param("tenantId") String tenantId);
}