package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.AssetFieldValue;

import java.util.List;
import java.util.Map;

/**
 * 设备自定义字段值 Mapper 接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper
public interface AssetFieldValueMapper extends BaseMapper<AssetFieldValue> {

    /**
     * 根据设备ID查询字段值
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 字段值列表
     */
    List<AssetFieldValue> selectByAssetId(@Param("assetId") String assetId,
                                          @Param("tenantId") String tenantId);

    /**
     * 根据字段ID查询字段值
     *
     * @param fieldId 字段ID
     * @param tenantId 租户ID
     * @return 字段值列表
     */
    List<AssetFieldValue> selectByFieldId(@Param("fieldId") String fieldId,
                                          @Param("tenantId") String tenantId);

    /**
     * 查询设备的自定义字段值（包含字段配置信息）
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 字段值Map
     */
    List<Map<String, Object>> selectAssetFieldValues(@Param("assetId") String assetId,
                                                     @Param("tenantId") String tenantId);

    /**
     * 批量保存字段值
     *
     * @param fieldValues 字段值列表
     * @return 影响行数
     */
    int insertBatch(@Param("fieldValues") List<AssetFieldValue> fieldValues);

    /**
     * 批量删除设备的字段值
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 影响行数
     */
    int deleteByAssetId(@Param("assetId") String assetId,
                        @Param("tenantId") String tenantId);

    /**
     * 批量删除字段相关的值
     *
     * @param fieldId 字段ID
     * @param tenantId 租户ID
     * @return 影响行数
     */
    int deleteByFieldId(@Param("fieldId") String fieldId,
                        @Param("tenantId") String tenantId);
}