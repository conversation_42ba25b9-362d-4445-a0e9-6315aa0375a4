package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.Tag;

import java.util.List;
import java.util.Map;

/**
 * 设备标签 Mapper 接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper
public interface TagMapper extends BaseMapper<Tag> {

    /**
     * 查询有效的标签列表
     *
     * @param tenantId 租户ID
     * @return 标签列表
     */
    List<Tag> selectActiveList(@Param("tenantId") String tenantId);

    /**
     * 根据分类查询标签
     *
     * @param categoryId 分类ID
     * @param tenantId 租户ID
     * @return 标签列表
     */
    List<Tag> selectByCategory(@Param("categoryId") String categoryId,
                               @Param("tenantId") String tenantId);

    /**
     * 根据标签编码查询标签
     *
     * @param tagCode 标签编码
     * @param tenantId 租户ID
     * @param excludeId 排除的ID
     * @return 标签信息
     */
    Tag selectByTagCode(@Param("tagCode") String tagCode,
                        @Param("tenantId") String tenantId,
                        @Param("excludeId") String excludeId);

    /**
     * 查询标签列表
     *
     * @param tag 查询条件
     * @return 标签列表
     */
    List<Tag> selectTagList(Tag tag);

    /**
     * 根据标签类型查询标签
     *
     * @param tagType 标签类型
     * @param tenantId 租户ID
     * @return 标签列表
     */
    List<Tag> selectByTagType(@Param("tagType") String tagType,
                              @Param("tenantId") String tenantId);

    /**
     * 查询热门标签
     *
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 标签列表
     */
    List<Tag> selectPopularTags(@Param("tenantId") String tenantId,
                                @Param("limit") int limit);

    /**
     * 更新标签使用次数
     *
     * @param tagId 标签ID
     * @param tenantId 租户ID
     * @return 影响行数
     */
    int updateUsageCount(@Param("tagId") String tagId,
                         @Param("tenantId") String tenantId);

    /**
     * 查询标签使用统计
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectUsageStatistics(@Param("tenantId") String tenantId);

    /**
     * 查询最大排序顺序
     *
     * @param categoryId 分类ID
     * @param tenantId 租户ID
     * @return 最大排序顺序
     */
    Integer selectMaxSortOrder(@Param("categoryId") String categoryId,
                               @Param("tenantId") String tenantId);

    /**
     * 搜索标签
     *
     * @param keyword 关键词
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 标签列表
     */
    List<Tag> searchTags(@Param("keyword") String keyword,
                         @Param("tenantId") String tenantId,
                         @Param("limit") int limit);
}