package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.AssetSpec;

import java.util.List;
import java.util.Map;

/**
 * 设备规格参数值Mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper
public interface AssetSpecMapper extends BaseMapper<AssetSpec> {

    /**
     * 查询设备的规格参数
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 参数列表
     */
    List<AssetSpec> selectByAssetId(@Param("assetId") String assetId,
                                    @Param("tenantId") String tenantId);

    /**
     * 查询设备的规格参数详情
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 参数详情列表
     */
    List<Map<String, Object>> selectAssetSpecDetails(@Param("assetId") String assetId,
                                                      @Param("tenantId") String tenantId);

    /**
     * 根据参数查询设备列表
     *
     * @param paramId 参数ID
     * @param paramValue 参数值
     * @param tenantId 租户ID
     * @return 设备列表
     */
    List<Map<String, Object>> selectAssetsByParam(@Param("paramId") String paramId,
                                                   @Param("paramValue") String paramValue,
                                                   @Param("tenantId") String tenantId);

    /**
     * 根据多个参数查询设备
     *
     * @param params 参数条件
     * @param tenantId 租户ID
     * @return 设备列表
     */
    List<Map<String, Object>> selectAssetsByParams(@Param("params") List<Map<String, Object>> params,
                                                    @Param("tenantId") String tenantId);

    /**
     * 批量删除设备参数
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    int deleteByAssetId(@Param("assetId") String assetId,
                        @Param("tenantId") String tenantId);

    /**
     * 查询参数对比数据
     *
     * @param assetIds 设备ID列表
     * @param tenantId 租户ID
     * @return 对比数据
     */
    List<Map<String, Object>> selectCompareData(@Param("assetIds") List<String> assetIds,
                                                 @Param("tenantId") String tenantId);

    /**
     * 查询相似设备（基于规格参数）
     *
     * @param assetId 设备ID
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 相似设备列表
     */
    List<Map<String, Object>> selectSimilarAssets(@Param("assetId") String assetId,
                                                   @Param("tenantId") String tenantId,
                                                   @Param("limit") int limit);

    /**
     * 验证参数值
     *
     * @param assetId 设备ID
     * @param paramId 参数ID
     * @param verifiedBy 验证人
     * @param tenantId 租户ID
     * @return 更新数量
     */
    int verifyParam(@Param("assetId") String assetId,
                    @Param("paramId") String paramId,
                    @Param("verifiedBy") String verifiedBy,
                    @Param("tenantId") String tenantId);

    /**
     * 查询规格参数统计
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    Map<String, Object> selectSpecStatistics(@Param("tenantId") String tenantId);

    /**
     * 查询待验证的参数列表
     *
     * @param tenantId 租户ID
     * @return 待验证参数列表
     */
    List<Map<String, Object>> selectUnverifiedParams(@Param("tenantId") String tenantId);
}