package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.PatrolPlan;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 设备巡检计划 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Mapper
public interface PatrolPlanMapper extends BaseMapper<PatrolPlan> {

    /**
     * 查询即将到期的计划
     *
     * @param expireTime 到期时间
     * @return 计划列表
     */
    List<PatrolPlan> selectExpiringPlans(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 查询计划统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> selectPlanStatistics();

    /**
     * 按类型统计计划数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectPlanStatisticsByType();

    /**
     * 按周期统计计划数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectPlanStatisticsByCycle();

    /**
     * 按部门统计计划数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectPlanStatisticsByDept();

    /**
     * 查询需要生成任务的计划
     *
     * @param currentTime 当前时间
     * @return 计划列表
     */
    List<PatrolPlan> selectPlansForTaskGeneration(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询计划执行情况
     *
     * @param planId 计划ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行情况
     */
    Map<String, Object> selectPlanExecutionStatus(
        @Param("planId") String planId,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );
}
