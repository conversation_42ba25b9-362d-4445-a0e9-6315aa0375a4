package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.MaintenanceTemplate;

import java.util.List;
import java.util.Map;

/**
 * 设备保养模板 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Mapper
public interface MaintenanceTemplateMapper extends BaseMapper<MaintenanceTemplate> {

    /**
     * 查询设备类型的保养模板
     *
     * @param equipmentTypeId 设备类型ID
     * @param maintenanceType 保养类型
     * @return 模板列表
     */
    List<MaintenanceTemplate> selectTemplatesByEquipmentType(
        @Param("equipmentTypeId") String equipmentTypeId,
        @Param("maintenanceType") String maintenanceType
    );

    /**
     * 查询默认保养模板
     *
     * @param equipmentTypeId 设备类型ID
     * @param maintenanceType 保养类型
     * @return 模板
     */
    MaintenanceTemplate selectDefaultTemplate(
        @Param("equipmentTypeId") String equipmentTypeId,
        @Param("maintenanceType") String maintenanceType
    );

    /**
     * 查询保养模板统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> selectTemplateStatistics();

    /**
     * 按保养类型统计模板数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectTemplateStatisticsByType();

    /**
     * 按设备类型统计模板数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectTemplateStatisticsByEquipmentType();

    /**
     * 按创建部门统计模板数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> selectTemplateStatisticsByDept();

    /**
     * 查询模板使用情况
     *
     * @param templateId 模板ID
     * @return 使用情况
     */
    Map<String, Object> selectTemplateUsageStatistics(@Param("templateId") String templateId);

    /**
     * 查询公开的保养模板
     *
     * @param equipmentTypeId 设备类型ID
     * @param maintenanceType 保养类型
     * @return 模板列表
     */
    List<MaintenanceTemplate> selectPublicTemplates(
        @Param("equipmentTypeId") String equipmentTypeId,
        @Param("maintenanceType") String maintenanceType
    );

    /**
     * 查询模板详情
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    Map<String, Object> selectTemplateDetails(@Param("templateId") String templateId);

    /**
     * 查询需要审核的模板
     *
     * @param reviewStatus 审核状态
     * @return 模板列表
     */
    List<MaintenanceTemplate> selectTemplatesForReview(@Param("reviewStatus") Integer reviewStatus);

    /**
     * 查询热门保养模板
     *
     * @param limit 限制数量
     * @return 模板列表
     */
    List<MaintenanceTemplate> selectPopularTemplates(@Param("limit") Integer limit);

    /**
     * 查询最新保养模板
     *
     * @param limit 限制数量
     * @return 模板列表
     */
    List<MaintenanceTemplate> selectLatestTemplates(@Param("limit") Integer limit);

    /**
     * 查询相似保养模板
     *
     * @param templateId 模板ID
     * @param limit 限制数量
     * @return 模板列表
     */
    List<MaintenanceTemplate> selectSimilarTemplates(
        @Param("templateId") String templateId,
        @Param("limit") Integer limit
    );

    /**
     * 更新模板使用次数
     *
     * @param templateId 模板ID
     * @return 影响行数
     */
    int updateTemplateUsageCount(@Param("templateId") String templateId);

    /**
     * 批量更新模板状态
     *
     * @param templateIds 模板ID列表
     * @param status 状态
     * @return 影响行数
     */
    int batchUpdateTemplateStatus(
        @Param("templateIds") List<String> templateIds,
        @Param("status") Integer status
    );
}