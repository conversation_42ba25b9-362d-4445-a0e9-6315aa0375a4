package org.simple.equipment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.simple.equipment.entity.SpecParam;

import java.util.List;
import java.util.Map;

/**
 * 设备规格参数定义Mapper接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Mapper
public interface SpecParamMapper extends BaseMapper<SpecParam> {

    /**
     * 查询参数列表
     *
     * @param specParam 查询条件
     * @return 参数列表
     */
    List<SpecParam> selectSpecParamList(@Param("specParam") SpecParam specParam);

    /**
     * 根据参数组查询参数列表
     *
     * @param groupId 参数组ID
     * @param tenantId 租户ID
     * @return 参数列表
     */
    List<SpecParam> selectByGroupId(@Param("groupId") String groupId,
                                    @Param("tenantId") String tenantId);

    /**
     * 根据设备分类和类型查询参数
     *
     * @param categoryId 分类ID
     * @param typeId 类型ID
     * @param tenantId 租户ID
     * @return 参数列表
     */
    List<SpecParam> selectByCategory(@Param("categoryId") String categoryId,
                                     @Param("typeId") String typeId,
                                     @Param("tenantId") String tenantId);

    /**
     * 根据编码查询参数
     *
     * @param paramCode 参数编码
     * @param tenantId 租户ID
     * @param excludeId 排除的ID
     * @return 参数
     */
    SpecParam selectByParamCode(@Param("paramCode") String paramCode,
                                @Param("tenantId") String tenantId,
                                @Param("excludeId") String excludeId);

    /**
     * 查询可搜索的参数列表
     *
     * @param tenantId 租户ID
     * @return 参数列表
     */
    List<SpecParam> selectSearchableParams(@Param("tenantId") String tenantId);

    /**
     * 查询可比较的参数列表
     *
     * @param tenantId 租户ID
     * @return 参数列表
     */
    List<SpecParam> selectComparableParams(@Param("tenantId") String tenantId);

    /**
     * 查询参数详情（包含分组信息）
     *
     * @param paramId 参数ID
     * @param tenantId 租户ID
     * @return 参数详情
     */
    Map<String, Object> selectParamDetail(@Param("paramId") String paramId,
                                          @Param("tenantId") String tenantId);

    /**
     * 批量更新排序
     *
     * @param params 参数列表
     * @param tenantId 租户ID
     * @return 更新数量
     */
    int batchUpdateSort(@Param("params") List<SpecParam> params,
                        @Param("tenantId") String tenantId);

    /**
     * 查询参数使用统计
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectUsageStatistics(@Param("tenantId") String tenantId);
}