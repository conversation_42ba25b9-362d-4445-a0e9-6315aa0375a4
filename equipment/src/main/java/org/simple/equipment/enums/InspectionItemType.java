package org.simple.equipment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点检项目类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Getter
@AllArgsConstructor
public enum InspectionItemType {

    CHECK("CHECK", "检查"),
    MEASURE("MEASURE", "测量"),
    RECORD("RECORD", "记录"),
    MAINTAIN("MAINTAIN", "维护"),
    CLEAN("CLEAN", "清洁");

    private final String code;
    private final String name;

    public static InspectionItemType getByCode(String code) {
        for (InspectionItemType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}