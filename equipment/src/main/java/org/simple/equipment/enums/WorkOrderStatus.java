package org.simple.equipment.enums;

/**
 * 工单状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public enum WorkOrderStatus {

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消");

    private final String code;
    private final String description;

    WorkOrderStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static WorkOrderStatus fromCode(String code) {
        for (WorkOrderStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的工单状态编码: " + code);
    }

    /**
     * 检查状态是否可以转换
     *
     * @param from 源状态
     * @param to 目标状态
     * @return 是否可以转换
     */
    public static boolean canTransition(WorkOrderStatus from, WorkOrderStatus to) {
        switch (from) {
            case PENDING:
                return to == PROCESSING || to == CANCELLED;
            case PROCESSING:
                return to == COMPLETED || to == CANCELLED;
            case COMPLETED:
            case CANCELLED:
                return false;
            default:
                return false;
        }
    }
}