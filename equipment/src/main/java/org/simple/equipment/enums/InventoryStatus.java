package org.simple.equipment.enums;

/**
 * 设备盘点状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public enum InventoryStatus {
    
    /**
     * 计划中
     */
    PLANNED("PLANNED", "计划中"),
    
    /**
     * 准备中
     */
    PREPARING("PREPARING", "准备中"),
    
    /**
     * 进行中
     */
    IN_PROGRESS("IN_PROGRESS", "进行中"),
    
    /**
     * 已暂停
     */
    PAUSED("PAUSED", "已暂停"),
    
    /**
     * 待复盘
     */
    PENDING_REVIEW("PENDING_REVIEW", "待复盘"),
    
    /**
     * 复盘中
     */
    REVIEWING("REVIEWING", "复盘中"),
    
    /**
     * 待调整
     */
    PENDING_ADJUSTMENT("PENDING_ADJUSTMENT", "待调整"),
    
    /**
     * 调整中
     */
    ADJUSTING("ADJUSTING", "调整中"),
    
    /**
     * 待确认
     */
    PENDING_CONFIRM("PENDING_CONFIRM", "待确认"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),
    
    /**
     * 已关闭
     */
    CLOSED("CLOSED", "已关闭");
    
    private final String code;
    private final String description;
    
    InventoryStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static InventoryStatus getByCode(String code) {
        for (InventoryStatus status : InventoryStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}