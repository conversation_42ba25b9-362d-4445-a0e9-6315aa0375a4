package org.simple.equipment.enums;

/**
 * 设备维修状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public enum RepairStatus {
    
    /**
     * 待报修
     */
    PENDING_REPORT("PENDING_REPORT", "待报修"),
    
    /**
     * 已报修
     */
    REPORTED("REPORTED", "已报修"),
    
    /**
     * 待分配
     */
    PENDING_ASSIGN("PENDING_ASSIGN", "待分配"),
    
    /**
     * 已分配
     */
    ASSIGNED("ASSIGNED", "已分配"),
    
    /**
     * 待确认
     */
    PENDING_CONFIRM("PENDING_CONFIRM", "待确认"),
    
    /**
     * 已确认
     */
    CONFIRMED("CONFIRMED", "已确认"),
    
    /**
     * 维修中
     */
    IN_REPAIR("IN_REPAIR", "维修中"),
    
    /**
     * 待验收
     */
    PENDING_ACCEPTANCE("PENDING_ACCEPTANCE", "待验收"),
    
    /**
     * 验收通过
     */
    ACCEPTED("ACCEPTED", "验收通过"),
    
    /**
     * 验收不通过
     */
    REJECTED("REJECTED", "验收不通过"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),
    
    /**
     * 已关闭
     */
    CLOSED("CLOSED", "已关闭");
    
    private final String code;
    private final String description;
    
    RepairStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static RepairStatus getByCode(String code) {
        for (RepairStatus status : RepairStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}