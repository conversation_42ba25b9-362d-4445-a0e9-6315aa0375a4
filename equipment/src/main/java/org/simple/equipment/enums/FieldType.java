package org.simple.equipment.enums;

import lombok.Getter;

/**
 * 字段类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
@Getter
public enum FieldType {

    /**
     * 文本类型
     */
    TEXT("text", "文本"),

    /**
     * 数字类型
     */
    NUMBER("number", "数字"),

    /**
     * 日期类型
     */
    DATE("date", "日期"),

    /**
     * 日期时间类型
     */
    DATETIME("datetime", "日期时间"),

    /**
     * 选择类型
     */
    SELECT("select", "选择"),

    /**
     * 多选类型
     */
    CHECKBOX("checkbox", "多选"),

    /**
     * 文件类型
     */
    FILE("file", "文件"),

    /**
     * 多行文本
     */
    TEXTAREA("textarea", "多行文本"),

    /**
     * 单选按钮
     */
    RADIO("radio", "单选按钮"),

    /**
     * 开关类型
     */
    SWITCH("switch", "开关");

    private final String code;
    private final String name;

    FieldType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     */
    public static FieldType getByCode(String code) {
        for (FieldType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}