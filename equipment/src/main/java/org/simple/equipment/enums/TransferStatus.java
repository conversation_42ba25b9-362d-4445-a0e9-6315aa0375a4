package org.simple.equipment.enums;

/**
 * 设备调拨状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public enum TransferStatus {
    
    /**
     * 待申请
     */
    PENDING_APPLICATION("PENDING_APPLICATION", "待申请"),
    
    /**
     * 已申请
     */
    APPLIED("APPLIED", "已申请"),
    
    /**
     * 待审批
     */
    PENDING_APPROVAL("PENDING_APPROVAL", "待审批"),
    
    /**
     * 审批中
     */
    APPROVING("APPROVING", "审批中"),
    
    /**
     * 审批通过
     */
    APPROVED("APPROVED", "审批通过"),
    
    /**
     * 审批拒绝
     */
    REJECTED("REJECTED", "审批拒绝"),
    
    /**
     * 待调出
     */
    PENDING_TRANSFER_OUT("PENDING_TRANSFER_OUT", "待调出"),
    
    /**
     * 调出中
     */
    TRANSFERRING_OUT("TRANSFERRING_OUT", "调出中"),
    
    /**
     * 已调出
     */
    TRANSFERRED_OUT("TRANSFERRED_OUT", "已调出"),
    
    /**
     * 待接收
     */
    PENDING_RECEIVE("PENDING_RECEIVE", "待接收"),
    
    /**
     * 已接收
     */
    RECEIVED("RECEIVED", "已接收"),
    
    /**
     * 接收确认
     */
    RECEIVE_CONFIRMED("RECEIVE_CONFIRMED", "接收确认"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),
    
    /**
     * 异常
     */
    ABNORMAL("ABNORMAL", "异常");
    
    private final String code;
    private final String description;
    
    TransferStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static TransferStatus getByCode(String code) {
        for (TransferStatus status : TransferStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}