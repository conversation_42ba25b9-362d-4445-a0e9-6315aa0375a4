package org.simple.equipment.enums;

/**
 * 设备调拨类型枚举
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public enum TransferType {
    
    /**
     * 部门调拨
     */
    DEPARTMENT_TRANSFER("DEPARTMENT_TRANSFER", "部门调拨"),
    
    /**
     * 位置调拨
     */
    LOCATION_TRANSFER("LOCATION_TRANSFER", "位置调拨"),
    
    /**
     * 人员调拨
     */
    PERSONNEL_TRANSFER("PERSONNEL_TRANSFER", "人员调拨"),
    
    /**
     * 项目调拨
     */
    PROJECT_TRANSFER("PROJECT_TRANSFER", "项目调拨"),
    
    /**
     * 临时调拨
     */
    TEMPORARY_TRANSFER("TEMPORARY_TRANSFER", "临时调拨"),
    
    /**
     * 永久调拨
     */
    PERMANENT_TRANSFER("PERMANENT_TRANSFER", "永久调拨"),
    
    /**
     * 借用调拨
     */
    BORROW_TRANSFER("BORROW_TRANSFER", "借用调拨"),
    
    /**
     * 归还调拨
     */
    RETURN_TRANSFER("RETURN_TRANSFER", "归还调拨"),
    
    /**
     * 报废调拨
     */
    SCRAP_TRANSFER("SCRAP_TRANSFER", "报废调拨"),
    
    /**
     * 维修调拨
     */
    REPAIR_TRANSFER("REPAIR_TRANSFER", "维修调拨");
    
    private final String code;
    private final String description;
    
    TransferType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static TransferType getByCode(String code) {
        for (TransferType type : TransferType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}