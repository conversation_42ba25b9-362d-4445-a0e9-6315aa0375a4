<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.WorkOrderMapper">

    <!-- 基本字段映射 -->
    <resultMap id="BaseResultMap" type="org.simple.equipment.entity.WorkOrder">
        <id column="id" property="id"/>
        <result column="order_code" property="orderCode"/>
        <result column="order_title" property="orderTitle"/>
        <result column="order_type" property="orderType"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="applicant_id" property="applicantId"/>
        <result column="applicant_name" property="applicantName"/>
        <result column="department_id" property="departmentId"/>
        <result column="assigned_user_id" property="assignedUserId"/>
        <result column="assigned_user_name" property="assignedUserName"/>
        <result column="priority" property="priority"/>
        <result column="order_status" property="orderStatus"/>
        <result column="description" property="description"/>
        <result column="solution" property="solution"/>
        <result column="images" property="images"/>
        <result column="apply_time" property="applyTime"/>
        <result column="assign_time" property="assignTime"/>
        <result column="start_time" property="startTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
    </resultMap>

    <!-- 获取工单统计信息 -->
    <select id="getWorkOrderStatistics" parameterType="string" resultType="map">
        SELECT 
            order_status,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM equipment_work_order WHERE tenant_id = #{tenantId}), 2) as percentage
        FROM equipment_work_order 
        WHERE tenant_id = #{tenantId}
        GROUP BY order_status
        ORDER BY order_status
    </select>

    <!-- 获取用户的工单列表 -->
    <select id="getUserWorkOrders" resultMap="BaseResultMap">
        SELECT * FROM equipment_work_order
        WHERE (applicant_id = #{userId} OR assigned_user_id = #{userId})
        AND tenant_id = #{tenantId}
        AND order_status IN ('PENDING', 'PROCESSING')
        ORDER BY priority DESC, apply_time ASC
    </select>

    <!-- 获取设备的工单列表 -->
    <select id="getEquipmentWorkOrders" resultMap="BaseResultMap">
        SELECT * FROM equipment_work_order
        WHERE equipment_id = #{equipmentId}
        AND tenant_id = #{tenantId}
        ORDER BY apply_time DESC
    </select>

    <!-- 获取待处理工单列表 -->
    <select id="getPendingWorkOrders" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM equipment_work_order
        WHERE tenant_id = #{tenantId}
        AND order_status = 'PENDING'
        ORDER BY priority DESC, apply_time ASC
    </select>

    <!-- 获取超时工单列表 -->
    <select id="getTimeoutWorkOrders" resultMap="BaseResultMap">
        SELECT * FROM equipment_work_order
        WHERE tenant_id = #{tenantId}
        AND order_status IN ('PENDING', 'PROCESSING')
        AND apply_time &lt; DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
        ORDER BY apply_time ASC
    </select>

    <!-- 获取工单处理时长统计 -->
    <select id="getProcessingTimeStatistics" parameterType="string" resultType="map">
        SELECT 
            order_type,
            AVG(TIMESTAMPDIFF(HOUR, apply_time, complete_time)) as avg_hours,
            MIN(TIMESTAMPDIFF(HOUR, apply_time, complete_time)) as min_hours,
            MAX(TIMESTAMPDIFF(HOUR, apply_time, complete_time)) as max_hours,
            COUNT(*) as total_count
        FROM equipment_work_order 
        WHERE tenant_id = #{tenantId}
        AND order_status = 'COMPLETED'
        AND complete_time IS NOT NULL
        GROUP BY order_type
        ORDER BY avg_hours DESC
    </select>

</mapper>