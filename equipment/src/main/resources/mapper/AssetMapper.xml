<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.AssetMapper">

    <!-- 基础字段映射 -->
    <sql id="Base_Column_List">
        a.id, a.equipment_code, a.equipment_name, a.category_id, a.type_id,
        a.model, a.manufacturer, a.supplier, a.purchase_price, a.department_id,
        a.location_id, a.equipment_images, a.scrap_years, a.equipment_status,
        a.qr_code, a.specification, a.warranty_period, a.install_date,
        a.use_date, a.remark, a.tenant_id, a.create_date, a.update_date,
        a.creator, a.updater, a.is_deleted
    </sql>

    <!-- 扩展字段映射 -->
    <sql id="Extended_Column_List">
        <include refid="Base_Column_List"/>,
        c.category_name,
        t.type_name,
        d.dept_name as department_name,
        l.location_name
    </sql>

    <!-- 基础查询条件 -->
    <sql id="Base_Where_Clause">
        <where>
            a.is_deleted = '0'
            <if test="asset.tenantId != null and asset.tenantId != ''">
                AND a.tenant_id = #{asset.tenantId}
            </if>
            <if test="asset.equipmentCode != null and asset.equipmentCode != ''">
                AND a.equipment_code LIKE CONCAT('%', #{asset.equipmentCode}, '%')
            </if>
            <if test="asset.equipmentName != null and asset.equipmentName != ''">
                AND a.equipment_name LIKE CONCAT('%', #{asset.equipmentName}, '%')
            </if>
            <if test="asset.categoryId != null and asset.categoryId != ''">
                AND a.category_id = #{asset.categoryId}
            </if>
            <if test="asset.typeId != null and asset.typeId != ''">
                AND a.type_id = #{asset.typeId}
            </if>
            <if test="asset.departmentId != null and asset.departmentId != ''">
                AND a.department_id = #{asset.departmentId}
            </if>
            <if test="asset.locationId != null and asset.locationId != ''">
                AND a.location_id = #{asset.locationId}
            </if>
            <if test="asset.equipmentStatus != null and asset.equipmentStatus != ''">
                AND a.equipment_status = #{asset.equipmentStatus}
            </if>
            <if test="asset.manufacturer != null and asset.manufacturer != ''">
                AND a.manufacturer LIKE CONCAT('%', #{asset.manufacturer}, '%')
            </if>
            <if test="asset.supplier != null and asset.supplier != ''">
                AND a.supplier LIKE CONCAT('%', #{asset.supplier}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询设备档案 -->
    <select id="listAssets" resultType="org.simple.equipment.dto.AssetDto">
        SELECT
        <include refid="Extended_Column_List"/>
        FROM equipment_asset a
        LEFT JOIN equipment_category c ON a.category_id = c.id
        LEFT JOIN equipment_type t ON a.type_id = t.id
        LEFT JOIN equipment_department d ON a.department_id = d.id
        LEFT JOIN equipment_location l ON a.location_id = l.id
        <include refid="Base_Where_Clause"/>
        ORDER BY a.create_date DESC
    </select>

    <!-- 根据租户和编码查询设备 -->
    <select id="getByEquipmentCode" resultType="org.simple.equipment.entity.Asset">
        SELECT <include refid="Base_Column_List"/>
        FROM equipment_asset a
        WHERE a.equipment_code = #{equipmentCode}
        AND a.tenant_id = #{tenantId}
        AND a.is_deleted = '0'
        LIMIT 1
    </select>

    <!-- 查询科室下的设备列表 -->
    <select id="getAssetsByDepartment" resultType="org.simple.equipment.dto.AssetDto">
        SELECT
        a.id, a.equipment_code, a.equipment_name, a.category_id, a.type_id,
        a.model, a.manufacturer, a.equipment_status, a.department_id, a.location_id,
        c.category_name, t.type_name
        FROM equipment_asset a
        LEFT JOIN equipment_category c ON a.category_id = c.id
        LEFT JOIN equipment_type t ON a.type_id = t.id
        WHERE a.department_id = #{departmentId}
        AND a.tenant_id = #{tenantId}
        AND a.is_deleted = '0'
        ORDER BY a.equipment_code
    </select>

    <!-- 统计设备状态数量 -->
    <select id="getStatusStatistics" resultType="java.util.Map">
        SELECT 
        equipment_status as status,
        COUNT(*) as count
        FROM equipment_asset
        WHERE tenant_id = #{tenantId} 
        AND is_deleted = '0'
        GROUP BY equipment_status
    </select>

    <!-- 生成设备编码 -->
    <select id="generateEquipmentCode" resultType="java.lang.String">
        SELECT CONCAT(#{prefix}, 
               LPAD(IFNULL(MAX(CAST(SUBSTRING(equipment_code, LENGTH(#{prefix}) + 1) AS UNSIGNED)), 0) + 1, 6, '0'))
        FROM equipment_asset
        WHERE equipment_code LIKE CONCAT(#{prefix}, '%')
        AND tenant_id = #{tenantId}
    </select>

</mapper>