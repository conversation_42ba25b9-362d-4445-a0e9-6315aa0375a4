<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.AssetFieldValueMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.simple.equipment.entity.AssetFieldValue">
        <id column="id" property="id"/>
        <result column="asset_id" property="assetId"/>
        <result column="field_id" property="fieldId"/>
        <result column="field_value" property="fieldValue"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, asset_id, field_id, field_value, tenant_id, create_date, update_date, creator, updater
    </sql>

    <!-- 根据设备ID查询字段值 -->
    <select id="selectByAssetId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM equipment_asset_field_value
        WHERE asset_id = #{assetId}
        AND tenant_id = #{tenantId}
        ORDER BY create_date DESC
    </select>

    <!-- 根据字段ID查询字段值 -->
    <select id="selectByFieldId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM equipment_asset_field_value
        WHERE field_id = #{fieldId}
        AND tenant_id = #{tenantId}
        ORDER BY create_date DESC
    </select>

    <!-- 查询设备的自定义字段值（包含字段配置信息） -->
    <select id="selectAssetFieldValues" resultType="java.util.Map">
        SELECT
            v.id,
            v.asset_id,
            v.field_id,
            v.field_value,
            f.field_code,
            f.field_name,
            f.field_type,
            f.default_value,
            f.options,
            f.validation_rules,
            f.is_required,
            f.display_order,
            f.placeholder,
            f.help_text,
            f.field_group
        FROM equipment_asset_field_value v
        INNER JOIN equipment_custom_field f ON v.field_id = f.id
        WHERE v.asset_id = #{assetId}
        AND v.tenant_id = #{tenantId}
        AND f.is_enabled = '1'
        ORDER BY f.display_order ASC, v.create_date DESC
    </select>

    <!-- 批量插入字段值 -->
    <insert id="insertBatch">
        INSERT INTO equipment_asset_field_value
        (id, asset_id, field_id, field_value, tenant_id, create_date, update_date, creator, updater)
        VALUES
        <foreach collection="fieldValues" item="item" separator=",">
            (#{item.id}, #{item.assetId}, #{item.fieldId}, #{item.fieldValue}, #{item.tenantId},
             #{item.createDate}, #{item.updateDate}, #{item.creator}, #{item.updater})
        </foreach>
    </insert>

    <!-- 批量删除设备的字段值 -->
    <delete id="deleteByAssetId">
        DELETE FROM equipment_asset_field_value
        WHERE asset_id = #{assetId}
        AND tenant_id = #{tenantId}
    </delete>

    <!-- 批量删除字段相关的值 -->
    <delete id="deleteByFieldId">
        DELETE FROM equipment_asset_field_value
        WHERE field_id = #{fieldId}
        AND tenant_id = #{tenantId}
    </delete>

</mapper>