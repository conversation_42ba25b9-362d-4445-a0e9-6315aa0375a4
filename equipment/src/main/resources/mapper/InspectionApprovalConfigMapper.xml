<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.simple.equipment.mapper.InspectionApprovalConfigMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.simple.equipment.entity.InspectionApprovalConfig">
        <id column="config_id" property="configId" />
        <result column="config_name" property="configName" />
        <result column="config_code" property="configCode" />
        <result column="config_description" property="configDescription" />
        <result column="scope_type" property="scopeType" />
        <result column="scope_value" property="scopeValue" />
        <result column="scope_name" property="scopeName" />
        <result column="approval_enabled" property="approvalEnabled" />
        <result column="approval_level" property="approvalLevel" />
        <result column="auto_approval_condition" property="autoApprovalCondition" />
        <result column="approval_timeout" property="approvalTimeout" />
        <result column="timeout_action" property="timeoutAction" />
        <result column="urgent_condition" property="urgentCondition" />
        <result column="urgent_flow" property="urgentFlow" />
        <result column="notification_config" property="notificationConfig" />
        <result column="flow_definition" property="flowDefinition" />
        <result column="config_status" property="configStatus" />
        <result column="priority" property="priority" />
        <result column="effective_time" property="effectiveTime" />
        <result column="expiry_time" property="expiryTime" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 分页查询审核配置 -->
    <select id="selectConfigPage" resultMap="BaseResultMap">
        SELECT c.*
        FROM eq_inspection_approval_config c
        <where>
            c.tenant_id = #{params.tenantId}
            <if test="params.configName != null and params.configName != ''">
                AND c.config_name LIKE CONCAT('%', #{params.configName}, '%')
            </if>
            <if test="params.configCode != null and params.configCode != ''">
                AND c.config_code LIKE CONCAT('%', #{params.configCode}, '%')
            </if>
            <if test="params.scopeType != null and params.scopeType != ''">
                AND c.scope_type = #{params.scopeType}
            </if>
            <if test="params.configStatus != null and params.configStatus != ''">
                AND c.config_status = #{params.configStatus}
            </if>
            <if test="params.approvalEnabled != null">
                AND c.approval_enabled = #{params.approvalEnabled}
            </if>
        </where>
        ORDER BY c.priority DESC, c.create_time DESC
    </select>

    <!-- 查询生效的配置 -->
    <select id="selectActiveConfigs" resultMap="BaseResultMap">
        SELECT c.*
        FROM eq_inspection_approval_config c
        WHERE c.tenant_id = #{tenantId}
          AND c.config_status = 'ACTIVE'
          AND c.approval_enabled = true
          AND (c.effective_time IS NULL OR c.effective_time &lt;= NOW())
          AND (c.expiry_time IS NULL OR c.expiry_time >= NOW())
        ORDER BY c.priority DESC
    </select>

    <!-- 根据适用范围查询配置 -->
    <select id="selectByScope" resultMap="BaseResultMap">
        SELECT c.*
        FROM eq_inspection_approval_config c
        WHERE c.tenant_id = #{tenantId}
          AND c.scope_type = #{scopeType}
          AND c.scope_value = #{scopeValue}
          AND c.config_status = 'ACTIVE'
          AND c.approval_enabled = true
        ORDER BY c.priority DESC
        LIMIT 1
    </select>

    <!-- 查询匹配的审核配置 -->
    <select id="selectMatchingConfigs" resultMap="BaseResultMap">
        SELECT c.*
        FROM eq_inspection_approval_config c
        WHERE c.tenant_id = #{tenantId}
          AND c.config_status = 'ACTIVE'
          AND c.approval_enabled = true
          AND (c.effective_time IS NULL OR c.effective_time &lt;= NOW())
          AND (c.expiry_time IS NULL OR c.expiry_time >= NOW())
          AND (
            c.scope_type = 'ALL'
            OR (c.scope_type = 'ASSET' AND c.scope_value = #{assetId})
            OR (c.scope_type = 'DEPT' AND c.scope_value = #{deptId})
            OR (c.scope_type = 'CATEGORY' AND c.scope_value = #{categoryId})
          )
        ORDER BY 
          CASE c.scope_type
            WHEN 'ASSET' THEN 1
            WHEN 'CATEGORY' THEN 2  
            WHEN 'DEPT' THEN 3
            WHEN 'ALL' THEN 4
          END,
          c.priority DESC
        LIMIT 1
    </select>

    <!-- 查询默认配置 -->
    <select id="selectDefaultConfig" resultMap="BaseResultMap">
        SELECT c.*
        FROM eq_inspection_approval_config c
        WHERE c.tenant_id = #{tenantId}
          AND c.scope_type = 'ALL'
          AND c.config_status = 'ACTIVE'
          AND c.approval_enabled = true
        ORDER BY c.priority DESC
        LIMIT 1
    </select>

    <!-- 检查配置冲突 -->
    <select id="selectConflictConfigs" resultMap="BaseResultMap">
        SELECT c.*
        FROM eq_inspection_approval_config c
        WHERE c.tenant_id = #{tenantId}
          AND c.scope_type = #{scopeType}
          AND c.scope_value = #{scopeValue}
          AND c.config_status = 'ACTIVE'
        <if test="excludeId != null and excludeId != ''">
          AND c.config_id != #{excludeId}
        </if>
    </select>

    <!-- 统计配置使用情况 -->
    <select id="selectConfigUsageStats" resultType="map">
        SELECT 
            c.config_id,
            c.config_name,
            c.scope_type,
            c.scope_name,
            COUNT(a.approval_id) as usage_count,
            MAX(a.submit_time) as last_used_time
        FROM eq_inspection_approval_config c
        LEFT JOIN eq_inspection_approval a ON FIND_IN_SET(c.config_id, a.extra_data)
        WHERE c.tenant_id = #{tenantId}
          AND c.config_status = 'ACTIVE'
        GROUP BY c.config_id, c.config_name, c.scope_type, c.scope_name
        ORDER BY usage_count DESC
    </select>

</mapper>