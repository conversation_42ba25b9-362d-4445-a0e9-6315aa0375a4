-- 设备巡检管理相关表

-- 1. 设备巡检计划表
CREATE TABLE IF NOT EXISTS `eq_patrol_plan` (
  `plan_id` varchar(32) NOT NULL COMMENT '巡检计划ID',
  `plan_code` varchar(50) NOT NULL COMMENT '计划编号',
  `plan_name` varchar(100) NOT NULL COMMENT '计划名称',
  `patrol_type` varchar(20) DEFAULT 'ROUTINE' COMMENT '巡检类型(ROUTINE-例行巡检,SPECIAL-专项巡检,EMERGENCY-应急巡检)',
  `patrol_cycle` varchar(20) DEFAULT 'DAILY' COMMENT '巡检周期(DAILY-每日,WEEKLY-每周,MONTHLY-每月,QUARTERLY-每季度,YEARLY-每年)',
  `route_id` varchar(32) NOT NULL COMMENT '巡检路线ID',
  `route_name` varchar(100) DEFAULT NULL COMMENT '巡检路线名称',
  `responsible_user_id` varchar(32) DEFAULT NULL COMMENT '责任人ID',
  `responsible_user_name` varchar(50) DEFAULT NULL COMMENT '责任人姓名',
  `responsible_dept_id` varchar(32) DEFAULT NULL COMMENT '责任部门ID',
  `responsible_dept_name` varchar(100) DEFAULT NULL COMMENT '责任部门名称',
  `start_time` time DEFAULT NULL COMMENT '计划开始时间',
  `end_time` time DEFAULT NULL COMMENT '计划结束时间',
  `duration_minutes` int(11) DEFAULT '0' COMMENT '计划持续时间(分钟)',
  `effective_date` datetime DEFAULT NULL COMMENT '计划生效日期',
  `expiry_date` datetime DEFAULT NULL COMMENT '计划失效日期',
  `execution_rule` longtext COMMENT '执行规则(JSON配置)',
  `patrol_requirements` text COMMENT '巡检要求描述',
  `notes` text COMMENT '注意事项',
  `auto_generate` tinyint(1) DEFAULT '1' COMMENT '自动生成任务(0-否,1-是)',
  `remind_minutes` int(11) DEFAULT '30' COMMENT '提前提醒时间(分钟)',
  `plan_status` tinyint(1) DEFAULT '0' COMMENT '计划状态(0-草稿,1-生效,2-暂停,3-失效)',
  `priority` tinyint(1) DEFAULT '2' COMMENT '优先级(1-低,2-中,3-高,4-紧急)',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-未删除,1-已删除)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`plan_id`),
  UNIQUE KEY `uniq_eq_patrol_plan_code` (`plan_code`),
  KEY `idx_eq_patrol_plan_route` (`route_id`),
  KEY `idx_eq_patrol_plan_responsible_user` (`responsible_user_id`),
  KEY `idx_eq_patrol_plan_responsible_dept` (`responsible_dept_id`),
  KEY `idx_eq_patrol_plan_type` (`patrol_type`),
  KEY `idx_eq_patrol_plan_cycle` (`patrol_cycle`),
  KEY `idx_eq_patrol_plan_status` (`plan_status`),
  KEY `idx_eq_patrol_plan_effective` (`effective_date`),
  KEY `idx_eq_patrol_plan_expiry` (`expiry_date`),
  KEY `idx_eq_patrol_plan_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备巡检计划表';

-- 2. 设备巡检路线表
CREATE TABLE IF NOT EXISTS `eq_patrol_route` (
  `route_id` varchar(32) NOT NULL COMMENT '路线ID',
  `route_code` varchar(50) NOT NULL COMMENT '路线编号',
  `route_name` varchar(100) NOT NULL COMMENT '路线名称',
  `route_desc` text COMMENT '路线描述',
  `route_type` varchar(20) DEFAULT 'INDOOR' COMMENT '路线类型(INDOOR-室内路线,OUTDOOR-室外路线,MIXED-混合路线)',
  `start_point` varchar(200) DEFAULT NULL COMMENT '路线起点',
  `end_point` varchar(200) DEFAULT NULL COMMENT '路线终点',
  `total_distance` decimal(10,2) DEFAULT '0.00' COMMENT '路线总距离(米)',
  `estimated_duration` int(11) DEFAULT '60' COMMENT '预计用时(分钟)',
  `difficulty_level` tinyint(1) DEFAULT '1' COMMENT '路线难度(1-简单,2-一般,3-困难,4-危险)',
  `route_status` tinyint(1) DEFAULT '1' COMMENT '路线状态(0-禁用,1-启用,2-维护中)',
  `route_map` varchar(255) DEFAULT NULL COMMENT '路线地图文件',
  `route_image` varchar(255) DEFAULT NULL COMMENT '路线图片',
  `safety_equipment` text COMMENT '安全面具要求',
  `safety_notes` text COMMENT '安全注意事项',
  `checkpoint_count` int(11) DEFAULT '0' COMMENT '巡检要点数量',
  `responsible_dept_id` varchar(32) DEFAULT NULL COMMENT '责任部门ID',
  `responsible_dept_name` varchar(100) DEFAULT NULL COMMENT '责任部门名称',
  `created_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `created_by_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `audit_status` tinyint(1) DEFAULT '0' COMMENT '审核状态(0-草稿,1-待审,2-通过,3-驳回)',
  `auditor_id` varchar(32) DEFAULT NULL COMMENT '审核人ID',
  `auditor_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `audit_comments` text COMMENT '审核意见',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-未删除,1-已删除)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`route_id`),
  UNIQUE KEY `uniq_eq_patrol_route_code` (`route_code`),
  KEY `idx_eq_patrol_route_type` (`route_type`),
  KEY `idx_eq_patrol_route_status` (`route_status`),
  KEY `idx_eq_patrol_route_difficulty` (`difficulty_level`),
  KEY `idx_eq_patrol_route_responsible_dept` (`responsible_dept_id`),
  KEY `idx_eq_patrol_route_created_by` (`created_by`),
  KEY `idx_eq_patrol_route_audit_status` (`audit_status`),
  KEY `idx_eq_patrol_route_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备巡检路线表';

-- 3. 设备巡检要点表
CREATE TABLE IF NOT EXISTS `eq_patrol_checkpoint` (
  `checkpoint_id` varchar(32) NOT NULL COMMENT '要点ID',
  `checkpoint_code` varchar(50) NOT NULL COMMENT '要点编号',
  `checkpoint_name` varchar(100) NOT NULL COMMENT '要点名称',
  `checkpoint_desc` text COMMENT '要点描述',
  `route_id` varchar(32) NOT NULL COMMENT '所属路线ID',
  `route_name` varchar(100) DEFAULT NULL COMMENT '路线名称',
  `asset_id` varchar(32) DEFAULT NULL COMMENT '设备ID',
  `asset_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `location_id` varchar(32) DEFAULT NULL COMMENT '设备位置ID',
  `location_name` varchar(100) DEFAULT NULL COMMENT '设备位置名称',
  `checkpoint_type` varchar(20) DEFAULT 'EQUIPMENT' COMMENT '要点类型(EQUIPMENT-设备点,AREA-区域点,SAFETY-安全点,ENVIRONMENT-环境点)',
  `checkpoint_order` int(11) DEFAULT '1' COMMENT '要点顺序',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `qr_code` varchar(255) DEFAULT NULL COMMENT '二维码内容',
  `qr_code_image` varchar(255) DEFAULT NULL COMMENT '二维码图片',
  `patrol_items` longtext COMMENT '巡检项目列表(JSON)',
  `patrol_standard` text COMMENT '巡检标准',
  `patrol_method` text COMMENT '巡检方法',
  `estimated_duration` int(11) DEFAULT '10' COMMENT '预计用时(分钟)',
  `require_photo` tinyint(1) DEFAULT '1' COMMENT '必须拍照(0-不需要,1-需要)',
  `require_checkin` tinyint(1) DEFAULT '1' COMMENT '必须签到(0-不需要,1-需要)',
  `safety_notes` text COMMENT '安全注意事项',
  `requirements` text COMMENT '巡检要求',
  `exception_guide` text COMMENT '异常处理指导',
  `contact_info` text COMMENT '联系人信息',
  `checkpoint_status` tinyint(1) DEFAULT '1' COMMENT '要点状态(0-禁用,1-启用,2-维护中)',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-未删除,1-已删除)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`checkpoint_id`),
  UNIQUE KEY `uniq_eq_patrol_checkpoint_code` (`checkpoint_code`),
  KEY `idx_eq_patrol_checkpoint_route` (`route_id`),
  KEY `idx_eq_patrol_checkpoint_asset` (`asset_id`),
  KEY `idx_eq_patrol_checkpoint_location` (`location_id`),
  KEY `idx_eq_patrol_checkpoint_type` (`checkpoint_type`),
  KEY `idx_eq_patrol_checkpoint_order` (`checkpoint_order`),
  KEY `idx_eq_patrol_checkpoint_status` (`checkpoint_status`),
  KEY `idx_eq_patrol_checkpoint_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备巡检要点表';

-- 4. 设备巡检任务表
CREATE TABLE IF NOT EXISTS `eq_patrol_task` (
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `task_code` varchar(50) NOT NULL COMMENT '任务编号',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `plan_id` varchar(32) DEFAULT NULL COMMENT '所属计划ID',
  `plan_name` varchar(100) DEFAULT NULL COMMENT '计划名称',
  `route_id` varchar(32) NOT NULL COMMENT '巡检路线ID',
  `route_name` varchar(100) DEFAULT NULL COMMENT '路线名称',
  `task_type` varchar(20) DEFAULT 'ROUTINE' COMMENT '任务类型(ROUTINE-例行巡检,SPECIAL-专项巡检,EMERGENCY-应急巡检)',
  `task_status` varchar(20) DEFAULT 'PENDING' COMMENT '任务状态(PENDING-待执行,ASSIGNED-已分配,IN_PROGRESS-进行中,COMPLETED-已完成,CANCELLED-已取消,OVERDUE-已超时)',
  `priority` tinyint(1) DEFAULT '2' COMMENT '任务优先级(1-低,2-中,3-高,4-紧急)',
  `executor_id` varchar(32) DEFAULT NULL COMMENT '执行人ID',
  `executor_name` varchar(50) DEFAULT NULL COMMENT '执行人姓名',
  `executor_dept_id` varchar(32) DEFAULT NULL COMMENT '执行部门ID',
  `executor_dept_name` varchar(100) DEFAULT NULL COMMENT '执行部门名称',
  `planned_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `actual_duration` int(11) DEFAULT '0' COMMENT '实际用时(分钟)',
  `total_checkpoints` int(11) DEFAULT '0' COMMENT '巡检要点总数',
  `completed_checkpoints` int(11) DEFAULT '0' COMMENT '已完成要点数',
  `completion_rate` decimal(5,2) DEFAULT '0.00' COMMENT '完成进度(%)',
  `exception_count` int(11) DEFAULT '0' COMMENT '发现异常数量',
  `critical_exception_count` int(11) DEFAULT '0' COMMENT '重大异常数量',
  `task_desc` text COMMENT '任务描述',
  `task_requirements` text COMMENT '任务要求',
  `execution_result` text COMMENT '执行结果描述',
  `execution_notes` text COMMENT '执行备注',
  `assigned_time` datetime DEFAULT NULL COMMENT '分配时间',
  `assigned_by` varchar(32) DEFAULT NULL COMMENT '分配人ID',
  `assigned_by_name` varchar(50) DEFAULT NULL COMMENT '分配人姓名',
  `accepted_time` datetime DEFAULT NULL COMMENT '接受时间',
  `is_overdue` tinyint(1) DEFAULT '0' COMMENT '是否超时(0-未超时,1-已超时)',
  `overdue_minutes` int(11) DEFAULT '0' COMMENT '超时时间(分钟)',
  `cancel_reason` text COMMENT '取消原因',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-未删除,1-已删除)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`task_id`),
  UNIQUE KEY `uniq_eq_patrol_task_code` (`task_code`),
  KEY `idx_eq_patrol_task_plan` (`plan_id`),
  KEY `idx_eq_patrol_task_route` (`route_id`),
  KEY `idx_eq_patrol_task_type` (`task_type`),
  KEY `idx_eq_patrol_task_status` (`task_status`),
  KEY `idx_eq_patrol_task_executor` (`executor_id`),
  KEY `idx_eq_patrol_task_executor_dept` (`executor_dept_id`),
  KEY `idx_eq_patrol_task_planned_start` (`planned_start_time`),
  KEY `idx_eq_patrol_task_planned_end` (`planned_end_time`),
  KEY `idx_eq_patrol_task_assigned` (`assigned_time`),
  KEY `idx_eq_patrol_task_overdue` (`is_overdue`),
  KEY `idx_eq_patrol_task_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备巡检任务表';

-- 5. 设备巡检记录表
CREATE TABLE IF NOT EXISTS `eq_patrol_record` (
  `record_id` varchar(32) NOT NULL COMMENT '记录ID',
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `task_code` varchar(50) DEFAULT NULL COMMENT '任务编号',
  `checkpoint_id` varchar(32) NOT NULL COMMENT '要点ID',
  `checkpoint_name` varchar(100) DEFAULT NULL COMMENT '要点名称',
  `asset_id` varchar(32) DEFAULT NULL COMMENT '设备ID',
  `asset_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `patrol_user_id` varchar(32) NOT NULL COMMENT '巡检人ID',
  `patrol_user_name` varchar(50) DEFAULT NULL COMMENT '巡检人姓名',
  `patrol_time` datetime DEFAULT NULL COMMENT '巡检时间',
  `patrol_result` varchar(20) DEFAULT 'NORMAL' COMMENT '巡检结果(NORMAL-正常,ABNORMAL-异常,CRITICAL-严重)',
  `patrol_status` varchar(20) DEFAULT 'PENDING' COMMENT '巡检状态(PENDING-待巡检,COMPLETED-已完成,SKIPPED-已跳过)',
  `patrol_duration` int(11) DEFAULT '0' COMMENT '巡检工时(分钟)',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `location_address` varchar(255) DEFAULT NULL COMMENT '位置地址',
  `checkin_method` varchar(20) DEFAULT 'MANUAL' COMMENT '签到方式(QR_CODE-二维码,GPS-GPS定位,MANUAL-手动)',
  `checkin_time` datetime DEFAULT NULL COMMENT '签到时间',
  `patrol_items_result` longtext COMMENT '巡检项目结果(JSON)',
  `exception_desc` text COMMENT '异常情况描述',
  `exception_level` tinyint(1) DEFAULT '1' COMMENT '异常级别(1-轻微,2-一般,3-严重,4-紧急)',
  `photos` longtext COMMENT '现场照片(JSON数组)',
  `exception_photos` longtext COMMENT '异常照片(JSON数组)',
  `voice_record` varchar(255) DEFAULT NULL COMMENT '语音记录',
  `patrol_notes` text COMMENT '巡检备注',
  `handling_measures` text COMMENT '处理措施',
  `handler_name` varchar(50) DEFAULT NULL COMMENT '处理人员',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `handle_status` varchar(20) DEFAULT 'PENDING' COMMENT '处理状态(PENDING-待处理,PROCESSING-处理中,COMPLETED-已处理)',
  `follower_name` varchar(50) DEFAULT NULL COMMENT '跟进人员',
  `is_qualified` tinyint(1) DEFAULT '1' COMMENT '是否合格(0-不合格,1-合格)',
  `qualification_rate` decimal(5,2) DEFAULT '100.00' COMMENT '合格率(%)',
  `score` decimal(5,2) DEFAULT '100.00' COMMENT '评分',
  `score_details` longtext COMMENT '评分详情(JSON)',
  `audit_status` tinyint(1) DEFAULT '0' COMMENT '审核状态(0-未审核,1-已审核)',
  `auditor_id` varchar(32) DEFAULT NULL COMMENT '审核人ID',
  `auditor_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_comments` text COMMENT '审核意见',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-未删除,1-已删除)',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_eq_patrol_record_task` (`task_id`),
  KEY `idx_eq_patrol_record_checkpoint` (`checkpoint_id`),
  KEY `idx_eq_patrol_record_asset` (`asset_id`),
  KEY `idx_eq_patrol_record_user` (`patrol_user_id`),
  KEY `idx_eq_patrol_record_time` (`patrol_time`),
  KEY `idx_eq_patrol_record_result` (`patrol_result`),
  KEY `idx_eq_patrol_record_status` (`patrol_status`),
  KEY `idx_eq_patrol_record_exception_level` (`exception_level`),
  KEY `idx_eq_patrol_record_handle_status` (`handle_status`),
  KEY `idx_eq_patrol_record_audit_status` (`audit_status`),
  KEY `idx_eq_patrol_record_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备巡检记录表';

-- 初始化测试数据

-- 初始化巡检路线
INSERT INTO `eq_patrol_route` (`route_id`, `route_code`, `route_name`, `route_desc`, `route_type`, `start_point`, `end_point`, `total_distance`, `estimated_duration`, `difficulty_level`, `route_status`, `safety_equipment`, `safety_notes`, `checkpoint_count`, `responsible_dept_id`, `responsible_dept_name`, `created_by`, `created_by_name`, `audit_status`, `tenant_id`, `del_flag`, `create_by`, `create_time`) VALUES
('route_001', 'RT001', '机房日常巡检路线', '机房设备日常巡检路线，包括主要服务器、网络设备和空调系统', 'INDOOR', '机房入口', '机房出口', 150.00, 45, 2, 1, '绝缘手套,防静电鞋,安全帽', '进入机房前必须佩戴防静电装备，严禁携带易燃物品', 8, 'dept_it', 'IT部门', 'user_001', '管理员', 2, 'default', 0, 'system', NOW()),
('route_002', 'RT002', '办公楼安全巡检路线', '办公楼安全设备巡检路线，包括消防设备、应急照明等', 'INDOOR', '一楼大厅', '十楼天台', 300.00, 60, 1, 1, '手电筒,对讲机', '检查过程中注意楼道安全，发现问题及时报告', 12, 'dept_security', '安保部', 'user_002', '安保主管', 2, 'default', 0, 'system', NOW()),
('route_003', 'RT003', '厂区设备巡检路线', '生产厂区主要设备巡检路线，包括生产线和辅助设备', 'OUTDOOR', '厂区大门', '仓库区域', 800.00, 120, 3, 1, '安全帽,防护眼镜,工作服,防滑鞋', '巡检中遇到设备运行时不得靠近，遵守安全操作规程', 15, 'dept_production', '生产部', 'user_003', '生产主管', 2, 'default', 0, 'system', NOW());

-- 初始化巡检要点
INSERT INTO `eq_patrol_checkpoint` (`checkpoint_id`, `checkpoint_code`, `checkpoint_name`, `checkpoint_desc`, `route_id`, `route_name`, `asset_id`, `asset_name`, `location_id`, `location_name`, `checkpoint_type`, `checkpoint_order`, `longitude`, `latitude`, `qr_code`, `patrol_items`, `patrol_standard`, `patrol_method`, `estimated_duration`, `require_photo`, `require_checkin`, `safety_notes`, `requirements`, `checkpoint_status`, `tenant_id`, `del_flag`, `create_by`, `create_time`) VALUES
('cp_001', 'CP001', '主服务器机柜', '检查主服务器运行状态', 'route_001', '机房日常巡检路线', 'asset_001', '主服务器', 'location_001', '机房一区', 'EQUIPMENT', 1, 116.397428, 39.916527, 'QR_MAIN_SERVER_001', '[{"item":"服务器运行状态","type":"check"},{"item":"温度指示","type":"record"},{"item":"LED指示灯","type":"check"}]', '服务器正常运行，温度在正常范围内', '目视检查和仪表读数', 10, 1, 1, '不得触碰运行中的设备', '必须记录温度数值和设备状态', 1, 'default', 0, 'system', NOW()),
('cp_002', 'CP002', '网络交换机', '检查网络设备连接状态', 'route_001', '机房日常巡检路线', 'asset_002', '核心交换机', 'location_001', '机房一区', 'EQUIPMENT', 2, 116.397428, 39.916527, 'QR_NETWORK_SWITCH_001', '[{"item":"网络连接状态","type":"check"},{"item":"端口指示灯","type":"check"},{"item":"设备温度","type":"record"}]', '所有端口指示灯正常，无超温现象', '目视检查和上机测试', 15, 1, 1, '检查时保持静电防护', '必须检查所有活跃端口状态', 1, 'default', 0, 'system', NOW()),
('cp_003', 'CP003', '消防器材箱', '检查消防器材配置', 'route_002', '办公楼安全巡检路线', NULL, NULL, 'location_002', '一楼大厅', 'SAFETY', 1, 116.397428, 39.916527, 'QR_FIRE_BOX_001', '[{"item":"灭火器数量","type":"count"},{"item":"器材有效期","type":"check"},{"item":"箱体完整性","type":"check"}]', '灭火器数量充足，在有效期内，箱体无损坏', '目视检查和清点数量', 8, 1, 1, '检查完毕确保箱体关闭', '必须记录器材数量和有效期', 1, 'default', 0, 'system', NOW()),
('cp_004', 'CP004', '应急照明系统', '检查应急照明设备', 'route_002', '办公楼安全巡检路线', 'asset_003', '应急照明系统', 'location_003', '楼道间', 'SAFETY', 2, 116.397428, 39.916527, 'QR_EMERGENCY_LIGHT_001', '[{"item":"照明亮度","type":"test"},{"item":"备电电池","type":"test"},{"item":"开关功能","type":"test"}]', '照明亮度正常，备电功能正常', '手动测试和功能检查', 12, 1, 1, '测试时注意周围人员安全', '必须测试应急切换功能', 1, 'default', 0, 'system', NOW());

-- 初始化巡检计划
INSERT INTO `eq_patrol_plan` (`plan_id`, `plan_code`, `plan_name`, `patrol_type`, `patrol_cycle`, `route_id`, `route_name`, `responsible_user_id`, `responsible_user_name`, `responsible_dept_id`, `responsible_dept_name`, `start_time`, `end_time`, `duration_minutes`, `effective_date`, `expiry_date`, `execution_rule`, `patrol_requirements`, `notes`, `auto_generate`, `remind_minutes`, `plan_status`, `priority`, `tenant_id`, `del_flag`, `create_by`, `create_time`) VALUES
('plan_001', 'PP001', '机房日常巡检计划', 'ROUTINE', 'DAILY', 'route_001', '机房日常巡检路线', 'user_001', '张三', 'dept_it', 'IT部门', '09:00:00', '10:00:00', 60, '2024-01-01 00:00:00', '2024-12-31 23:59:59', '{"weekdays":[1,2,3,4,5],"frequency":"daily","skip_holidays":true}', '每日上午进行机房设备巡检，检查设备运行状态', '如遇紧急情况立即通知相关负责人', 1, 30, 1, 3, 'default', 0, 'system', NOW()),
('plan_002', 'PP002', '安全设备周检计划', 'ROUTINE', 'WEEKLY', 'route_002', '办公楼安全巡检路线', 'user_002', '李四', 'dept_security', '安保部', '14:00:00', '16:00:00', 120, '2024-01-01 00:00:00', '2024-12-31 23:59:59', '{"weekdays":[5],"frequency":"weekly","week_of_month":"all"}', '每周五下午进行安全设备巡检，重点检查消防器材', '测试过程中不得影响正常办公', 1, 60, 1, 2, 'default', 0, 'system', NOW()),
('plan_003', 'PP003', '生产设备月检计划', 'SPECIAL', 'MONTHLY', 'route_003', '厂区设备巡检路线', 'user_003', '王五', 'dept_production', '生产部', '08:00:00', '12:00:00', 240, '2024-01-01 00:00:00', '2024-12-31 23:59:59', '{"day_of_month":1,"frequency":"monthly","backup_days":[2,3]}', '每月第一个工作日进行全面设备检查', '检查期间停止相关生产线运行', 1, 120, 1, 2, 'default', 0, 'system', NOW());

-- 创建索引
CREATE INDEX idx_eq_patrol_plan_name ON eq_patrol_plan(plan_name);
CREATE INDEX idx_eq_patrol_route_name ON eq_patrol_route(route_name);
CREATE INDEX idx_eq_patrol_checkpoint_name ON eq_patrol_checkpoint(checkpoint_name);
CREATE INDEX idx_eq_patrol_task_name ON eq_patrol_task(task_name);
CREATE INDEX idx_eq_patrol_record_patrol_time ON eq_patrol_record(patrol_time);
CREATE INDEX idx_eq_patrol_record_checkin_time ON eq_patrol_record(checkin_time);
CREATE INDEX idx_eq_patrol_task_completion_rate ON eq_patrol_task(completion_rate);
CREATE INDEX idx_eq_patrol_record_is_qualified ON eq_patrol_record(is_qualified);
