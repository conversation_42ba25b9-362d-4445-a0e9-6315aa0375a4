-- 设备二维码管理表
CREATE TABLE `equipment_qr_codes` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `equipment_id` VARCHAR(50) NOT NULL COMMENT '设备ID',
    `qr_code_data` TEXT NOT NULL COMMENT '二维码数据(JSON格式)',
    `qr_code_image_url` VARCHAR(500) COMMENT '二维码图片URL',
    `qr_type` ENUM('EQUIPMENT', 'TASK', 'LOCATION') DEFAULT 'EQUIPMENT' COMMENT '二维码类型',
    `generated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
    `expires_at` TIMESTAMP NULL COMMENT '过期时间',
    `is_active` TINYINT DEFAULT 1 COMMENT '是否激活',
    `scan_count` INT DEFAULT 0 COMMENT '扫描次数',
    `last_scan_at` TIMESTAMP NULL COMMENT '最后扫描时间',
    `created_by` VARCHAR(50) COMMENT '创建人',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(50) COMMENT '更新人',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_equipment_qr` (`equipment_id`, `tenant_id`),
    INDEX `idx_qr_type` (`qr_type`),
    INDEX `idx_tenant` (`tenant_id`),
    INDEX `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备二维码管理表';

-- 设备模板关联表
CREATE TABLE `equipment_template_association` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `equipment_id` VARCHAR(50) NOT NULL COMMENT '设备ID',
    `template_id` VARCHAR(50) NOT NULL COMMENT '模板ID',
    `association_type` ENUM('PRIMARY', 'SECONDARY') DEFAULT 'PRIMARY' COMMENT '关联类型',
    `auto_assigned` TINYINT DEFAULT 0 COMMENT '是否自动分配',
    `assignment_rule` JSON COMMENT '分配规则',
    `priority` INT DEFAULT 0 COMMENT '优先级',
    `effective_from` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '生效开始时间',
    `effective_to` TIMESTAMP NULL COMMENT '生效结束时间',
    `created_by` VARCHAR(50) COMMENT '创建人',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(50) COMMENT '更新人',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_equipment_template` (`equipment_id`, `template_id`, `tenant_id`),
    INDEX `idx_equipment` (`equipment_id`),
    INDEX `idx_template` (`template_id`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备模板关联表';

-- 设备扫码记录表
CREATE TABLE `equipment_scan_records` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `qr_code_id` VARCHAR(50) NOT NULL COMMENT '二维码ID',
    `equipment_id` VARCHAR(50) NOT NULL COMMENT '设备ID',
    `scanner_id` VARCHAR(50) NOT NULL COMMENT '扫描人ID',
    `scanner_name` VARCHAR(100) COMMENT '扫描人姓名',
    `scan_type` ENUM('INSPECTION', 'MAINTENANCE', 'QUERY', 'OTHER') DEFAULT 'INSPECTION' COMMENT '扫描类型',
    `scan_location` VARCHAR(200) COMMENT '扫描位置',
    `scan_device_info` JSON COMMENT '扫描设备信息',
    `task_id` VARCHAR(50) COMMENT '关联任务ID',
    `scan_result` ENUM('SUCCESS', 'FAILED', 'EXPIRED', 'INVALID') DEFAULT 'SUCCESS' COMMENT '扫描结果',
    `error_message` VARCHAR(500) COMMENT '错误信息',
    `scan_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '扫描时间',
    `ip_address` VARCHAR(45) COMMENT 'IP地址',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    INDEX `idx_qr_code` (`qr_code_id`),
    INDEX `idx_equipment` (`equipment_id`),
    INDEX `idx_scanner` (`scanner_id`),
    INDEX `idx_scan_time` (`scan_time`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备扫码记录表';

-- 设备分类增强表 (医疗设备特色)
CREATE TABLE `medical_equipment_classification` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `equipment_id` VARCHAR(50) NOT NULL COMMENT '设备ID',
    `medical_category` ENUM('LIFE_SUPPORT', 'DIAGNOSTIC', 'THERAPEUTIC', 'MONITORING', 'REHABILITATION', 'LABORATORY') DEFAULT 'MONITORING' COMMENT '医疗设备类别',
    `safety_level` ENUM('CLASS_I', 'CLASS_II', 'CLASS_III') DEFAULT 'CLASS_I' COMMENT '安全等级',
    `importance_level` ENUM('A', 'B', 'C') DEFAULT 'C' COMMENT '重要程度等级',
    `usage_intensity` ENUM('HIGH', 'MEDIUM', 'LOW') DEFAULT 'LOW' COMMENT '使用强度',
    `risk_level` ENUM('CRITICAL', 'HIGH', 'MEDIUM', 'LOW') DEFAULT 'LOW' COMMENT '风险等级',
    `inspection_frequency` INT DEFAULT 30 COMMENT '点检频率(天)',
    `calibration_required` TINYINT DEFAULT 0 COMMENT '是否需要计量检定',
    `calibration_cycle` INT DEFAULT 365 COMMENT '计量检定周期(天)',
    `regulatory_requirements` JSON COMMENT '法规要求',
    `special_requirements` JSON COMMENT '特殊要求',
    `created_by` VARCHAR(50) COMMENT '创建人',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` VARCHAR(50) COMMENT '更新人',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_equipment_classification` (`equipment_id`, `tenant_id`),
    INDEX `idx_medical_category` (`medical_category`),
    INDEX `idx_safety_level` (`safety_level`),
    INDEX `idx_importance_level` (`importance_level`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='医疗设备分类表';