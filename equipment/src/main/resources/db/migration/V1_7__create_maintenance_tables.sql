-- 设备保养功能相关表结构

-- 设备保养计划表
CREATE TABLE IF NOT EXISTS `eq_maintenance_plan` (
  `plan_id` varchar(32) NOT NULL COMMENT '保养计划ID',
  `plan_name` varchar(100) NOT NULL COMMENT '计划名称',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '计划编码',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `equipment_name` varchar(100) NOT NULL COMMENT '设备名称',
  `equipment_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `maintenance_type` varchar(20) DEFAULT 'DAILY' COMMENT '保养类型(DAILY-日常保养,LEVEL_ONE-一级保养,LEVEL_TWO-二级保养,LEVEL_THREE-三级保养,SPECIAL-专项保养,SEASONAL-季节性保养,PREVENTIVE-预防性保养)',
  `maintenance_cycle` varchar(20) DEFAULT 'DAILY' COMMENT '保养周期(DAILY-每日,WEEKLY-每周,MONTHLY-每月,QUARTERLY-每季度,HALF_YEARLY-每半年,YEARLY-每年,HOURLY-按小时,MILEAGE-按里程,USAGE_COUNT-按次数)',
  `cycle_value` int DEFAULT 1 COMMENT '周期值(数值)',
  `cycle_unit` varchar(20) DEFAULT 'DAY' COMMENT '周期单位(天、小时、公里等)',
  `maintenance_standard` longtext COMMENT '保养标准',
  `maintenance_content` longtext COMMENT '保养内容',
  `maintenance_requirements` longtext COMMENT '保养要求',
  `required_hours` decimal(10,2) DEFAULT 0.00 COMMENT '所需工时(小时)',
  `estimated_cost` decimal(10,2) DEFAULT 0.00 COMMENT '预计费用',
  `responsible_person_id` varchar(32) DEFAULT NULL COMMENT '负责人ID',
  `responsible_person_name` varchar(50) DEFAULT NULL COMMENT '负责人姓名',
  `execute_dept_id` varchar(32) DEFAULT NULL COMMENT '执行部门ID',
  `execute_dept_name` varchar(100) DEFAULT NULL COMMENT '执行部门名称',
  `priority` int DEFAULT 2 COMMENT '优先级(1-低,2-中,3-高,4-紧急)',
  `start_time` time DEFAULT NULL COMMENT '开始时间',
  `end_time` time DEFAULT NULL COMMENT '结束时间',
  `effective_date` datetime DEFAULT NULL COMMENT '生效日期',
  `expiry_date` datetime DEFAULT NULL COMMENT '失效日期',
  `plan_status` int DEFAULT 0 COMMENT '计划状态(0-草稿,1-生效,2-暂停,3-失效)',
  `auto_generate` int DEFAULT 1 COMMENT '自动生成任务(0-否,1-是)',
  `remind_hours` int DEFAULT 24 COMMENT '提前提醒时间(小时)',
  `skip_holidays` int DEFAULT 0 COMMENT '跳过节假日(0-否,1-是)',
  `execution_rule` longtext COMMENT '执行规则(JSON配置)',
  `last_execution_time` datetime DEFAULT NULL COMMENT '上次执行时间',
  `next_execution_time` datetime DEFAULT NULL COMMENT '下次执行时间',
  `execution_count` int DEFAULT 0 COMMENT '已执行次数',
  `notes` longtext COMMENT '备注',
  `enabled` int DEFAULT 1 COMMENT '是否启用(0-禁用,1-启用)',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`plan_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_maintenance_type` (`maintenance_type`),
  KEY `idx_maintenance_cycle` (`maintenance_cycle`),
  KEY `idx_plan_status` (`plan_status`),
  KEY `idx_next_execution_time` (`next_execution_time`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备保养计划表';

-- 设备保养任务表
CREATE TABLE IF NOT EXISTS `eq_maintenance_task` (
  `task_id` varchar(32) NOT NULL COMMENT '保养任务ID',
  `task_number` varchar(50) DEFAULT NULL COMMENT '任务编号',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `plan_id` varchar(32) DEFAULT NULL COMMENT '保养计划ID',
  `plan_name` varchar(100) DEFAULT NULL COMMENT '计划名称',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `equipment_name` varchar(100) NOT NULL COMMENT '设备名称',
  `equipment_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `maintenance_type` varchar(20) DEFAULT 'DAILY' COMMENT '保养类型(DAILY-日常保养,LEVEL_ONE-一级保养,LEVEL_TWO-二级保养,LEVEL_THREE-三级保养,SPECIAL-专项保养,SEASONAL-季节性保养,PREVENTIVE-预防性保养)',
  `maintenance_content` longtext COMMENT '保养内容',
  `maintenance_requirements` longtext COMMENT '保养要求',
  `scheduled_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `scheduled_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `required_hours` decimal(10,2) DEFAULT 0.00 COMMENT '所需工时(小时)',
  `actual_hours` decimal(10,2) DEFAULT 0.00 COMMENT '实际工时(小时)',
  `estimated_cost` decimal(10,2) DEFAULT 0.00 COMMENT '预计费用',
  `actual_cost` decimal(10,2) DEFAULT 0.00 COMMENT '实际费用',
  `executor_id` varchar(32) DEFAULT NULL COMMENT '执行人ID',
  `executor_name` varchar(50) DEFAULT NULL COMMENT '执行人姓名',
  `execute_dept_id` varchar(32) DEFAULT NULL COMMENT '执行部门ID',
  `execute_dept_name` varchar(100) DEFAULT NULL COMMENT '执行部门名称',
  `task_status` varchar(20) DEFAULT 'PLANNED' COMMENT '任务状态(PLANNED-计划中,PENDING-待保养,IN_PROGRESS-保养中,PAUSED-已暂停,COMPLETED-已完成,CANCELLED-已取消,OVERDUE-已超期,ABNORMAL-异常)',
  `priority` int DEFAULT 2 COMMENT '优先级(1-低,2-中,3-高,4-紧急)',
  `completion_percentage` decimal(5,2) DEFAULT 0.00 COMMENT '完成百分比',
  `assigned_by` varchar(32) DEFAULT NULL COMMENT '分配人ID',
  `assigned_time` datetime DEFAULT NULL COMMENT '分配时间',
  `accepted_time` datetime DEFAULT NULL COMMENT '接受时间',
  `completed_time` datetime DEFAULT NULL COMMENT '完成时间',
  `maintenance_result` longtext COMMENT '保养结果',
  `found_problems` longtext COMMENT '发现问题',
  `solutions` longtext COMMENT '解决方案',
  `spare_parts_used` longtext COMMENT '备件使用',
  `quality_evaluation` longtext COMMENT '质量评价',
  `evaluated_by` varchar(32) DEFAULT NULL COMMENT '评价人ID',
  `evaluated_time` datetime DEFAULT NULL COMMENT '评价时间',
  `evaluation_score` decimal(5,2) DEFAULT NULL COMMENT '评价得分',
  `delay_reason` longtext COMMENT '延期原因',
  `cancel_reason` longtext COMMENT '取消原因',
  `attachments` longtext COMMENT '附件',
  `notes` longtext COMMENT '备注',
  `is_urgent` int DEFAULT 0 COMMENT '是否紧急(0-否,1-是)',
  `is_outsourced` int DEFAULT 0 COMMENT '是否外包(0-否,1-是)',
  `outsourced_vendor` varchar(100) DEFAULT NULL COMMENT '外包供应商',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`task_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_maintenance_type` (`maintenance_type`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_executor_id` (`executor_id`),
  KEY `idx_execute_dept_id` (`execute_dept_id`),
  KEY `idx_scheduled_start_time` (`scheduled_start_time`),
  KEY `idx_scheduled_end_time` (`scheduled_end_time`),
  KEY `idx_priority` (`priority`),
  KEY `idx_is_urgent` (`is_urgent`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备保养任务表';

-- 设备保养记录表
CREATE TABLE IF NOT EXISTS `eq_maintenance_record` (
  `record_id` varchar(32) NOT NULL COMMENT '保养记录ID',
  `record_number` varchar(50) DEFAULT NULL COMMENT '记录编号',
  `task_id` varchar(32) DEFAULT NULL COMMENT '保养任务ID',
  `task_number` varchar(50) DEFAULT NULL COMMENT '任务编号',
  `plan_id` varchar(32) DEFAULT NULL COMMENT '保养计划ID',
  `equipment_id` varchar(32) NOT NULL COMMENT '设备ID',
  `equipment_name` varchar(100) NOT NULL COMMENT '设备名称',
  `equipment_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `maintenance_type` varchar(20) DEFAULT 'DAILY' COMMENT '保养类型(DAILY-日常保养,LEVEL_ONE-一级保养,LEVEL_TWO-二级保养,LEVEL_THREE-三级保养,SPECIAL-专项保养,SEASONAL-季节性保养,PREVENTIVE-预防性保养)',
  `maintenance_items` longtext COMMENT '保养项目',
  `maintenance_standard` longtext COMMENT '保养标准',
  `maintenance_content` longtext COMMENT '保养内容',
  `executor_id` varchar(32) DEFAULT NULL COMMENT '执行人ID',
  `executor_name` varchar(50) DEFAULT NULL COMMENT '执行人姓名',
  `execute_dept_id` varchar(32) DEFAULT NULL COMMENT '执行部门ID',
  `execute_dept_name` varchar(100) DEFAULT NULL COMMENT '执行部门名称',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `actual_hours` decimal(10,2) DEFAULT 0.00 COMMENT '实际工时(小时)',
  `before_maintenance_status` longtext COMMENT '保养前状态',
  `after_maintenance_status` longtext COMMENT '保养后状态',
  `maintenance_result` varchar(20) DEFAULT 'NORMAL' COMMENT '保养结果(NORMAL-正常,ABNORMAL-异常,NEED_REPAIR-需要维修)',
  `found_problems` longtext COMMENT '发现问题',
  `solutions` longtext COMMENT '解决方案',
  `replaced_parts` longtext COMMENT '更换部件',
  `materials_used` longtext COMMENT '使用材料',
  `parts_cost` decimal(10,2) DEFAULT 0.00 COMMENT '备件费用',
  `labor_cost` decimal(10,2) DEFAULT 0.00 COMMENT '人工费用',
  `other_cost` decimal(10,2) DEFAULT 0.00 COMMENT '其他费用',
  `total_cost` decimal(10,2) DEFAULT 0.00 COMMENT '总费用',
  `quality_evaluation` longtext COMMENT '质量评价',
  `evaluated_by` varchar(32) DEFAULT NULL COMMENT '评价人ID',
  `evaluated_by_name` varchar(50) DEFAULT NULL COMMENT '评价人姓名',
  `evaluated_time` datetime DEFAULT NULL COMMENT '评价时间',
  `evaluation_score` decimal(5,2) DEFAULT NULL COMMENT '评价得分',
  `photo_attachments` longtext COMMENT '照片附件',
  `document_attachments` longtext COMMENT '文档附件',
  `video_attachments` longtext COMMENT '视频附件',
  `audio_attachments` longtext COMMENT '音频附件',
  `suggestions` longtext COMMENT '建议',
  `next_maintenance_suggestion` datetime DEFAULT NULL COMMENT '下次保养建议时间',
  `notes` longtext COMMENT '备注',
  `is_qualified` int DEFAULT 1 COMMENT '是否合格(0-不合格,1-合格)',
  `is_outsourced` int DEFAULT 0 COMMENT '是否外包(0-否,1-是)',
  `outsourced_vendor` varchar(100) DEFAULT NULL COMMENT '外包供应商',
  `weather_condition` varchar(50) DEFAULT NULL COMMENT '天气情况',
  `environment_temperature` decimal(5,2) DEFAULT NULL COMMENT '环境温度',
  `environment_humidity` decimal(5,2) DEFAULT NULL COMMENT '环境湿度',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`record_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_equipment_id` (`equipment_id`),
  KEY `idx_maintenance_type` (`maintenance_type`),
  KEY `idx_executor_id` (`executor_id`),
  KEY `idx_execute_dept_id` (`execute_dept_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_maintenance_result` (`maintenance_result`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备保养记录表';

-- 设备保养模板表
CREATE TABLE IF NOT EXISTS `eq_maintenance_template` (
  `template_id` varchar(32) NOT NULL COMMENT '保养模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) DEFAULT NULL COMMENT '模板编码',
  `equipment_type_id` varchar(32) DEFAULT NULL COMMENT '设备类型ID',
  `equipment_type_name` varchar(100) DEFAULT NULL COMMENT '设备类型名称',
  `equipment_model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `maintenance_type` varchar(20) DEFAULT 'DAILY' COMMENT '保养类型(DAILY-日常保养,LEVEL_ONE-一级保养,LEVEL_TWO-二级保养,LEVEL_THREE-三级保养,SPECIAL-专项保养,SEASONAL-季节性保养,PREVENTIVE-预防性保养)',
  `maintenance_cycle` varchar(20) DEFAULT 'DAILY' COMMENT '保养周期(DAILY-每日,WEEKLY-每周,MONTHLY-每月,QUARTERLY-每季度,HALF_YEARLY-每半年,YEARLY-每年,HOURLY-按小时,MILEAGE-按里程,USAGE_COUNT-按次数)',
  `cycle_value` int DEFAULT 1 COMMENT '周期值(数值)',
  `cycle_unit` varchar(20) DEFAULT 'DAY' COMMENT '周期单位(天、小时、公里等)',
  `maintenance_standard` longtext COMMENT '保养标准',
  `maintenance_content` longtext COMMENT '保养内容',
  `maintenance_items` longtext COMMENT '保养项目(JSON格式)',
  `maintenance_requirements` longtext COMMENT '保养要求',
  `maintenance_steps` longtext COMMENT '保养步骤',
  `required_hours` decimal(10,2) DEFAULT 0.00 COMMENT '所需工时(小时)',
  `estimated_cost` decimal(10,2) DEFAULT 0.00 COMMENT '预计费用',
  `required_skills` longtext COMMENT '所需技能',
  `required_tools` longtext COMMENT '所需工具',
  `required_materials` longtext COMMENT '所需材料',
  `safety_precautions` longtext COMMENT '安全注意事项',
  `quality_standards` longtext COMMENT '质量标准',
  `acceptance_criteria` longtext COMMENT '验收标准',
  `create_dept_id` varchar(32) DEFAULT NULL COMMENT '创建部门ID',
  `create_dept_name` varchar(100) DEFAULT NULL COMMENT '创建部门名称',
  `reviewed_by` varchar(32) DEFAULT NULL COMMENT '审核人ID',
  `reviewed_by_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `reviewed_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_status` int DEFAULT 0 COMMENT '审核状态(0-待审核,1-已审核,2-已拒绝)',
  `review_comments` longtext COMMENT '审核意见',
  `template_status` int DEFAULT 0 COMMENT '模板状态(0-草稿,1-生效,2-停用)',
  `version` varchar(20) DEFAULT '1.0' COMMENT '版本号',
  `effective_date` datetime DEFAULT NULL COMMENT '生效日期',
  `expiry_date` datetime DEFAULT NULL COMMENT '失效日期',
  `usage_count` int DEFAULT 0 COMMENT '使用次数',
  `attachments` longtext COMMENT '附件',
  `notes` longtext COMMENT '备注',
  `is_default` int DEFAULT 0 COMMENT '是否默认(0-否,1-是)',
  `is_public` int DEFAULT 0 COMMENT '是否公开(0-否,1-是)',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `ext1` varchar(255) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(255) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(255) DEFAULT NULL COMMENT '扩展字段3',
  `ext4` varchar(255) DEFAULT NULL COMMENT '扩展字段4',
  `ext5` varchar(255) DEFAULT NULL COMMENT '扩展字段5',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人ID',
  `is_deleted` int DEFAULT 0 COMMENT '逻辑删除(0-未删除,1-已删除)',
  PRIMARY KEY (`template_id`),
  KEY `idx_equipment_type_id` (`equipment_type_id`),
  KEY `idx_maintenance_type` (`maintenance_type`),
  KEY `idx_maintenance_cycle` (`maintenance_cycle`),
  KEY `idx_template_status` (`template_status`),
  KEY `idx_review_status` (`review_status`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备保养模板表';

-- 插入测试数据

-- 保养模板测试数据
INSERT INTO `eq_maintenance_template` (`template_id`, `template_name`, `template_code`, `equipment_type_id`, `equipment_type_name`, `equipment_model`, `maintenance_type`, `maintenance_cycle`, `cycle_value`, `cycle_unit`, `maintenance_standard`, `maintenance_content`, `maintenance_items`, `maintenance_requirements`, `maintenance_steps`, `required_hours`, `estimated_cost`, `required_skills`, `required_tools`, `required_materials`, `safety_precautions`, `quality_standards`, `acceptance_criteria`, `template_status`, `version`, `usage_count`, `is_default`, `is_public`, `sort_order`, `tenant_id`, `create_by`) VALUES
('mt_001', '空调设备日常保养模板', 'HVAC_DAILY_001', 'et_001', '空调设备', 'VRV系列', 'DAILY', 'DAILY', 1, 'DAY', '按照制造商要求执行日常保养', '1.检查空调外观清洁度\n2.检查空调运行状态\n3.检查过滤网清洁度\n4.检查排水管道通畅性\n5.记录运行参数', '[{"item":"外观检查","standard":"无灰尘、无异物","method":"目视检查"},{"item":"运行状态","standard":"正常启停、无异响","method":"操作测试"},{"item":"过滤网","standard":"无堵塞、清洁","method":"拆卸检查"},{"item":"排水管","standard":"通畅无堵塞","method":"通水测试"},{"item":"运行参数","standard":"温度、压力正常","method":"仪表读数"}]', '1.保养人员需具备基本电工知识\n2.保养时需关闭电源\n3.使用专用工具进行拆装\n4.保养完成后进行功能测试', '1.关闭电源并挂牌\n2.拆卸并清洗过滤网\n3.清洁空调外壳\n4.检查电气连接\n5.测试运行状态\n6.记录保养数据\n7.恢复设备运行', 2.00, 50.00, '电工基础知识、空调维护技能', '螺丝刀、扳手、清洁布、吸尘器', '清洁剂、润滑油', '断电操作、防触电、防坠落', '设备运行正常、外观清洁', '功能测试通过、记录完整', 1, '1.0', 0, 1, 1, 1, 'tenant_001', 'user_001'),
('mt_002', '电梯设备月度保养模板', 'ELEVATOR_MONTHLY_001', 'et_002', '电梯设备', 'OTIS系列', 'LEVEL_ONE', 'MONTHLY', 1, 'MONTH', '按照电梯安全规程执行月度保养', '1.检查电梯门系统\n2.检查电梯轿厢\n3.检查电梯机房\n4.检查电梯井道\n5.检查安全装置\n6.功能测试', '[{"item":"门系统","standard":"开关正常、无卡阻","method":"手动操作"},{"item":"轿厢","standard":"运行平稳、无异响","method":"乘坐测试"},{"item":"机房","standard":"设备正常、无漏油","method":"目视检查"},{"item":"井道","standard":"无异物、导轨润滑","method":"检查清理"},{"item":"安全装置","standard":"动作可靠、反应灵敏","method":"测试验证"}]', '1.保养人员需持有电梯操作证\n2.保养时需悬挂停用标识\n3.严格按照安全操作规程\n4.保养完成后进行全面测试', '1.停用电梯并挂牌\n2.检查机房设备\n3.检查井道设施\n4.检查轿厢门系统\n5.润滑保养\n6.安全装置测试\n7.功能验证\n8.记录保养情况', 4.00, 200.00, '电梯维护技能、安全操作证', '专用工具、润滑枪、测试仪器', '润滑油、清洁剂、备用配件', '高空作业安全、电气安全、机械安全', '运行平稳、安全可靠', '安全测试通过、记录完整', 1, '1.0', 0, 1, 1, 2, 'tenant_001', 'user_001'),
('mt_003', '消防设备季度保养模板', 'FIRE_QUARTERLY_001', 'et_003', '消防设备', '通用型', 'QUARTERLY', 'QUARTERLY', 1, 'QUARTER', '按照消防设备保养规程执行', '1.检查消防泵\n2.检查消防栓\n3.检查灭火器\n4.检查报警系统\n5.检查应急照明\n6.系统联动测试', '[{"item":"消防泵","standard":"启动正常、压力达标","method":"试运行"},{"item":"消防栓","standard":"出水正常、压力充足","method":"放水测试"},{"item":"灭火器","standard":"压力正常、无过期","method":"检查标识"},{"item":"报警系统","standard":"报警及时、声光正常","method":"模拟测试"},{"item":"应急照明","standard":"照明正常、电池充足","method":"断电测试"}]', '1.保养人员需具备消防设备知识\n2.保养时需通知消防控制室\n3.严格按照消防安全规程\n4.测试时避免误报', '1.通知消防控制室\n2.检查消防泵房\n3.检查消防栓箱\n4.检查灭火器配置\n5.测试报警系统\n6.检查应急照明\n7.系统联动测试\n8.恢复正常状态', 6.00, 300.00, '消防设备知识、系统操作技能', '压力表、测试仪、工具箱', '密封圈、润滑剂、清洁用品', '消防安全、防误报、防触电', '系统功能正常、无误报', '联动测试通过、记录完整', 1, '1.0', 0, 1, 1, 3, 'tenant_001', 'user_001');

-- 保养计划测试数据
INSERT INTO `eq_maintenance_plan` (`plan_id`, `plan_name`, `plan_code`, `equipment_id`, `equipment_name`, `equipment_code`, `maintenance_type`, `maintenance_cycle`, `cycle_value`, `cycle_unit`, `maintenance_standard`, `maintenance_content`, `maintenance_requirements`, `required_hours`, `estimated_cost`, `responsible_person_id`, `responsible_person_name`, `execute_dept_id`, `execute_dept_name`, `priority`, `start_time`, `end_time`, `effective_date`, `expiry_date`, `plan_status`, `auto_generate`, `remind_hours`, `execution_rule`, `next_execution_time`, `execution_count`, `enabled`, `sort_order`, `tenant_id`, `create_by`) VALUES
('mp_001', '1号楼空调日常保养', 'HVAC_001_DAILY', 'eq_001', '1号楼中央空调', 'HVAC_001', 'DAILY', 'DAILY', 1, 'DAY', '按照制造商要求执行日常保养', '检查空调运行状态、清洁过滤网、记录运行参数', '保养人员需具备基本电工知识，保养时需关闭电源', 2.00, 50.00, 'user_001', '张三', 'dept_001', '设备部', 2, '08:00:00', '10:00:00', '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1, 1, 2, '{"workdays_only":true,"skip_holidays":true}', '2024-12-08 08:00:00', 0, 1, 1, 'tenant_001', 'user_001'),
('mp_002', '办公楼电梯月度保养', 'ELEVATOR_001_MONTHLY', 'eq_002', '办公楼客梯', 'ELEVATOR_001', 'LEVEL_ONE', 'MONTHLY', 1, 'MONTH', '按照电梯安全规程执行月度保养', '检查电梯门系统、轿厢、机房、井道、安全装置', '保养人员需持有电梯操作证，严格按照安全操作规程', 4.00, 200.00, 'user_002', '李四', 'dept_001', '设备部', 3, '06:00:00', '10:00:00', '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1, 1, 24, '{"first_day_of_month":true}', '2024-12-15 06:00:00', 0, 1, 2, 'tenant_001', 'user_001'),
('mp_003', '消防设备季度保养', 'FIRE_001_QUARTERLY', 'eq_003', '消防泵房设备', 'FIRE_001', 'QUARTERLY', 'QUARTERLY', 1, 'QUARTER', '按照消防设备保养规程执行', '检查消防泵、消防栓、灭火器、报警系统、应急照明', '保养人员需具备消防设备知识，保养时需通知消防控制室', 6.00, 300.00, 'user_003', '王五', 'dept_002', '消防部', 4, '09:00:00', '15:00:00', '2024-01-01 00:00:00', '2024-12-31 23:59:59', 1, 1, 48, '{"first_day_of_quarter":true}', '2024-12-20 09:00:00', 0, 1, 3, 'tenant_001', 'user_001');

-- 保养任务测试数据
INSERT INTO `eq_maintenance_task` (`task_id`, `task_number`, `task_name`, `plan_id`, `plan_name`, `equipment_id`, `equipment_name`, `equipment_code`, `maintenance_type`, `maintenance_content`, `maintenance_requirements`, `scheduled_start_time`, `scheduled_end_time`, `required_hours`, `estimated_cost`, `executor_id`, `executor_name`, `execute_dept_id`, `execute_dept_name`, `task_status`, `priority`, `completion_percentage`, `is_urgent`, `tenant_id`, `create_by`) VALUES
('mt_001', 'TASK_20241207_001', '1号楼空调日常保养-12月7日', 'mp_001', '1号楼空调日常保养', 'eq_001', '1号楼中央空调', 'HVAC_001', 'DAILY', '检查空调运行状态、清洁过滤网、记录运行参数', '保养人员需具备基本电工知识，保养时需关闭电源', '2024-12-07 08:00:00', '2024-12-07 10:00:00', 2.00, 50.00, 'user_001', '张三', 'dept_001', '设备部', 'PENDING', 2, 0.00, 0, 'tenant_001', 'user_001'),
('mt_002', 'TASK_20241207_002', '办公楼电梯月度保养-12月', 'mp_002', '办公楼电梯月度保养', 'eq_002', '办公楼客梯', 'ELEVATOR_001', 'LEVEL_ONE', '检查电梯门系统、轿厢、机房、井道、安全装置', '保养人员需持有电梯操作证，严格按照安全操作规程', '2024-12-15 06:00:00', '2024-12-15 10:00:00', 4.00, 200.00, 'user_002', '李四', 'dept_001', '设备部', 'PLANNED', 3, 0.00, 0, 'tenant_001', 'user_001'),
('mt_003', 'TASK_20241207_003', '消防设备季度保养-Q4', 'mp_003', '消防设备季度保养', 'eq_003', '消防泵房设备', 'FIRE_001', 'QUARTERLY', '检查消防泵、消防栓、灭火器、报警系统、应急照明', '保养人员需具备消防设备知识，保养时需通知消防控制室', '2024-12-20 09:00:00', '2024-12-20 15:00:00', 6.00, 300.00, 'user_003', '王五', 'dept_002', '消防部', 'PLANNED', 4, 0.00, 1, 'tenant_001', 'user_001');

-- 保养记录测试数据
INSERT INTO `eq_maintenance_record` (`record_id`, `record_number`, `task_id`, `task_number`, `plan_id`, `equipment_id`, `equipment_name`, `equipment_code`, `maintenance_type`, `maintenance_items`, `maintenance_content`, `executor_id`, `executor_name`, `execute_dept_id`, `execute_dept_name`, `start_time`, `end_time`, `actual_hours`, `before_maintenance_status`, `after_maintenance_status`, `maintenance_result`, `found_problems`, `solutions`, `parts_cost`, `labor_cost`, `total_cost`, `quality_evaluation`, `evaluation_score`, `is_qualified`, `tenant_id`, `create_by`) VALUES
('mr_001', 'RECORD_20241206_001', 'mt_001', 'TASK_20241206_001', 'mp_001', 'eq_001', '1号楼中央空调', 'HVAC_001', 'DAILY', '外观检查、运行状态、过滤网、排水管、运行参数', '检查空调运行状态、清洁过滤网、记录运行参数', 'user_001', '张三', 'dept_001', '设备部', '2024-12-06 08:00:00', '2024-12-06 10:00:00', 2.00, '运行正常，过滤网有轻微积尘', '运行正常，过滤网已清洁', 'NORMAL', '过滤网积尘较多', '清洗过滤网，检查排水管道', 0.00, 100.00, 100.00, '保养质量良好，设备运行正常', 95.00, 1, 'tenant_001', 'user_001'),
('mr_002', 'RECORD_20241205_001', 'mt_002', 'TASK_20241205_001', 'mp_002', 'eq_002', '办公楼客梯', 'ELEVATOR_001', 'LEVEL_ONE', '门系统、轿厢、机房、井道、安全装置', '检查电梯门系统、轿厢、机房、井道、安全装置', 'user_002', '李四', 'dept_001', '设备部', '2024-12-05 06:00:00', '2024-12-05 10:00:00', 4.00, '电梯运行正常，门系统略有异响', '电梯运行正常，门系统异响已排除', 'NORMAL', '门系统轻微异响', '调整门系统，添加润滑剂', 50.00, 200.00, 250.00, '保养质量优秀，设备运行平稳', 98.00, 1, 'tenant_001', 'user_001'),
('mr_003', 'RECORD_20241204_001', 'mt_003', 'TASK_20241204_001', 'mp_003', 'eq_003', '消防泵房设备', 'FIRE_001', 'QUARTERLY', '消防泵、消防栓、灭火器、报警系统、应急照明', '检查消防泵、消防栓、灭火器、报警系统、应急照明', 'user_003', '王五', 'dept_002', '消防部', '2024-12-04 09:00:00', '2024-12-04 15:00:00', 6.00, '消防设备运行正常，部分灭火器压力不足', '消防设备运行正常，已更换不合格灭火器', 'NORMAL', '3个灭火器压力不足', '更换3个压力不足的灭火器', 150.00, 300.00, 450.00, '保养质量良好，消防系统完好', 96.00, 1, 'tenant_001', 'user_001');