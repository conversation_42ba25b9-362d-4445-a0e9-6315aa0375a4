-- 护士工作台配置表
CREATE TABLE `nurse_workspace_config` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(50) NOT NULL COMMENT '护士用户ID',
    `workspace_name` VARCHAR(100) DEFAULT '我的工作台' COMMENT '工作台名称',
    `theme_config` JSON COMMENT '主题配置',
    `layout_config` JSON COMMENT '布局配置',
    `widget_config` JSON COMMENT '组件配置',
    `quick_actions` JSON COMMENT '快速操作配置',
    `notification_config` JSON COMMENT '通知配置',
    `display_preferences` JSON COMMENT '显示偏好',
    `voice_enabled` TINYINT DEFAULT 1 COMMENT '是否启用语音功能',
    `offline_mode` TINYINT DEFAULT 1 COMMENT '是否启用离线模式',
    `auto_sync` TINYINT DEFAULT 1 COMMENT '是否自动同步',
    `language` VARCHAR(10) DEFAULT 'zh_CN' COMMENT '语言设置',
    `font_size` ENUM('SMALL', 'MEDIUM', 'LARGE') DEFAULT 'MEDIUM' COMMENT '字体大小',
    `is_active` TINYINT DEFAULT 1 COMMENT '是否启用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_workspace` (`user_id`, `tenant_id`),
    INDEX `idx_user` (`user_id`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='护士工作台配置表';

-- 护士任务概览表
CREATE TABLE `nurse_task_overview` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(50) NOT NULL COMMENT '护士用户ID',
    `task_date` DATE NOT NULL COMMENT '任务日期',
    `shift_type` ENUM('DAY', 'NIGHT', 'EVENING') NOT NULL COMMENT '班次类型',
    `total_tasks` INT DEFAULT 0 COMMENT '总任务数',
    `completed_tasks` INT DEFAULT 0 COMMENT '已完成任务数',
    `pending_tasks` INT DEFAULT 0 COMMENT '待完成任务数',
    `overdue_tasks` INT DEFAULT 0 COMMENT '逾期任务数',
    `exception_count` INT DEFAULT 0 COMMENT '异常数量',
    `emergency_count` INT DEFAULT 0 COMMENT '紧急事件数量',
    `work_duration` INT DEFAULT 0 COMMENT '工作时长(分钟)',
    `efficiency_score` DECIMAL(5,2) DEFAULT 0.00 COMMENT '效率评分',
    `quality_score` DECIMAL(5,2) DEFAULT 0.00 COMMENT '质量评分',
    `department_id` VARCHAR(50) COMMENT '科室ID',
    `ward_id` VARCHAR(50) COMMENT '病区ID',
    `shift_start_time` TIMESTAMP NULL COMMENT '班次开始时间',
    `shift_end_time` TIMESTAMP NULL COMMENT '班次结束时间',
    `last_updated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_date_shift` (`user_id`, `task_date`, `shift_type`, `tenant_id`),
    INDEX `idx_user_date` (`user_id`, `task_date`),
    INDEX `idx_shift` (`shift_type`),
    INDEX `idx_department` (`department_id`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='护士任务概览表';

-- 护士个人统计表
CREATE TABLE `nurse_personal_statistics` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(50) NOT NULL COMMENT '护士用户ID',
    `statistic_type` ENUM('DAILY', 'WEEKLY', 'MONTHLY') NOT NULL COMMENT '统计类型',
    `statistic_date` DATE NOT NULL COMMENT '统计日期',
    `total_inspections` INT DEFAULT 0 COMMENT '总点检次数',
    `equipment_count` INT DEFAULT 0 COMMENT '负责设备数量',
    `exception_found` INT DEFAULT 0 COMMENT '发现异常数量',
    `exception_resolved` INT DEFAULT 0 COMMENT '解决异常数量',
    `average_inspection_time` DECIMAL(10,2) DEFAULT 0.00 COMMENT '平均点检时间(分钟)',
    `accuracy_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '准确率',
    `timeliness_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '及时率',
    `comprehensive_score` DECIMAL(5,2) DEFAULT 0.00 COMMENT '综合评分',
    `department_ranking` INT COMMENT '科室排名',
    `hospital_ranking` INT COMMENT '全院排名',
    `improvement_suggestions` JSON COMMENT '改进建议',
    `achievements` JSON COMMENT '成就记录',
    `work_hours` DECIMAL(10,2) DEFAULT 0.00 COMMENT '工作时长',
    `overtime_hours` DECIMAL(10,2) DEFAULT 0.00 COMMENT '加班时长',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_type_date` (`user_id`, `statistic_type`, `statistic_date`, `tenant_id`),
    INDEX `idx_user_type` (`user_id`, `statistic_type`),
    INDEX `idx_date` (`statistic_date`),
    INDEX `idx_ranking` (`department_ranking`, `hospital_ranking`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='护士个人统计表';

-- 护士快速操作配置表
CREATE TABLE `nurse_quick_actions` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `user_id` VARCHAR(50) NOT NULL COMMENT '护士用户ID',
    `action_code` VARCHAR(50) NOT NULL COMMENT '操作代码',
    `action_name` VARCHAR(100) NOT NULL COMMENT '操作名称',
    `action_type` ENUM('SCAN', 'REPORT', 'QUERY', 'CUSTOM') NOT NULL COMMENT '操作类型',
    `action_icon` VARCHAR(100) COMMENT '操作图标',
    `action_color` VARCHAR(20) COMMENT '操作颜色',
    `action_config` JSON COMMENT '操作配置',
    `display_order` INT DEFAULT 0 COMMENT '显示顺序',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `last_used` TIMESTAMP NULL COMMENT '最后使用时间',
    `is_enabled` TINYINT DEFAULT 1 COMMENT '是否启用',
    `is_system_default` TINYINT DEFAULT 0 COMMENT '是否系统默认',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_action` (`user_id`, `action_code`, `tenant_id`),
    INDEX `idx_user` (`user_id`),
    INDEX `idx_action_type` (`action_type`),
    INDEX `idx_display_order` (`display_order`),
    INDEX `idx_usage_count` (`usage_count`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='护士快速操作配置表';

-- 护士排行榜表
CREATE TABLE `nurse_ranking_board` (
    `id` VARCHAR(50) NOT NULL COMMENT '主键ID',
    `ranking_type` ENUM('DAILY', 'WEEKLY', 'MONTHLY') NOT NULL COMMENT '排行类型',
    `ranking_period` VARCHAR(20) NOT NULL COMMENT '排行周期(如2024-01)',
    `user_id` VARCHAR(50) NOT NULL COMMENT '护士用户ID',
    `user_name` VARCHAR(100) COMMENT '护士姓名',
    `department_id` VARCHAR(50) COMMENT '科室ID',
    `department_name` VARCHAR(100) COMMENT '科室名称',
    `ranking_category` ENUM('EFFICIENCY', 'QUALITY', 'COMPREHENSIVE', 'INNOVATION') NOT NULL COMMENT '排行类别',
    `score` DECIMAL(10,2) NOT NULL COMMENT '得分',
    `ranking_position` INT NOT NULL COMMENT '排名位置',
    `previous_position` INT COMMENT '上期排名',
    `position_change` INT DEFAULT 0 COMMENT '排名变化',
    `details` JSON COMMENT '详细信息',
    `badges` JSON COMMENT '获得徽章',
    `is_top_performer` TINYINT DEFAULT 0 COMMENT '是否优秀表现者',
    `calculated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',
    `tenant_id` VARCHAR(50) COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_period_user_category` (`ranking_period`, `user_id`, `ranking_category`, `tenant_id`),
    INDEX `idx_ranking_type` (`ranking_type`),
    INDEX `idx_category_position` (`ranking_category`, `ranking_position`),
    INDEX `idx_user` (`user_id`),
    INDEX `idx_department` (`department_id`),
    INDEX `idx_score` (`score`),
    INDEX `idx_tenant` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='护士排行榜表';

-- 插入默认的护士快速操作
INSERT INTO `nurse_quick_actions` (`id`, `user_id`, `action_code`, `action_name`, `action_type`, `action_icon`, `action_color`, `action_config`, `display_order`, `is_system_default`, `tenant_id`) VALUES
('QUICK_001', 'DEFAULT', 'SCAN_EQUIPMENT', '扫码点检', 'SCAN', 'qrcode-scan', '#1890FF', '{"targetPage": "/scan", "autoStart": true}', 1, 1, 'DEFAULT'),
('QUICK_002', 'DEFAULT', 'REPORT_EXCEPTION', '异常报告', 'REPORT', 'exclamation-circle', '#FF4D4F', '{"targetPage": "/exception/report", "voice": true}', 2, 1, 'DEFAULT'),
('QUICK_003', 'DEFAULT', 'QUERY_EQUIPMENT', '设备查询', 'QUERY', 'search', '#52C41A', '{"targetPage": "/equipment/search"}', 3, 1, 'DEFAULT'),
('QUICK_004', 'DEFAULT', 'TASK_LIST', '我的任务', 'QUERY', 'unordered-list', '#722ED1', '{"targetPage": "/tasks/my"}', 4, 1, 'DEFAULT'),
('QUICK_005', 'DEFAULT', 'VOICE_INPUT', '语音输入', 'CUSTOM', 'audio', '#FA8C16', '{"feature": "voice", "autoActivate": true}', 5, 1, 'DEFAULT'),
('QUICK_006', 'DEFAULT', 'OFFLINE_SYNC', '离线同步', 'CUSTOM', 'cloud-sync', '#13C2C2', '{"feature": "sync", "autoCheck": true}', 6, 1, 'DEFAULT');