spring:
  profiles:
    active: dev
  application:
    name: equipment
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
      file-size-threshold: 10MB
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration
  jackson:
    local-date-time-format: yyyy-MM-dd HH:mm:ss
    local-date-format: yyyy-MM-dd
  mvc:
    async:
      request-timeout: 80000

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: org.simple.equipment.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Sa-Token配置
sa-token:
  token-name: Authorization
  timeout: 2592000
  activity-timeout: 1800
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-version: true
    enable-request-cache: false
    enable-filter-multipart-api-method-type: POST
  production: false

server:
  port: 8081