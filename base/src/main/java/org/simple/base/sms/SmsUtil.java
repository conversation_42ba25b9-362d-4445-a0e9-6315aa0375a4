package org.simple.base.sms;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class SmsUtil {

    private static RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        SmsUtil.redisTemplate = redisTemplate;
    }

    public static AliSms getAliSms() {
        return AliSms.getInstance(redisTemplate);
    }

    public static TencentSms getTencentSms() {
        return TencentSms.getInstance(redisTemplate);
    }
}
