package org.simple.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/1/9 19:13
 * @Description 延迟队列执行任务枚举
 */
@Getter
@AllArgsConstructor
public enum RedisDelayQueueEnum {

    AUTH_LOCK_TIMEOUT_QUEUE("auth_lock_timeout_queue", "延迟队列", "lockTimeOutHandler");

    private final String queueName;
    private final String desc;
    private final String beanId;

}
