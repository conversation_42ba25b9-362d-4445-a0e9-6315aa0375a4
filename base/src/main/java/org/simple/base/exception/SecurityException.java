package org.simple.base.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SecurityException extends Exception{

    /**
     * 异常信息
     */
    private String errorMessage;

    /**
     * 自定义异常
     *
     * @param msg 异常信息
     */
    public SecurityException(String msg) {
        this.errorMessage = msg;
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }
}
