package org.simple.base.vo;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.simple.base.constant.IErrorCode;
import org.simple.base.enums.ResultCodeEnum;


/**
 * 公共接口返回对象
 */

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "返回结果")
public class FrResult<T> {
    @Schema(description = "返回码")
    private long code;
    @Schema(description = "返回信息")
    private String msg;
    @Schema(description = "返回数据", type = "object")
    private T data;
    @Schema(description = "license是否过期")
    private Long exp;
    @Schema(description = "密码失效天数")
    private Long pwdExp;

    protected FrResult() {
    }

    protected FrResult(long code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.exp = null;
    }

    protected FrResult(long code, String msg, T data, long exp) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.exp = exp;
    }

    protected FrResult(long exp, long code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.pwdExp = exp;
    }


    /**
     * 成功返回结果
     *
     * @param data 获取的数据
     */
    public static <T> FrResult<T> success(T data) {
        return new FrResult<T>(ResultCodeEnum.SUCCESS.getCode(),
                ResultCodeEnum.SUCCESS.getMsg(), data);
    }


    public static <T> FrResult<T> success() {
        return new FrResult<T>(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getMsg(), null);
    }

    public static <T> FrResult<T> expLicense(FrResult<T> frResult) {
        return new FrResult<T>(frResult.getCode(), frResult.getMsg(), frResult.getData(), -1);
    }

    public static <T> FrResult<T> expPwd(FrResult<T> frResult, long expSec) {
        return new FrResult<T>(expSec, frResult.getCode(),
                frResult.getMsg(), frResult.getData());
    }

    public static <T> FrResult<T> xssAndSql() {
        return new FrResult<T>(ResultCodeEnum.XSSANDSQL.getCode(), ResultCodeEnum.XSSANDSQL.getMsg(), null);
    }

    public static <T> FrResult<T> rateLimit() {
        return new FrResult<T>(ResultCodeEnum.RATELIMIT.getCode(), ResultCodeEnum.RATELIMIT.getMsg(), null);
    }

    public static <T> FrResult<T> rateLimit(String msg) {
        return new FrResult<T>(ResultCodeEnum.RATELIMIT.getCode(), msg, null);
    }

    /**
     * 成功返回结果
     *
     * @param data    获取的数据
     * @param message 提示信息
     */
    public static <T> FrResult<T> success(T data, String message) {
        return new FrResult<T>(ResultCodeEnum.SUCCESS.getCode(), message, data);
    }

    public static <T> FrResult<T> successNodata(String message) {
        return new FrResult<T>(ResultCodeEnum.SUCCESS.getCode(), message, null);
    }

    /**
     * 失败返回结果
     *
     * @param errorCode 错误码
     */
    public static <T> FrResult<T> failed(IErrorCode errorCode) {
        return new FrResult<T>(errorCode.getCode(), errorCode.getMsg(), null);
    }

    /**
     * 失败返回结果
     *
     * @param errorCode 错误码
     * @param message   错误信息
     */
    public static <T> FrResult<T> failed(IErrorCode errorCode, String message) {
        return new FrResult<T>(errorCode.getCode(), StrUtil.isEmpty(message) ? "系统错误" : message, null);
    }

    /**
     * 失败返回结果
     *
     * @param message 提示信息
     */
    public static <T> FrResult<T> failed(String message) {
        return new FrResult<T>(ResultCodeEnum.FAILED.getCode(), StrUtil.isEmpty(message) ? "系统错误" : message, null);
    }

    /**
     * 失败返回结果
     */
    public static <T> FrResult<T> failed() {
        return failed(ResultCodeEnum.FAILED);
    }

    /**
     * 参数验证失败返回结果
     */
    public static <T> FrResult<T> validateFailed() {
        return failed(ResultCodeEnum.VALIDATE_FAILED);
    }

    /**
     * 参数验证失败返回结果
     *
     * @param message 提示信息
     */
    public static <T> FrResult<T> validateFailed(String message) {
        return new FrResult<T>(ResultCodeEnum.VALIDATE_FAILED.getCode(), message, null);
    }

    /**
     * 未登录返回结果
     */
    public static <T> FrResult<T> tokenTimeOut() {
        return new FrResult<T>(ResultCodeEnum.TOKEN_TIMEOUT.getCode(),
                ResultCodeEnum.TOKEN_TIMEOUT.getMsg(), null);
    }

    /**
     * 未登录返回结果
     */
    public static <T> FrResult<T> unauthorized(String msg) {
        return new FrResult<T>(ResultCodeEnum.FORBIDDEN.getCode(),
                StringUtils.isNotEmpty(msg) ? msg : ResultCodeEnum.FORBIDDEN.getMsg(), null);
    }


    /**
     * 未授权返回结果
     */
    public static <T> FrResult<T> forbidden(String msg) {
        return new FrResult<T>(ResultCodeEnum.FORBIDDEN.getCode(),
                StringUtils.isNotEmpty(msg) ? msg :
                        ResultCodeEnum.FORBIDDEN.getMsg(), null);
    }
}