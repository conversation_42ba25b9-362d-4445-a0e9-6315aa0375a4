package org.simple.base.util;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingDeque;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @author: kqyin
 * @date: 2023/1/8 16:51
 * @Description: 延迟队列增删工具类
 */
@Slf4j
@Component
public class RedisDelayQueueUtil {

    // 注入RedissonClient实例
    @Resource
    private RedissonClient redissonClient;

    /**
     * <AUTHOR>
     * @Date 2023/4/20
     * @Description 添加延迟队列
     */
    public <T> boolean addDelayQueue(@NonNull T value, long delay,
                                     @NonNull TimeUnit timeUnit,
                                     @NonNull String queueName) {
        if (StrUtil.isBlank(queueName)) {
            return false;
        }
        try {
            RBlockingDeque<Object> blockingDeque = redissonClient.getBlockingDeque(queueName);
            RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
            delayedQueue.offer(value, delay, timeUnit);
            log.info("(添加延时队列成功) 队列名：{}，队列值：{}，延迟时间：{}", queueName, value, timeUnit.toSeconds(delay) + "秒");
        } catch (Exception e) {
            log.error("(添加延时队列失败) {}", e.getMessage());
            throw new RuntimeException("(添加延时队列失败)");
        }
        return true;
    }

    /**
     * 获取延迟队列
     */
    public <T> T getDelayQueue(@NonNull String queueName) throws InterruptedException {
        if (StrUtil.isBlank(queueName)) {
            return null;
        }
        RBlockingDeque<Object> blockingDeque = redissonClient.getBlockingDeque(queueName);
        RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
        return (T) delayedQueue.poll();
    }

    /**
     * 删除指定队列中的消息
     *
     * @param o         指定删除的消息对象队列值(同队列需保证唯一性)
     * @param queueName 指定队列键
     */
    public boolean removeDelayedQueue(@NonNull Object o, @NonNull String queueName) {
        if (StrUtil.isBlank(queueName)) {
            return false;
        }
        RBlockingDeque<Object> blockingDeque = redissonClient.getBlockingDeque(queueName);
        RDelayedQueue<Object> delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
        return delayedQueue.remove(o);
    }
}
