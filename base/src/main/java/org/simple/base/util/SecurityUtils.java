package org.simple.base.util;

import org.simple.base.dto.UserDto;
import org.springframework.stereotype.Component;

/**
 * 安全工具类
 * 提供用户认证和授权相关的工具方法
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Component
public class SecurityUtils {

    /**
     * 获取当前登录用户信息
     *
     * @return 当前用户信息
     */
    public static UserDto getLoginUser() {
        return AuthUtil.getUser();
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 当前用户ID
     */
    public static String getLoginUserId() {
        return AuthUtil.getUserId();
    }

    /**
     * 获取当前用户的租户ID
     *
     * @return 租户ID
     */
    public static String getTenantId() {
        try {
            UserDto user = AuthUtil.getUser();
            return user != null ? user.getTenantId() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前用户名
     *
     * @return 用户名
     */
    public static String getUsername() {
        try {
            UserDto user = AuthUtil.getUser();
            return user != null ? user.getUserName() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前用户昵称
     *
     * @return 用户昵称
     */
    public static String getNickname() {
        try {
            UserDto user = AuthUtil.getUser();
            return user != null ? user.getNickName() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前用户部门ID
     *
     * @return 部门ID
     */
    public static String getDeptId() {
        try {
            UserDto user = AuthUtil.getUser();
            return user != null ? user.getOrgan() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 判断当前用户是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isLogin() {
        return AuthUtil.isLogin();
    }

    /**
     * 获取当前用户的角色列表
     *
     * @return 角色列表
     */
    public static java.util.List<String> getRoles() {
        return AuthUtil.getRoles();
    }

    /**
     * 获取当前用户的权限列表
     *
     * @return 权限列表
     */
    public static java.util.List<String> getPermissions() {
        return AuthUtil.getPermissions();
    }

    /**
     * 获取客户端ID
     *
     * @return 客户端ID
     */
    public static String getClientId() {
        return AuthUtil.getClientId();
    }
}
