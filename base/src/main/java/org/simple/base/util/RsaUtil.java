package org.simple.base.util;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

public class RsaUtil {

    private static String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCh97Ua2shkO9UOovRWEo0qmpPrlKpmKGnHK3pDfrqUtYzGNTfyPlHxZ+2Uq7VHTYmvX2XGP2yg6J9AHcgX0JXYb80in5CCsQ7GcMS3b31XBXihZkHKkXfCj1nSP5dD0kd4CTb7xiKZd9IyuH8s9alMVx/E2fpmgIIUaYY33Uj/twIDAQAB";
    private static String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKH3tRrayGQ71Q6i9FYSjSqak+uUqmYoaccrekN+upS1jMY1N/I+UfFn7ZSrtUdNia9fZcY/bKDon0AdyBfQldhvzSKfkIKxDsZwxLdvfVcFeKFmQcqRd8KPWdI/l0PSR3gJNvvGIpl30jK4fyz1qUxXH8TZ+maAghRphjfdSP+3AgMBAAECgYAbBLvY3XtFQTfi56sTuqeC3mNjjpq4TPCMUYPlQ3wzd2+i1tjc16mKuDgJL3Wfjd80epj7L7RayUNbijxJQLnmK3cDYuZve6UxqtlR8FNyj9GfeoDjY4PPnSow0E0GZLvswDD/PKjpw4T8ISVqXg6zCbrn1NZu4Mk421vPU4A+ZQJBANlzsIzNhFpAiGk/x9R/ew5S4vHDbTTL6rNPhy4auW+787ji86+zhOirwnDdALwuyjtYQu5Asmk0znbAhsf82tsCQQC+rg13d9pbnRJ8TBj6lILrEmsf8KarW4KZ1blQIRJMqqY60IDUqlZcY6VHoec0jD5yy8qQhiKuf9NwrayCfI9VAkBCQdb8wC5g1aL69t0rqYBUV/sgkVx4Jt4nrsbQzIHOQuW7YyO/WKLZxmQjwaNUs6kZNgaG1B3Iord6RaWL0EbbAkEAhjiWmiXvutVSEdSvMJQho78PUShg6fY8dHURQOpGq1jkMjpatVhB+j7aNRYuXbrqqnvxVtJUz2iDOanG6QMtEQJBAJtZoiGWMHVcYiN+aJ/UnKhSfThLWly6DSkx4a1Ldd0OVKM+5eVbOSRlB9bLb+DlHCxlz9ArKBt/TMKt/wlicrc=";

    public static String encrypt(String data) {
        RSA rsa = new RSA(privateKey, publicKey);
        return rsa.encryptBase64(data, KeyType.PublicKey);
    }

    public static String decrypt(String data) {
        RSA rsa = new RSA(privateKey, publicKey);
        return rsa.decryptStr(data, KeyType.PrivateKey);
    }
}
