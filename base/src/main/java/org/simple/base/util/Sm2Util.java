package org.simple.base.util;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.BCUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import com.alibaba.fastjson2.JSONObject;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECKeyGenerationParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.util.BigIntegers;
import org.bouncycastle.util.encoders.Hex;
import org.simple.base.properties.Sm2Properties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.Base64;

@Component
public class Sm2Util {

    private static Sm2Properties sm2Properties;

    @Autowired
    private Sm2Util(Sm2Properties sm2Properties) {
        Sm2Util.sm2Properties = sm2Properties;
    }

    public static String encrypt(String str) {
        String xHex = sm2Properties.getPublicKey().substring(2).substring(0, 64);
        String yHex = sm2Properties.getPublicKey().substring(2).substring(64, 128);
        ECPublicKeyParameters ecPublicKeyParameters = BCUtil.toSm2Params(xHex, yHex);
        //创建sm2 对象
        SM2 sm2 = new SM2(null, ecPublicKeyParameters);
        sm2.usePlainEncoding();
        sm2.setMode(SM2Engine.Mode.C1C2C3);
        String hex = sm2.encryptHex(str, KeyType.PublicKey).substring(2);
        return cn.hutool.core.codec.Base64.encode(HexUtil.decodeHex(hex));
    }

    public static String decrypt(String str) {
        ECPrivateKeyParameters privateKeyParameters = BCUtil.toSm2Params(sm2Properties.getPrivateKey());
        //创建sm2 对象
        SM2 sm2 = new SM2(privateKeyParameters, null);
        sm2.usePlainEncoding();
        sm2.setMode(SM2Engine.Mode.C1C2C3);
        return sm2.decryptStr("04" + HexUtil.encodeHexStr(Base64.getDecoder().decode(str)), KeyType.PrivateKey);
    }

    public static void main(String[] args) {
        JSONObject result = generate();
        validate(result.getString("privateKey"), result.getString("publicKey"));
    }

    private static JSONObject generate() {
        JSONObject result = new JSONObject();
        // 获取国密曲线
        X9ECParameters gmParameters = GMNamedCurves.getByName("sm2p256v1");
        ECDomainParameters gmDomainParameters = new ECDomainParameters(gmParameters.getCurve(),
                gmParameters.getG(), gmParameters.getN());
        ECKeyPairGenerator keyPairGenerator = new ECKeyPairGenerator();
        keyPairGenerator.init(new ECKeyGenerationParameters(gmDomainParameters, new SecureRandom()));
        // 生成密钥对
        AsymmetricCipherKeyPair keyPair = keyPairGenerator.generateKeyPair();
        ECPrivateKeyParameters ecpriv = (ECPrivateKeyParameters) keyPair.getPrivate();
        ECPublicKeyParameters ecpub = (ECPublicKeyParameters) keyPair.getPublic();
        String privateKey = Hex.toHexString(BigIntegers.asUnsignedByteArray(32, ecpriv.getD()));
        String publicKey = Hex.toHexString(ecpub.getQ().getEncoded(false));
        System.out.println("私钥：" + privateKey);
        System.out.println("公钥：" + publicKey);
        result.put("privateKey", privateKey);
        result.put("publicKey", publicKey);
        return result;
    }

    private static void validate(String privateKey, String publicKey) {
        String decStr = "123456";
        String xHex = publicKey.substring(2).substring(0, 64);
        String yHex = publicKey.substring(2).substring(64, 128);
        ECPublicKeyParameters ecPublicKeyParameters = BCUtil.toSm2Params(xHex, yHex);
        //创建sm2 对象
        SM2 sm21 = new SM2(null, ecPublicKeyParameters);
        sm21.usePlainEncoding();
        sm21.setMode(SM2Engine.Mode.C1C2C3);
        String hex = sm21.encryptHex(decStr, KeyType.PublicKey).substring(2);
        String enStr = cn.hutool.core.codec.Base64.encode(HexUtil.decodeHex(hex));
        System.out.println("加密：" + enStr);

        ECPrivateKeyParameters privateKeyParameters = BCUtil.toSm2Params(privateKey);
        //创建sm2 对象
        SM2 sm2 = new SM2(privateKeyParameters, null);
        sm2.usePlainEncoding();
        sm2.setMode(SM2Engine.Mode.C1C2C3);
        System.out.println("解密：" + sm2.decryptStr("04" +
                HexUtil.encodeHexStr(Base64.getDecoder().decode(enStr)), KeyType.PrivateKey));

    }
}
