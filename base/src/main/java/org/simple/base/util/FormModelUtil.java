package org.simple.base.util;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.simple.base.constant.CommonConst;
import org.simple.base.vo.model.FormModelVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class FormModelUtil {
    private static RedisTemplate<String, ?> redisTemplate;

    private static final ObjectMapper objectMapper =
            ObjectMapperUtil.objectMapper();

    private FormModelUtil() {
    }

    @Autowired
    private void setRedisTemplate(RedisTemplate<String, ?> redisTemplate) {
        FormModelUtil.redisTemplate = redisTemplate;
    }

    public static FormModelVo getFormModel(String modelId) {
        if (redisTemplate.hasKey(CommonConst.SIMPLE_FORM_MODEL + modelId)) {
            String o = redisTemplate.opsForValue().get(CommonConst.SIMPLE_FORM_MODEL + modelId).toString();
            try {
                return objectMapper.readValue(o, FormModelVo.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        } else {
            return new FormModelVo();
        }
    }

    public static void delNotUsedKey(List<String> newKeys) {
        //删除缓存中已经不存在的系统参数
        Set<String> keySet =
                redisTemplate.keys(CommonConst.SIMPLE_FORM_MODEL + "*");
        if (!newKeys.isEmpty()) {
            if (CollUtil.isNotEmpty(keySet)) {
                for (String key : keySet) {
                    if (!newKeys.contains(key)) {
                        redisTemplate.delete(key);
                    }
                }
            }
        } else {
            if (CollUtil.isNotEmpty(keySet)) {
                redisTemplate.delete(keySet);
            }
        }
    }
}
