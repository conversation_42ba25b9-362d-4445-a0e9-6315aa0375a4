package org.simple.base.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FrQuickMapper {


    @Select("<script>" +
            "select t.* from (${obj.mainSql})t where 1=1 \n" +
            "   <if test=\"obj.param != null and obj.param.size() > 0\">\n" +
            "       <foreach collection=\"obj.param\" index=\"key\" item=\"item\">\n" +
            "    <choose>" +
            "    <when test=\"item.type == 'input' and item.con == 'like'\">\n" +
            "          and ${item.field} ${item.con}  CONCAT('%',#{item.value},'%')\n" +
            "    </when>" +
            "    <when test=\"item.type == 'input' and item.con != 'like'\">\n" +
            "          and ${item.field} ${item.con} #{item.value}\n" +
            "    </when>" +
            "    <when test=\"item.type == 'select'\">\n" +
            "          and ${item.field} = #{item.value}\n" +
            "    </when>" +
            "    <when test=\"item.type == 'date'\">\n" +
            "          and ${item.field} ${item.con} STR_TO_DATE(#{item.value}, '%Y-%m-%d')  \n" +
            "    </when>" +
            "    <when test=\"item.type == 'datetime'\">\n" +
            "          and ${item.field} ${item.con} STR_TO_DATE(#{item.value}, '%Y-%m-%d %H:%i:%s')  \n" +
            "    </when>" +
            "    <otherwise>\n" +
            "          and ${item.field} ${item.con} #{item.value}\n" +
            "    </otherwise>" +
            "    </choose>" +
            "      </foreach>\n" +
            "    </if>\n" +
            "    <if test=\"obj.sort != null and obj.sort != '' \">\n" +
            "        order by ${obj.sort} \n" +
            "    </if>" +
            "</script>")
    Page<JSONObject> pageList(Page<JSONObject> page, @Param("obj") JSONObject param);

    @Select("<script>" +
            "select t.* from (${obj.mainSql})t where 1=1 \n" +
            "   <if test=\"obj.param != null and obj.param.size() > 0\">\n" +
            "       <foreach collection=\"obj.param\" index=\"key\" item=\"item\">\n" +
            "    <choose>" +
            "    <when test=\"item.type == 'input' and item.con == 'like'\">\n" +
            "          and ${item.field} ${item.con}  CONCAT('%',#{item.value},'%')\n" +
            "    </when>" +
            "    <when test=\"item.type == 'input' and item.con != 'like'\">\n" +
            "          and ${item.field} ${item.con} #{item.value}\n" +
            "    </when>" +
            "    <when test=\"item.type == 'select'\">\n" +
            "          and ${item.field} = #{item.value}\n" +
            "    </when>" +
            "    <when test=\"item.type == 'date'\">\n" +
            "          and ${item.field} ${item.con} STR_TO_DATE(#{item.value}, '%Y-%m-%d')  \n" +
            "    </when>" +
            "    <when test=\"item.type == 'datetime'\">\n" +
            "          and ${item.field} ${item.con} STR_TO_DATE(#{item.value}, '%Y-%m-%d %H:%i:%s')  \n" +
            "    </when>" +
            "    <otherwise>\n" +
            "          and ${item.field} ${item.con} #{item.value}\n" +
            "    </otherwise>" +
            "    </choose>" +
            "      </foreach>\n" +
            "    </if>\n" +
            "    <if test=\"obj.sort != null and obj.sort != '' \">\n" +
            "        order by ${obj.sort} \n" +
            "    </if>" +
            "</script>")
    List<JSONObject> list(@Param("obj") JSONObject param);

    @Select("<script>" +
            "select t.* from (${obj.mainSql})t where 1=1 \n" +
            "   <if test=\"obj.param != null and obj.param.size() > 0\">\n" +
            "       <foreach collection=\"obj.param\" index=\"key\" item=\"item\">\n" +
            "    <choose>" +
            "    <when test=\"item.type == 'input' and item.con == 'like'\">\n" +
            "          and ${item.field} ${item.con}  CONCAT('%',#{item.value},'%')\n" +
            "    </when>" +
            "    <when test=\"item.type == 'input' and item.con != 'like'\">\n" +
            "          and ${item.field} ${item.con} #{item.value}\n" +
            "    </when>" +
            "    <when test=\"item.type == 'select'\">\n" +
            "          and ${item.field} = #{item.value}\n" +
            "    </when>" +
            "    <when test=\"item.type == 'date'\">\n" +
            "          and ${item.field} ${item.con} STR_TO_DATE(#{item.value}, '%Y-%m-%d')  \n" +
            "    </when>" +
            "    <when test=\"item.type == 'datetime'\">\n" +
            "          and ${item.field} ${item.con} STR_TO_DATE(#{item.value}, '%Y-%m-%d %H:%i:%s')  \n" +
            "    </when>" +
            "    <otherwise>\n" +
            "          and ${item.field} ${item.con} #{item.value}\n" +
            "    </otherwise>" +
            "    </choose>" +
            "      </foreach>\n" +
            "    </if>\n" +
            "    <if test=\"obj.sort != null and obj.sort != '' \">\n" +
            "        order by ${obj.sort} \n" +
            "    </if>" +
            "    LIMIT 1" +
            "</script>")
    JSONObject getOne(@Param("obj") JSONObject param);
}
