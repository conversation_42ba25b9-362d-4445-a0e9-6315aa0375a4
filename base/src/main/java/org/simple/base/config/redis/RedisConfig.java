package org.simple.base.config.redis;

 import com.alibaba.fastjson2.support.spring6.data.redis.FastJsonRedisSerializer;
 import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * RedisConfig
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2022/12/14
 */
@Configuration
public class RedisConfig {


    @Bean
    public RedisTemplate<String,?> redisTemplate(RedisConnectionFactory connectionFactory) {
        // 创建redisTemplate
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);
        // key采用String的序列化方式
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        // value序列化方式采用fastJson
        redisTemplate.setValueSerializer(new FastJsonRedisSerializer<Object>(Object.class));
        // hash的key也采用String的序列化方式
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        // hash的value序列化方式采用fastJson
        redisTemplate.setHashValueSerializer(new FastJsonRedisSerializer<Object>(Object.class));
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }
}
