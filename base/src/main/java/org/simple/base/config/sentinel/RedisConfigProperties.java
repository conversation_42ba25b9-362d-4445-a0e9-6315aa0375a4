package org.simple.base.config.sentinel;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class RedisConfigProperties {


    public static String HOST;


    public static int PORT;

    public static String PASSWORD;

    public static int DATABASE;


    @Value("${spring.data.redis.host}")
    public void setHost(String host) {
        HOST = host;
    }

    @Value("${spring.data.redis.port}")
    public void setPort(int port) {
        PORT = port;
    }

    @Value("${spring.data.redis.password:未定义}")
    public void setPassword(String password) {
        PASSWORD = password;
    }


    @Value("${spring.data.redis.database}")
    public void setDatabase(int database) {
        DATABASE = database;
    }

}
