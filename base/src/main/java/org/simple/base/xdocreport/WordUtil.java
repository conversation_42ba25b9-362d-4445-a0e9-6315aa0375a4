package org.simple.base.xdocreport;

import fr.opensagres.xdocreport.core.XDocReportException;
import fr.opensagres.xdocreport.document.IXDocReport;
import fr.opensagres.xdocreport.document.registry.XDocReportRegistry;
import fr.opensagres.xdocreport.template.IContext;
import fr.opensagres.xdocreport.template.TemplateEngineKind;

import java.io.IOException;
import java.io.InputStream;

public class WordUtil {

    public static ExportData getExportData(InputStream inputStream) throws IOException, XDocReportException {
        IXDocReport report =
                XDocReportRegistry.getRegistry().loadReport(inputStream, TemplateEngineKind.Freemarker);
        IContext context = report.createContext();
        return new ExportData(report, context);
    }

}
