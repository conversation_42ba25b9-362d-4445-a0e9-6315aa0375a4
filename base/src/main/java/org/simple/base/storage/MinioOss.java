package org.simple.base.storage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import io.minio.*;
import io.minio.errors.*;
import io.minio.messages.Item;
import org.simple.base.constant.RedisConstant;
import org.simple.base.dto.FileDto;
import org.simple.base.dto.OssDto;
import org.simple.base.vo.FrResult;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MinioOss {
    private static MinioOss minioOss = null;
    private static OssDto ossDto;

    private RedisTemplate<String, Object> redisTemplate;

    private MinioOss() {
    }

    public static MinioOss getInstance(RedisTemplate<String, Object> template) {
        if (null == minioOss) {
            minioOss = new MinioOss();
        }
        //设置配置对象
        ossDto = BeanUtil.fillBeanWithMap(
                template.opsForHash().entries(RedisConstant.MINIO_PIX), new OssDto(),
                false);
        return minioOss;
    }

    private MinioClient getMinioClient() {
        return MinioClient.builder()
                .endpoint(ossDto.getEndpoint())
                .credentials(ossDto.getAccessKeyId(), ossDto.getAccessKeySecret()).build();
    }


    public FrResult<String> fileUpload(MultipartFile file, String fileName, boolean isPrivate)
            throws ServerException, InsufficientDataException, ErrorResponseException,
            IOException, NoSuchAlgorithmException, InvalidKeyException,
            InvalidResponseException, XmlParserException, InternalException {
        MinioClient minioClient = getMinioClient();
        //String fileName = file.getOriginalFilename();
        String path = getFilePath();
        PutObjectArgs args = PutObjectArgs.builder()
                .bucket(ossDto.getWorkspace())
                .object(path + "/" + fileName)
                .stream(file.getInputStream(), file.getSize(), -1)
                .contentType(file.getContentType())
                .build();
        minioClient.putObject(args);
        return FrResult.success(path);
    }


    /**
     * 获取私有文件授权链接
     *
     * @param filepath : 路径
     */
//    public String downLoadLink(String filepath, Integer expir) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
//        MinioClient minioClient = getMinioClient();
//        return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
//                .method(Method.GET)
//                .bucket(ossDto.getWorkspace())
//                .object(filepath)
//                .expiry(expir, TimeUnit.SECONDS)
//                .build());
//    }


    /**
     * 下载文件，返回输入流
     *
     * @param filepath ： 路径
     */
    public void downLoad(String fileName,String filepath, OutputStream out) throws IOException,
            ServerException, InsufficientDataException, ErrorResponseException,
            NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException,
            XmlParserException, InternalException {
        MinioClient minioClient = getMinioClient();
        InputStream stream = minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(ossDto.getWorkspace())
                        .object(filepath+"/"+fileName)
                        .build());
        IoUtil.copy(stream, out);
     }

    /**
     * 删除文件
     *
     * @param filepath ： 路径
     */
    public boolean remove(String fileName,String filepath) throws IOException,
            ServerException, InsufficientDataException, ErrorResponseException,
            NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException,
            XmlParserException, InternalException {
        MinioClient minioClient = getMinioClient();
        minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(ossDto.getWorkspace())
                        .object(filepath+"/"+fileName)
                        .build()
        );
        return true;
    }


    /**
     * 查询文件列表
     */
    public FileDto listFiles(Integer size, String marker, String prefix) {
        MinioClient minioClient = getMinioClient();
        Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                        .bucket(ossDto.getWorkspace())
                        .startAfter(marker)
                        .prefix(prefix)
                        .maxKeys(size)
                        .build());
        List<org.simple.base.dto.File> listfile = new ArrayList<>();
        results.forEach(e -> {
            try {
                String childPath = e.get().objectName();
                org.simple.base.dto.File f = new org.simple.base.dto.File();
                f.setKey(childPath);
                f.setSize(e.get().size());
                if (!e.get().isDir()) {
                    f.setUpdateDate(Date.from(e.get().lastModified().toInstant()));
                }
                listfile.add(f);
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        });
        FileDto fIleDto = new FileDto();
        fIleDto.setFileList(listfile);
        return fIleDto;
    }

    /**
     * 凭借文件存储路径
     */
    private static String getFilePath() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("HHmm");
        Date date = new Date();
        return sdf.format(date) + "/" + sdf1.format(date);
    }
}
