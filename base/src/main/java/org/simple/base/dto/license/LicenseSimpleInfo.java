package org.simple.base.dto.license;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class LicenseSimpleInfo {

    @Schema(description = "项目编号")
    private String subject;
    @Schema(description = "有效开始时间")
    private String startDateTime;
    @Schema(description = "失效时间")
    private String endDateTime;
    @Schema(description = "授权发布组织")
    private String info;
    @Schema(description = "其它个性化内容")
    private LicenseExtParam licenseExtParam;
    @Schema(description = "是否过期")
    private boolean exp;

}
