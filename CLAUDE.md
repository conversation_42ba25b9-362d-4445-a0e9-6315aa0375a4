# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack enterprise management system called "frSimpleBoot Pro" consisting of:
- **Backend**: Java Spring Boot 3.x multi-module Maven project (requires JDK 17)
- **Frontend**: Vue 3 + TypeScript + TDesign UI components + Vite

## Architecture

### Backend Modules (`/admin/`)
- **center**: Main application module (system management, users, roles, menus, etc.)
- **auth**: Authentication and authorization module (OAuth2, Sa-Token)
- **base**: Common base classes, utilities, and configurations
- **aigc**: AI/ML functionality with LangChain4J integration
- **flow**: Workflow management (Flowable integration)
- **lowcode**: Low-code platform functionality
- **job**: Distributed job scheduling (XXL-Job)
- **license**: License management
- **open**: Open API functionality

### Frontend Structure (`/ui/`)
- **src/pages**: Feature modules (system, flow, lowcode, aigc, dashboard)
- **src/components**: Reusable UI components
- **src/api**: HTTP API clients organized by module
- **src/store**: Pinia state management
- **src/router**: Vue Router configuration

## Development Commands

### Backend (Java)
```bash
# Build all modules
mvn clean install

# Run center application
cd admin/center
mvn spring-boot:run

# Run tests
mvn test

# Package
mvn clean package
```

### Frontend (Vue)
```bash
cd ui

# Development
npm run dev          # Start dev server
npm run dev:linux    # Linux development mode

# Build
npm run build        # Production build
npm run build:test   # Test environment build

# Code quality
npm run lint         # ESLint check
npm run lint:fix     # ESLint fix
npm run stylelint    # Style check
npm run stylelint:fix # Style fix

# Preview
npm run preview      # Preview build
```

## Key Technologies

### Backend Stack
- Spring Boot 3.x with JDK 17
- MyBatis-Plus for database operations
- Sa-Token for authentication/authorization
- Redis for caching and session management
- Flowable for workflow management
- XXL-Job for distributed scheduling
- Sentinel for rate limiting
- LangChain4J for AI capabilities

### Frontend Stack
- Vue 3 with Composition API
- TypeScript for type safety
- TDesign UI component library
- Vite for build tooling
- Pinia for state management
- Axios for HTTP requests

## Database & Infrastructure
- MySQL database
- Redis for caching
- RabbitMQ for message queuing
- MinIO/Aliyun OSS for file storage
- Multi-tenant architecture support

## Important Notes
- Main application entry point: `admin/center/src/main/java/org/simple/center/CenterApp.java`
- Frontend dev server runs on default Vite port
- Backend uses Spring Boot default port (8080)
- All modules follow standard Maven directory structure
- Frontend follows Vue 3 + TypeScript best practices