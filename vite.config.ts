import { ConfigEnv, UserConfig, loadEnv } from 'vite';
import compressPlugin from 'vite-plugin-compression';
import { viteMockServe } from 'vite-plugin-mock';
import createVuePlugin from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { createHtmlPlugin } from 'vite-plugin-html';
import svgLoader from 'vite-svg-loader';
import UnoCSS from 'unocss/vite';
import path from 'path';
import proxy from './src/config/proxy';
import config from './src/config/style';

const CWD = process.cwd();

// https://vitejs.dev/config/
export default ({ mode }: ConfigEnv): UserConfig => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD);
  const TimeStamp = new Date().getTime();
  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    build: {
      outDir: 'dist',
      rollupOptions: {
        output: {
          entryFileNames: `assets/[name].${TimeStamp}.js`,
          chunkFileNames: `assets/[name].${TimeStamp}.js`,
          assetFileNames: `assets/[name].${TimeStamp}.[ext]`,
        },
      },
      minify: 'terser',
      terserOptions: {
        compress: {
          //生产环境时移除console
          drop_console: true,
          drop_debugger: true,
        },
      },
    },
    plugins: [
      createHtmlPlugin({
        inject: {
          data: {
            title: config.title,
          },
        },
      }),
      createVuePlugin(),
      vueJsx(),
      UnoCSS(),
      viteMockServe({
        mockPath: 'mock',
        enable: false,
      }),
      svgLoader(),
      compressPlugin({
        ext: '.gz',
        deleteOriginFile: false,
      }),
    ],

    server: {
      port: 3002,
      host: '0.0.0.0',
      proxy: {
        '/center': {
          target: proxy[mode].host,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/center/, 'center'),
        },
        '/lowcode': {
          target: proxy[mode].host,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/lowcode/, 'lowcode'),
        },
        '/flow': {
          target: proxy[mode].host,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/flow/, 'flow'),
        },
        '/license': {
          target: proxy[mode].host,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/license/, 'license'),
        },
        '/oauth2': {
          target: proxy[mode].host,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/oauth2/, 'oauth2'),
        },
        '/auth': {
          target: proxy[mode].host,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/auth/, 'auth'),
        },
        '/openapi': {
          target: proxy[mode].host,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/openapi/, 'openapi'),
        },
        '/aigc': {
          target: proxy[mode].host,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/aigc/, 'aigc'),
        },
      },
    },
  };
};
