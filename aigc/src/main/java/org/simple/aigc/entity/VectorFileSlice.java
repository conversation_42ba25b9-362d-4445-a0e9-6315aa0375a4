package org.simple.aigc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * @Copyright: frSimple
 * @Desc: 文档分段信息实体
 * @Date: 2025-03-07 16:16:59
 * @Author: frSimple
 */

@Data
@NoArgsConstructor
@TableName(value = "ai_vector_file_slice")
@Schema(description = "文档分段信息")
public class VectorFileSlice implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "向量Id")
    private String id;
    @Schema(description = "关联文档ID")
    private String knowFileId;
    @Schema(description = "内容")
    private String content;
    @Schema(description = "状态")
    private String status;
    @Schema(description = "vector_id")
    private String vectorId;
    @Schema(description = "关联知识库Id")
    private String knowId;
}
