package org.simple.aigc.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.simple.base.dto.BaseEntity;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * @Copyright: frSimple
 * @Desc: 知识库关联附件实体
 * @Date: 2025-03-06 16:53:01
 * @Author: frSimple
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "ai_vector_file")
@Schema(description = "知识库关联附件")
public class VectorFile extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    private String id;
    @Schema(description = "标题")
    @TableField(condition = SqlCondition.LIKE)
    private String title;
    @Schema(description = "文本内容")
    private String content;
    @Schema(description = "切片数量")
    private String sliceNum;
    @Schema(description = "data_source")
    private String dataSource;
    @Schema(description = "status")
    private String status;
    @Schema(description = "关联知识库")
    private String knowId;
    @Schema(description = "附件名称")
    private String fileName;
    @Schema(description = "附件大小(B)")
    private BigDecimal fileSize;
    @Schema(description = "附件路径")
    private String filePath;
    @Schema(description = "训练失败原因")
    private String remark;
}
