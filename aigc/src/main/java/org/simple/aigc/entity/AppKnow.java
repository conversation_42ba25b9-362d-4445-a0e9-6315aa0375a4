package org.simple.aigc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;



/**
 * @Copyright: frSimple
 * @Desc: 应用和知识库关联关系实体
 * @Date: 2025-03-06 21:20:45
 * @Author: frSimple
 */

@Data
@NoArgsConstructor
@TableName(value = "ai_app_know")
@Schema(description ="应用和知识库关联关系")
public class AppKnow implements Serializable{
    @Serial
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description ="id")
    private String id;
    @Schema(description ="app_id")
    private String appId;
    @Schema(description ="know_id")
    private String knowId;
}
