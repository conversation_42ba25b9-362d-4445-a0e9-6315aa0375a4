package org.simple.aigc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.simple.base.dto.BaseEntity;

import java.io.Serial;
import java.io.Serializable;


/**
 * @Copyright: frSimple
 * @Desc: ai应用信息实体
 * @Date: 2025-03-06 16:52:56
 * @Author: frSimple
 */

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@TableName(value = "ai_app")
@Schema(description = "ai应用信息")
public class AiApp extends BaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    private String id;
    @Schema(description = "应用名称")
    private String name;
    @Schema(description = "关联模型")
    private String modelId;
    @Schema(description = "AI昵称")
    private String nickName;
}
