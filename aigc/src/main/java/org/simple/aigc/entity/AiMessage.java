package org.simple.aigc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * @Copyright: frSimple
 * @Desc: 对话消息实体
 * @Date: 2025-03-11 09:45:38
 * @Author: frSimple
 */

@Data
@NoArgsConstructor
@TableName(value = "ai_message")
@Schema(description = "对话消息")
public class AiMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    private String id;
    @Schema(description = "对话ID")
    private String chatId;
    @Schema(description = "tokens")
    private String tokens;
    @Schema(description = "角色")
        private String role;
    @Schema(description = "关联应用ID")
    private String appId;
    @Schema(description = "内容")
    private String message;
    @Schema(description = "推理内容")
    private String reasoning;
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "创建人id")
    private String creator;
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}
