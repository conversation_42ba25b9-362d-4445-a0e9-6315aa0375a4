package org.simple.aigc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.simple.aigc.entity.KnowLib;

/**
 * @Copyright: frSimple
 * @Date: 2025-03-06 16:52:58
 * @Author: frSimple
 */

@Mapper
public interface KnowLibMapper
        extends BaseMapper<KnowLib> {

    @Select("select GROUP_CONCAT(t.`name`) from ai_know_lib t , ai_app_know t1  \n" +
            "where t.id = t1.know_id  and t1.app_id = #{appId} \n" +
            "group by t.`name` ")
    String getKnowNames(@Param("appId") String appId);

    @Select("select GROUP_CONCAT(t.`id`) from ai_know_lib t , ai_app_know t1  \n" +
            "where t.id = t1.know_id  and t1.app_id = #{appId} \n" +
            "group by t.`id` ")
    String getKnowIds(@Param("appId") String appId);

    @Select("select count(1) from ai_vector_file t where t.know_id  = #{knowId}")
    Long queryDocNum(@Param("knowId") String knowId);

}
