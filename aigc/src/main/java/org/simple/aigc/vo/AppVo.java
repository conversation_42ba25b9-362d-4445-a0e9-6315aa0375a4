package org.simple.aigc.vo;

import dev.langchain4j.data.message.ChatMessage;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class AppVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String id;
    private String name;
    private String modelId;
    private String knowIds;
    private String embeddingModelId;
    private String nickName;
    private ModelVo model;
    private List<ChatMessage> messages;
}
