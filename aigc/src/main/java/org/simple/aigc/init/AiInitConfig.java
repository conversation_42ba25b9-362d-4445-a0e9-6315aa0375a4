package org.simple.aigc.init;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import org.simple.aigc.constant.AiGcConstant;
import org.simple.aigc.entity.AiApp;
import org.simple.aigc.entity.AiModel;
import org.simple.aigc.entity.AppKnow;
import org.simple.aigc.service.AiAppService;
import org.simple.aigc.service.AiModelService;
import org.simple.aigc.service.AppKnowService;
import org.simple.aigc.service.KnowLibService;
import org.simple.aigc.util.AppUtil;
import org.simple.aigc.util.ModelUtil;
import org.simple.aigc.vo.AppVo;
import org.simple.aigc.vo.ModelVo;
import org.simple.base.util.ObjectMapperUtil;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Order(1)
public class AiInitConfig implements ApplicationRunner {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private AiModelService aiModelService;

    @Resource
    private AiAppService aiAppService;

    @Resource
    private AppKnowService appKnowService;

    @Resource
    private KnowLibService knowLibService;

    private final static ObjectMapper objectMapper =
            ObjectMapperUtil.objectMapper();

    @Override
    public void run(ApplicationArguments args) throws Exception {
        loadAiModel();
        loadAiApp();
    }


    private void loadAiApp() {
        //开始初始化系统参数
        List<AiApp> list = aiAppService.list();
        List<String> newKeys = new ArrayList<>();
        list.forEach(obj -> {
            try {
                AppVo appVo = new AppVo();
                BeanUtil.copyProperties(obj, appVo);
                AppKnow appKnow = new AppKnow();
                appKnow.setAppId(appVo.getId());
                List<AppKnow> appKnowList = appKnowService.list(Wrappers.query(appKnow));
                if (!appKnowList.isEmpty()) {
                    List<String> ids = new ArrayList<>();
                    List<String> embeddingModelIds = new ArrayList<>();
                    appKnowList.forEach(obj2 -> {
                        ids.add(obj2.getKnowId());
                        embeddingModelIds.add(knowLibService.getById(obj2.getKnowId()).getModelId());
                    });
                    appVo.setKnowIds(CollUtil.join(ids, ","));
                    appVo.setEmbeddingModelId(CollUtil.join(embeddingModelIds, ","));
                }
                redisTemplate.opsForValue().set(AiGcConstant.AI_APP_CONFIG +
                        obj.getId(), objectMapper.writeValueAsString(appVo));
                newKeys.add(AiGcConstant.AI_APP_CONFIG +
                        obj.getId());
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });
        //删除缓存中已经不存在的字典
        AppUtil.delNotUsedKey(newKeys);
    }

    private void loadAiModel() {
        //开始初始化系统参数
        List<AiModel> list = aiModelService.list();
        List<String> newKeys = new ArrayList<>();
        list.forEach(obj -> {
            try {
                ModelVo modelVo = new ModelVo();
                BeanUtil.copyProperties(obj, modelVo);
                redisTemplate.opsForValue().set(AiGcConstant.AI_MODEL_CONFIG +
                        obj.getId(), objectMapper.writeValueAsString(modelVo));
                newKeys.add(AiGcConstant.AI_MODEL_CONFIG +
                        obj.getId());
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });

        //删除缓存中已经不存在的字典
        ModelUtil.delNotUsedKey(newKeys);
    }
}
