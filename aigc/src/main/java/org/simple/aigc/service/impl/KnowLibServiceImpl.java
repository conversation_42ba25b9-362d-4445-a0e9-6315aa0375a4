package org.simple.aigc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.simple.aigc.entity.KnowLib;
import org.simple.aigc.mapper.KnowLibMapper;
import org.simple.aigc.service.KnowLibService;
import org.springframework.stereotype.Service;

/**
 * @Copyright: frSimple
 * @Date: 2025-03-06 16:52:58
 * @Author: frSimple
 */


@Service
public class KnowLibServiceImpl
        extends ServiceImpl<KnowLibMapper, KnowLib>
        implements KnowLibService {

    @Override
    public String getKnowNames(String appId) {
        return baseMapper.getKnowNames(appId);
    }

    @Override
    public String getKnowIds(String appId) {
        return baseMapper.getKnowIds(appId);
    }

    @Override
    public Long queryDocNum(String knowId) {
        return baseMapper.queryDocNum(knowId);
    }
}