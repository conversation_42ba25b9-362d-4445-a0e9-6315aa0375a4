package org.simple.aigc.service.impl;

import cn.hutool.core.util.StrUtil;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentParser;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.parser.TextDocumentParser;
import dev.langchain4j.data.document.parser.apache.pdfbox.ApachePdfBoxDocumentParser;
import dev.langchain4j.data.document.parser.apache.poi.ApachePoiDocumentParser;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingStore;
import org.simple.aigc.constant.AiGcConstant;
import org.simple.aigc.entity.VectorFileSlice;
import org.simple.aigc.service.EmbeddingService;
import org.simple.aigc.util.ModelUtil;
import org.simple.aigc.util.VectorStoreUtil;
import org.simple.aigc.vo.VectorFileVo;
import org.simple.base.storage.OssUtil;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class EmbeddingServiceImpl implements EmbeddingService {

    @Override
    public List<VectorFileSlice> save(VectorFileVo param) {
        try {
            Document document = null;
            if (StrUtil.isNotEmpty(param.getFileName())) {
                DocumentParser parser = getDocumentParser(param);
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                OssUtil.downLoad(param.getFilePath(), param.getFileId(),
                        out);
                ByteArrayInputStream swapStream = new ByteArrayInputStream(out.toByteArray());
                document = parser.parse(swapStream);
            } else {
                document = Document.from(param.getContent());
            }
            document.metadata().put(AiGcConstant.AI_KNOW_ID, param.getKnowId())
                    .put(AiGcConstant.AI_KNOW_FILE_ID, param.getFileId());
            EmbeddingModel embeddingModel = ModelUtil.getOllamaEmbeddingModel(param.getModelId());
            EmbeddingStore<TextSegment> embeddingStore = VectorStoreUtil.getEmbeddingStore();
            List<VectorFileSlice> list = new ArrayList<>();
            DocumentSplitter splitter =
                    DocumentSplitters.recursive(300, 20);
            List<TextSegment> segments = splitter.split(document);
            List<Embedding> embeddings = embeddingModel.embedAll(segments).content();
            List<String> ids = embeddingStore.addAll(embeddings, segments);
            for (int i = 0; i < ids.size(); i++) {
                VectorFileSlice slice = new VectorFileSlice();
                slice.setId(ids.get(i));
                slice.setKnowFileId(param.getFileId());
                slice.setKnowId(param.getKnowId());
                slice.setContent(segments.get(i).text());
                slice.setStatus("00");
                list.add(slice);
            }
            return list;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private DocumentParser getDocumentParser(VectorFileVo param) {
        DocumentParser parser = null;
        if (param.getFileName().toLowerCase().lastIndexOf(AiGcConstant.DOC_SUFFIX) >= 0 ||
                param.getFileName().toLowerCase().lastIndexOf(AiGcConstant.DOCX_SUFFIX) >= 0) {
            parser = new ApachePoiDocumentParser();
        } else if (param.getFileName().toLowerCase().lastIndexOf(AiGcConstant.MD_SUFFIX) >= 0 ||
                param.getFileName().toLowerCase().lastIndexOf(AiGcConstant.TXT_SUFFIX) >= 0) {
            parser = new TextDocumentParser();
        } else if (param.getFileName().toLowerCase().lastIndexOf(AiGcConstant.PDF_SUFFIX) >= 0) {
            parser = new ApachePdfBoxDocumentParser();
        } else if (param.getFileName().toLowerCase().lastIndexOf(AiGcConstant.XLS_SUFFIX) >= 0 ||
                param.getFileName().toLowerCase().lastIndexOf(AiGcConstant.XLSX_SUFFIX) >= 0) {
            parser = new ApachePoiDocumentParser();
        }
        return parser;
    }

    @Override
    public void delete(List<String> vectorIds) {
        if(!vectorIds.isEmpty()){
            EmbeddingStore<TextSegment> embeddingStore = VectorStoreUtil.getEmbeddingStore();
            embeddingStore.removeAll(vectorIds);
        }
    }

}
