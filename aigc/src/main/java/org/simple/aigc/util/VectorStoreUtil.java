package org.simple.aigc.util;

import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.pgvector.PgVectorEmbeddingStore;
import org.simple.aigc.config.PgVectorProperties;

public class VectorStoreUtil {

    static EmbeddingStore<TextSegment> getPgVectorEmbeddingStore() {
        PgVectorEmbeddingStore.PgVectorEmbeddingStoreBuilder builder =
                PgVectorEmbeddingStore.builder()
                        .host(PgVectorProperties.HOST)
                        .port(PgVectorProperties.PORT)
                        .database(PgVectorProperties.DATABASE)
                        .dimension(768)
                        .user(PgVectorProperties.USERNAME)
                        .password(PgVectorProperties.PASSWORD)
                        .table(PgVectorProperties.TABLE_NAME)
                        .indexListSize(1)
                        .useIndex(true)
                        .createTable(true)
                        .dropTableFirst(false);
        return builder.build();
    }

    public static EmbeddingStore<TextSegment> getEmbeddingStore() {
        return getPgVectorEmbeddingStore();
    }
}
