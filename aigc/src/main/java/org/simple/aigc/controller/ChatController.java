package org.simple.aigc.controller;

import jakarta.annotation.Resource;
import org.simple.aigc.param.ChatParam;
import org.simple.aigc.service.AiAppService;
import org.simple.base.util.AuthUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.concurrent.Executor;

@RestController
@RequestMapping("/aigc/chat")
public class ChatController {

    @Resource
    private Executor threadExecutor;

    @Resource
    private AiAppService aiAppService;


    @GetMapping("message")
    public SseEmitter chat(ChatParam chatParam) {
        try{
            SseEmitter emitter = new SseEmitter();
            chatParam.setUserId(AuthUtil.getUserId());
            chatParam.setEmitter(emitter);
            chatParam.setRequestAttributes((ServletRequestAttributes)
                    RequestContextHolder.getRequestAttributes());
            threadExecutor.execute(() -> {
                aiAppService.chat(chatParam);
            });
            return emitter;
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }
}
