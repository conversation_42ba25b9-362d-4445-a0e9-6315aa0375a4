<div class="row" style="margin-left: 1px; margin-top:10px; height: 50px;">
  <div class="col-md-6" style="margin-bottom: 10px;">
    <span style="font-size: 30px;font-weight: bold;">{{app}}</span>
  </div>
  <div class="col-md-6">
    <button class="btn btn-default-inverse" style="float: right; margin-right: 10px;" ng-disabled="!macInputModel" ng-click="addNewRule()">
      <i class="fa fa-plus"></i>&nbsp;&nbsp;新增流控规则</button>
    <!--<a class="btn btn-outline-success" style="float: right; margin-right: 10px;" ui-sref="dashboard.flow({app: app})">-->
      <!--回到集群页面</a>-->
  </div>
</div>

<div class="separator"></div>

<div class="container-fluid">
  <div class="row" style="margin-top: 20px; margin-bottom: 20px;">
    <div class="col-md-12">
      <div class="card">
        <div class="inputs-header">
          <span class="brand" style="font-size: 13px;">流控规则</span>
          <!--<button class="btn btn-danger" style="float: right;margin-right: 10px;height: 30px;font-size: 12px;" ng-click="disableAll()">全部禁用</button>-->
          <button class="btn btn-primary" style="float: right; margin-right: 10px; height: 30px;font-size: 12px;" ng-click="getMachineRules()">刷新</button>
          <input class="form-control witdh-200" placeholder="关键字" ng-model="searchKey">
          <div class="control-group" style="float:right;margin-right: 10px;margin-bottom: -10px;">
            <selectize id="gsInput" class="selectize-input-200" config="macsInputConfig" options="macsInputOptions" ng-model="macInputModel"
              placeholder="机器"></selectize>
          </div>
        </div>

        <!--.tools-header -->
        <div class="card-body" style="padding: 0px 0px;">
          <table class="table" style="border-left: none; border-right:none;margin-top: 10px;">
            <thead>
              <tr style="background: #F3F5F7;">
                <td style="width: 40%">
                  资源名
                </td>
                <td style="width: 8%;">
                  来源应用
                </td>
                <td style="width: 8%;">
                  流控模式
                </td>
                <td style="width: 8%;">
                  阈值类型
                </td>
                <td style="width: 6%;">
                  阈值
                </td>
                <td style="width: 8%;">
                  阈值模式
                </td>
                <td style="width: 10%;">
                  流控效果
                </td>
                <td style="width: 12%;">
                  操作
                </td>
              </tr>
            </thead>
            <tbody>
              <tr dir-paginate="rule in rules | filter: searchKey | itemsPerPage: rulesPageConfig.pageSize " current-page="rulesPageConfig.currentPageIndex"
                pagination-id="entriesPagination">
                <td style="word-wrap:break-word;word-break:break-all;">{{rule.resource}}</td>
                <td style="word-wrap:break-word;word-break:break-all;">{{rule.limitApp }}</td>
                <td>
                  <span ng-if="rule.strategy == 0">直接</span>
                  <span ng-if="rule.strategy == 1">关联</span>
                  <span ng-if="rule.strategy == 2">链路</span>
                </td>
                <td>
                  {{rule.grade==0 ? '线程数' : 'QPS'}}
                </td>
                <td style="word-wrap:break-word;word-break:break-all;">
                  {{rule.count}}
                </td>
                <td>
                  <span>{{generateThresholdTypeShow(rule)}}</span>
                </td>
                <td>
                  <span ng-if="rule.controlBehavior == 0">快速失败</span>
                  <span ng-if="rule.controlBehavior == 1">Warm Up</span>
                  <span ng-if="rule.controlBehavior == 2">排队等待</span>
                  <span ng-if="rule.controlBehavior == 3">预热排队</span>
                </td>
                <td>
                  <button class="btn btn-xs btn-default" type="button" ng-click="editRule(rule)" style="font-size: 12px; height:25px;">编辑</button>
                  <button class="btn btn-xs btn-default" type="button" ng-click="deleteRule(rule)" style="font-size: 12px; height:25px;">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- .card-body -->
        <div class="pagination-footer">
          <dir-pagination-controls boundary-links="true" template-url="app/views/pagination.tpl.html" pagination-id="entriesPagination"
            on-page-change="">
          </dir-pagination-controls>
          <div class="tools" style="">
            <span>共 {{rulesPageConfig.totalCount}} 条记录, </span>
            <span>
              每页
              <input class="form-control" ng-model="rulesPageConfig.pageSize"> 条记录
            </span>
            <!--<span>第 {{rulesPageConfig.currentPageIndex}} / {{rulesPageConfig.totalPage}} 页</span>-->
          </div>
          <!-- .tools -->
        </div>
        <!-- pagination-footer -->
      </div>
      <!-- .card -->
    </div>
    <!-- .col-md-12 -->
  </div>
  <!-- -->
</div>
<!-- .container-fluid -->
