<div>
    <span class="brand" style="font-weight:bold;">{{serverAssignDialogData.title}}</span>
    <div class="card" style="margin-top: 20px;margin-bottom: 10px;">
        <div class="panel-body">
            <div class="row">
                <form role="form" class="form-horizontal">
                    <div ng-if="serverAssignDialogData.type == 'edit'">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">Token Server</label>
                            <div class="col-sm-4">
                                <p class="form-control-static">{{serverAssignDialogData.serverData.currentServer}}</p>
                            </div>

                            <label class="col-sm-2 control-label">Server 端口</label>
                            <div class="col-sm-3">
                                <input type="number" min="1" max="65535" class="form-control highlight-border"
                                       ng-disabled="!serverAssignDialogData.serverData.belongToApp"
                                       ng-model='serverAssignDialogData.serverData.serverPort' placeholder='请输入 Token Server 端口'/>
                            </div>
                        </div>
                        <div class="form-group" ng-if="serverAssignDialogData.serverData.belongToApp">
                            <label class="col-sm-2 control-label"
                                   title="server 最大允许的总 QPS，注意 embedded 模式下不要设的太大">最大允许 QPS</label>
                            <div class="col-sm-3">
                                <input type="number" min="0" max="200000" class="form-control highlight-border"
                                       ng-model='serverAssignDialogData.serverData.maxAllowedQps' placeholder='请输入 server 最大允许 QPS'/>
                            </div>
                        </div>
                    </div>

                    <div ng-if="serverAssignDialogData.type == 'add'">
                        <div class="form-group" >
                            <label class="col-sm-2 control-label">机器类型</label>
                            <div class="col-sm-4">
                                <div class="form-control highlight-border" align="center">
                                    <input type="radio" name="strategy" value="0" checked ng-model='serverAssignDialogData.serverData.serverType' />&nbsp;应用内机器&nbsp;&nbsp;
                                    <input type="radio" name="strategy" value="1" ng-model='serverAssignDialogData.serverData.serverType' />&nbsp;外部指定机器
                                </div>
                            </div>

                            <div ng-if="serverAssignDialogData.serverData.serverType == 1">
                                <div class="col-sm-6">
                                    <p class="form-control-static text-primary" style="font-size: x-small;">若指定外部 server，请先在相应页面对外部 server 进行配置，然后在此页面指定。</p>
                                </div>
                            </div>
                        </div>

                        <div ng-if="serverAssignDialogData.serverData.serverType == 0">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">选择机器</label>
                                <div class="col-sm-4">
                                    <select ng-model="serverAssignDialogData.serverData.currentServer" ng-change="onCurrentServerChange()"
                                            ng-options="machineId for machineId in remainingMachineList"
                                            class="form-control"></select>
                                </div>

                                <label class="col-sm-2 control-label">Server 端口</label>
                                <div class="col-sm-3">
                                    <input type="number" min="1" max="65535" class="form-control highlight-border"
                                           ng-model='serverAssignDialogData.serverData.serverPort' placeholder='请输入 Token Server 端口号'/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label"
                                       title="server 最大允许的总 QPS，注意 embedded 模式下不要设的太大">最大允许 QPS</label>
                                <div class="col-sm-3">
                                    <input type="number" min="0" max="200000" class="form-control highlight-border"
                                           ng-model='serverAssignDialogData.serverData.maxAllowedQps' placeholder='请输入 server 最大允许 QPS'/>
                                </div>
                            </div>
                        </div>

                        <div ng-if="serverAssignDialogData.serverData.serverType == 1">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">Server IP</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control highlight-border"
                                           ng-model='serverAssignDialogData.serverData.currentServer' placeholder='请输入独立的 Token Server IP'/>
                                </div>

                                <label class="col-sm-2 control-label">Server 端口</label>
                                <div class="col-sm-3">
                                    <input type="number" min="1" max="65535" class="form-control highlight-border"
                                           ng-model='serverAssignDialogData.serverData.serverPort' placeholder='请输入 Token Server 端口号'/>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- assign form start -->
                <form role="form" class="form-inline" ng-if="serverAssignDialogData.serverData.currentServer"
                      style="margin-top: 30px; margin-left: 20px; text-align: center;">
                    <div>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <label class="control-label" style="width: 220px; text-align: center;">请从中选取 client：</label>
                                <div>
                                    <select size="8" multiple="multiple" ng-model="tmp.curRemainingClientChosen"
                                            ng-options="ip for ip in remainingMachineList | filter: notChosenServer"
                                            class="form-control" style="width: 100%;">
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-12">

                                <button type="button" class="btn btn-outline-primary"
                                        ng-click="moveToRemainingSharePool()">←
                                </button>
                                <button type="button" class="btn btn-outline-primary"
                                        ng-click="moveToServerGroup()">→
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-12">
                                <label class="control-label" style="width: 200px; text-align: center;">已选取的 client 列表</label>
                                <div>
                                    <select size="8" multiple="multiple" ng-model="tmp.curClientChosen"
                                            ng-options="ip for ip in serverAssignDialogData.serverData.clientSet"
                                            class="form-control" style="width: 100%;"></select>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="separator"></div>
            <div clss="row" style="margin-top: 20px;">
                <button class="btn btn-outline-danger" style="float:right; height: 30px;font-size: 12px;margin-left: 10px;" ng-click="serverAssignDialog.close()">取消</button>
                <button class="btn btn-outline-success" style="float:right; height: 30px;font-size: 12px;margin-left: 10px;" ng-click="saveAssignForDialog()">{{serverAssignDialogData.confirmBtnText}}</button>
            </div>
        </div>
    </div>
</div>
