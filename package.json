{"name": "frsimple-boot-pro", "version": "1.0.0", "type": "module", "scripts": {"dev:mock": "vite --open --mode mock", "dev": "vite --open --mode development", "dev:linux": "vite --mode development", "build:test": "vite build --mode test", "build": "vue-tsc --noEmit && vite build --mode release", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx ./ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx ./ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,vue,vss,sass,less}", "site:preview": "npm run build && cp -r dist _site", "test": "echo \"no test specified,work in process\"", "test:coverage": "echo \"no test:coverage specified,work in process\""}, "dependencies": {"@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.1.4", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/lang-xml": "^6.0.2", "@codemirror/theme-one-dark": "^6.1.0", "@logicflow/core": "1.2.9", "@logicflow/extension": "1.2.9", "@tdesign-vue-next/chat": "^0.2.3", "@vueuse/core": "^10.7.2", "@wangeditor/editor": "^5.1.0", "@wangeditor/editor-for-vue": "^5.1.11", "axios": "^1.6.7", "codemirror": "^6.0.1", "dayjs": "^1.11.10", "echarts": "5.4.3", "event-source-polyfill": "^1.0.31", "gm-crypto": "^0.1.12", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "~2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "qrcode.vue": "^3.4.1", "qs": "^6.11.2", "tdesign-icons-vue-next": "^0.3.4", "tdesign-vue-next": "~1.13.0", "tvision-color": "^1.6.0", "vue": "~3.3.4", "vue-clipboard3": "^2.0.0", "vue-codemirror": "^6.1.1", "vue-draggable-plus": "^0.5.4", "vue-router": "~4.3.0", "vue3-print-nb": "^0.1.4"}, "devDependencies": {"@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "@types/echarts": "^4.9.21", "@types/lodash": "^4.17.6", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/compiler-sfc": "~3.3.8", "@vue/eslint-config-typescript": "^12.0.0", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-vue": "^9.21.1", "eslint-plugin-vue-scoped-css": "^2.7.2", "less": "^4.2.0", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "postcss-html": "^1.6.0", "postcss-less": "^6.0.0", "prettier": "^3.2.5", "stylelint": "~16.2.1", "stylelint-config-standard": "^36.0.0", "stylelint-order": "~6.0.4", "typescript": "~5.4.3", "unocss": "^0.65.1", "vite": "^5.1.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-mock": "^3.0.1", "vite-svg-loader": "^5.1.0", "vue-tsc": "^1.8.27"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write", "npm run lint:fix"], "*.{html,vue,css,sass,less}": ["npm run stylelint:fix"]}, "engines": {"node": ">=18.0.0"}}